#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C++性能引擎编译脚本
自动检测编译器并编译动态库
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class EngineBuilder:
    """性能引擎编译器"""
    
    def __init__(self):
        self.source_dir = Path(__file__).parent
        self.build_dir = self.source_dir / "build"
        self.lib_dir = self.source_dir / "lib"
        
        # 创建目录
        self.build_dir.mkdir(exist_ok=True)
        self.lib_dir.mkdir(exist_ok=True)
        
        # 平台相关设置
        self.platform = platform.system().lower()
        self.setup_platform_config()
    
    def setup_platform_config(self):
        """设置平台相关配置"""
        if self.platform == "windows":
            self.lib_extension = ".dll"
            self.obj_extension = ".obj"
            self.preferred_compilers = ["cl", "g++", "clang++"]
        elif self.platform == "darwin":  # macOS
            self.lib_extension = ".dylib"
            self.obj_extension = ".o"
            self.preferred_compilers = ["clang++", "g++"]
        else:  # Linux
            self.lib_extension = ".so"
            self.obj_extension = ".o"
            self.preferred_compilers = ["g++", "clang++"]
        
        self.lib_name = f"performance_engine{self.lib_extension}"
    
    def find_compiler(self):
        """查找可用的编译器"""
        for compiler in self.preferred_compilers:
            try:
                result = subprocess.run([compiler, "--version"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ 找到编译器: {compiler}")
                    return compiler
            except FileNotFoundError:
                continue
        
        raise RuntimeError("❌ 未找到可用的C++编译器")
    
    def build_with_gcc_clang(self, compiler):
        """使用GCC或Clang编译"""
        source_file = self.source_dir / "performance_engine.cpp"
        output_file = self.lib_dir / self.lib_name
        
        # 编译命令
        cmd = [
            compiler,
            "-std=c++17",
            "-O3",  # 最高优化级别
            "-fPIC",  # 位置无关代码
            "-shared",  # 生成动态库
            "-pthread",  # 多线程支持
            str(source_file),
            "-o", str(output_file)
        ]
        
        # 平台特定选项
        if self.platform == "darwin":
            cmd.extend(["-undefined", "dynamic_lookup"])
        elif self.platform == "linux":
            cmd.append("-ldl")
        
        print(f"🔨 编译命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.source_dir)
            
            if result.returncode == 0:
                print(f"✅ 编译成功: {output_file}")
                return True
            else:
                print(f"❌ 编译失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 编译异常: {e}")
            return False
    
    def build_with_msvc(self):
        """使用MSVC编译"""
        source_file = self.source_dir / "performance_engine.cpp"
        output_file = self.lib_dir / self.lib_name
        
        # 查找Visual Studio
        vs_paths = [
            "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Auxiliary/Build/vcvars64.bat",
            "C:/Program Files/Microsoft Visual Studio/2019/Community/VC/Auxiliary/Build/vcvars64.bat",
            "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Auxiliary/Build/vcvars64.bat"
        ]
        
        vcvars_bat = None
        for path in vs_paths:
            if os.path.exists(path):
                vcvars_bat = path
                break
        
        if not vcvars_bat:
            print("❌ 未找到Visual Studio环境")
            return False
        
        # 编译命令
        compile_cmd = f'''
        call "{vcvars_bat}"
        cl /std:c++17 /O2 /LD /EHsc "{source_file}" /Fe:"{output_file}"
        '''
        
        try:
            result = subprocess.run(compile_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0 and output_file.exists():
                print(f"✅ MSVC编译成功: {output_file}")
                return True
            else:
                print(f"❌ MSVC编译失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ MSVC编译异常: {e}")
            return False
    
    def build_with_cmake(self):
        """使用CMake编译"""
        cmake_file = self.source_dir / "CMakeLists.txt"
        
        # 创建CMakeLists.txt
        cmake_content = f"""
cmake_minimum_required(VERSION 3.10)
project(PerformanceEngine)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 优化设置
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
set(CMAKE_BUILD_TYPE Release)

# 源文件
add_library(performance_engine SHARED performance_engine.cpp)

# 线程支持
find_package(Threads REQUIRED)
target_link_libraries(performance_engine Threads::Threads)

# 输出目录
set_target_properties(performance_engine PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${{CMAKE_CURRENT_SOURCE_DIR}}/lib"
    RUNTIME_OUTPUT_DIRECTORY "${{CMAKE_CURRENT_SOURCE_DIR}}/lib"
)

# Windows特定设置
if(WIN32)
    set_target_properties(performance_engine PROPERTIES
        WINDOWS_EXPORT_ALL_SYMBOLS TRUE
    )
endif()
"""
        
        with open(cmake_file, 'w') as f:
            f.write(cmake_content)
        
        try:
            # 配置
            config_cmd = ["cmake", "-B", str(self.build_dir), "-S", str(self.source_dir)]
            result = subprocess.run(config_cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ CMake配置失败: {result.stderr}")
                return False
            
            # 编译
            build_cmd = ["cmake", "--build", str(self.build_dir), "--config", "Release"]
            result = subprocess.run(build_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ CMake编译成功")
                return True
            else:
                print(f"❌ CMake编译失败: {result.stderr}")
                return False
                
        except FileNotFoundError:
            print("❌ 未找到CMake")
            return False
        except Exception as e:
            print(f"❌ CMake编译异常: {e}")
            return False
    
    def build(self):
        """执行编译"""
        print("🚀 开始编译C++性能引擎...")
        print(f"📁 源码目录: {self.source_dir}")
        print(f"🏗️ 构建目录: {self.build_dir}")
        print(f"📚 库目录: {self.lib_dir}")
        print(f"🖥️ 平台: {self.platform}")
        
        # 检查源文件
        source_file = self.source_dir / "performance_engine.cpp"
        header_file = self.source_dir / "performance_engine.hpp"
        
        if not source_file.exists():
            print(f"❌ 源文件不存在: {source_file}")
            return False
        
        if not header_file.exists():
            print(f"❌ 头文件不存在: {header_file}")
            return False
        
        # 尝试不同的编译方法
        build_methods = []
        
        # 优先尝试CMake
        build_methods.append(("CMake", self.build_with_cmake))
        
        # 然后尝试直接编译器
        try:
            compiler = self.find_compiler()
            if compiler == "cl":
                build_methods.append(("MSVC", self.build_with_msvc))
            else:
                build_methods.append((f"{compiler}", lambda: self.build_with_gcc_clang(compiler)))
        except RuntimeError as e:
            print(e)
        
        # 执行编译
        for method_name, method_func in build_methods:
            print(f"\n🔨 尝试使用 {method_name} 编译...")
            
            if method_func():
                # 验证输出文件
                output_file = self.lib_dir / self.lib_name
                if output_file.exists():
                    print(f"✅ 编译成功! 输出文件: {output_file}")
                    print(f"📊 文件大小: {output_file.stat().st_size / 1024:.1f} KB")
                    return True
                else:
                    print(f"❌ 输出文件不存在: {output_file}")
            
            print(f"❌ {method_name} 编译失败，尝试下一种方法...")
        
        print("❌ 所有编译方法都失败了")
        return False
    
    def test_library(self):
        """测试编译的库"""
        lib_file = self.lib_dir / self.lib_name
        
        if not lib_file.exists():
            print("❌ 库文件不存在，无法测试")
            return False
        
        try:
            # 尝试加载库
            import ctypes
            lib = ctypes.CDLL(str(lib_file))
            
            # 测试基本函数
            if hasattr(lib, 'create_engine'):
                print("✅ 库加载成功，函数可用")
                return True
            else:
                print("❌ 库加载成功，但函数不可用")
                return False
                
        except Exception as e:
            print(f"❌ 库测试失败: {e}")
            return False
    
    def clean(self):
        """清理构建文件"""
        import shutil
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"🗑️ 清理构建目录: {self.build_dir}")
        
        if self.lib_dir.exists():
            shutil.rmtree(self.lib_dir)
            print(f"🗑️ 清理库目录: {self.lib_dir}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="编译C++性能引擎")
    parser.add_argument("--clean", action="store_true", help="清理构建文件")
    parser.add_argument("--test", action="store_true", help="测试编译的库")
    
    args = parser.parse_args()
    
    builder = EngineBuilder()
    
    if args.clean:
        builder.clean()
        return
    
    if args.test:
        success = builder.test_library()
        sys.exit(0 if success else 1)
    
    # 执行编译
    success = builder.build()
    
    if success:
        print("\n🎉 编译完成!")
        print("📝 使用说明:")
        print("1. 在Python中导入: from core_engine.python_bindings import get_performance_engine")
        print("2. 获取引擎实例: engine = get_performance_engine()")
        print("3. 初始化数据: engine.initialize_data(your_data)")
        print("4. 使用高性能搜索和排序功能")
        
        # 自动测试
        if builder.test_library():
            print("✅ 库测试通过")
        else:
            print("❌ 库测试失败")
    else:
        print("\n❌ 编译失败!")
        print("💡 解决方案:")
        print("1. 安装C++编译器 (GCC, Clang, 或 Visual Studio)")
        print("2. 确保编译器在PATH环境变量中")
        print("3. 安装CMake (可选，但推荐)")
        sys.exit(1)

if __name__ == "__main__":
    main()
