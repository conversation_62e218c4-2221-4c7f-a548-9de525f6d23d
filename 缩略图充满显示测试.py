#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缩略图充满显示测试工具
测试修复后的缩略图是否能充满整个方块
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QSlider,
                               QGroupBox, QGridLayout, QScrollArea)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor

class ThumbnailFillTestWindow(QMainWindow):
    """缩略图充满显示测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🖼️ 缩略图充满显示测试")
        self.setGeometry(100, 100, 1000, 700)
        
        self.test_images = []
        self.thumbnail_widgets = []
        self.setup_ui()
        self.generate_test_images()
        self.create_thumbnail_widgets()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🖼️ 缩略图充满显示测试")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 控制面板
        control_group = QGroupBox("🎛️ 大小控制")
        control_layout = QHBoxLayout(control_group)
        
        control_layout.addWidget(QLabel("缩略图大小:"))
        
        self.size_slider = QSlider(Qt.Horizontal)
        self.size_slider.setRange(100, 400)
        self.size_slider.setValue(200)
        self.size_slider.valueChanged.connect(self.on_size_changed)
        control_layout.addWidget(self.size_slider)
        
        self.size_label = QLabel("200px")
        control_layout.addWidget(self.size_label)
        
        # 测试按钮
        test_btn = QPushButton("🧪 测试充满效果")
        test_btn.clicked.connect(self.test_fill_effect)
        control_layout.addWidget(test_btn)
        
        layout.addWidget(control_group)
        
        # 缩略图显示区域
        self.scroll_area = QScrollArea()
        self.thumbnail_container = QWidget()
        self.thumbnail_layout = QGridLayout(self.thumbnail_container)
        self.scroll_area.setWidget(self.thumbnail_container)
        self.scroll_area.setWidgetResizable(True)
        layout.addWidget(self.scroll_area)
        
        # 说明文字
        info_label = QLabel("""
📝 测试说明:
• 拖动滑块调整缩略图大小
• 观察图片是否充满整个方块
• 修复前: 图片小，周围有空白
• 修复后: 图片充满，可能会裁剪
        """)
        info_label.setStyleSheet("background-color: #f0f8ff; padding: 10px; border-radius: 5px;")
        layout.addWidget(info_label)
    
    def generate_test_images(self):
        """生成测试图片"""
        test_dir = Path("test_thumbnail_fill")
        test_dir.mkdir(exist_ok=True)
        
        # 生成不同宽高比的测试图片
        test_configs = [
            (800, 600, "4:3横图", QColor(255, 100, 100)),
            (600, 800, "3:4竖图", QColor(100, 255, 100)),
            (1920, 1080, "16:9横图", QColor(100, 100, 255)),
            (1080, 1920, "9:16竖图", QColor(255, 255, 100)),
            (500, 500, "1:1方图", QColor(255, 100, 255)),
            (2000, 800, "超宽图", QColor(100, 255, 255))
        ]
        
        for i, (width, height, name, color) in enumerate(test_configs):
            # 创建测试图片
            pixmap = QPixmap(width, height)
            pixmap.fill(color)
            
            # 绘制信息
            painter = QPainter(pixmap)
            painter.setPen(QColor(255, 255, 255))
            painter.setFont(QFont("Arial", max(20, min(width, height) // 20)))
            
            text = f"{name}\n{width}x{height}"
            painter.drawText(pixmap.rect(), Qt.AlignCenter, text)
            
            # 绘制边框
            painter.setPen(QColor(0, 0, 0))
            painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1))
            painter.end()
            
            # 保存文件
            file_path = test_dir / f"test_{i+1}_{width}x{height}.png"
            pixmap.save(str(file_path))
            self.test_images.append(str(file_path))
        
        print(f"✅ 已生成 {len(self.test_images)} 张测试图片")
    
    def create_thumbnail_widgets(self):
        """创建缩略图控件"""
        try:
            from ui.components.content_area import ThumbnailWidget
            from theme.theme_manager import ThemeManager
            from database.db_manager import DatabaseManager
            from utils.config_manager import ConfigManager
            
            # 创建管理器实例
            theme_manager = ThemeManager()
            db_manager = DatabaseManager()
            config_manager = ConfigManager()
            
            # 清理现有控件
            for widget in self.thumbnail_widgets:
                widget.deleteLater()
            self.thumbnail_widgets.clear()
            
            # 创建新的缩略图控件
            for i, image_path in enumerate(self.test_images):
                # 创建模拟的数据项
                item_data = {
                    'file_path': image_path,
                    'file_name': Path(image_path).name,
                    'file_type': 'image',
                    'file_size': Path(image_path).stat().st_size if Path(image_path).exists() else 0
                }
                
                # 创建缩略图控件
                thumbnail_widget = ThumbnailWidget(
                    item_data, theme_manager, db_manager, config_manager
                )
                
                # 设置初始大小
                thumbnail_widget.set_thumbnail_size(self.size_slider.value())
                
                # 添加到布局
                row = i // 3
                col = i % 3
                self.thumbnail_layout.addWidget(thumbnail_widget, row, col)
                
                self.thumbnail_widgets.append(thumbnail_widget)
            
            print(f"✅ 已创建 {len(self.thumbnail_widgets)} 个缩略图控件")
            
        except Exception as e:
            print(f"❌ 创建缩略图控件失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_size_changed(self, size):
        """尺寸变化"""
        self.size_label.setText(f"{size}px")
        
        # 更新所有缩略图控件的大小
        for widget in self.thumbnail_widgets:
            widget.set_thumbnail_size(size)
        
        print(f"📏 缩略图大小已调整为: {size}px")
    
    def test_fill_effect(self):
        """测试充满效果"""
        print("\n🧪 开始测试充满效果...")
        
        # 测试不同尺寸
        test_sizes = [150, 200, 250, 300, 350]
        
        for size in test_sizes:
            print(f"📏 测试尺寸: {size}px")
            
            # 设置尺寸
            self.size_slider.setValue(size)
            QApplication.processEvents()
            
            # 等待一下让缩放完成
            time.sleep(0.5)
            QApplication.processEvents()
            
            # 检查每个控件
            for i, widget in enumerate(self.thumbnail_widgets):
                if hasattr(widget, 'thumbnail_label'):
                    label = widget.thumbnail_label
                    pixmap = label.pixmap()
                    
                    if pixmap and not pixmap.isNull():
                        actual_size = pixmap.size()
                        expected_size = size
                        
                        # 检查是否充满
                        width_fill = actual_size.width() >= expected_size * 0.9  # 允许10%误差
                        height_fill = actual_size.height() >= expected_size * 0.9
                        
                        fill_status = "✅ 充满" if (width_fill or height_fill) else "❌ 未充满"
                        print(f"  图片{i+1}: {actual_size.width()}x{actual_size.height()} -> {fill_status}")
                    else:
                        print(f"  图片{i+1}: ❌ 无图片数据")
        
        print("🎉 充满效果测试完成!")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = ThumbnailFillTestWindow()
    window.show()
    
    print("缩略图充满显示测试工具启动成功！")
    print("功能特性：")
    print("1. 🖼️ 多种宽高比测试图片")
    print("2. 📏 实时大小调整测试")
    print("3. 🧪 自动充满效果验证")
    print("4. 📊 详细的测试结果分析")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
