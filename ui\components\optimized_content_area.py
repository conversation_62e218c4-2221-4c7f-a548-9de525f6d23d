#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的内容区域
集成高性能列表视图、快速搜索、智能排序等功能
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QComboBox, QSlider, QLineEdit,
                               QSplitter, QFrame, QButtonGroup, QCheckBox)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont
from typing import List, Dict, Any
import time

from .high_performance_list import HighPerformanceListView, PerformanceMonitorWidget, FastSearchEngine

class OptimizedContentAreaWidget(QWidget):
    """优化的内容区域控件"""

    # 信号定义
    item_selected = Signal(int, dict)
    items_selection_changed = Signal(int)
    files_dropped = Signal(list)

    def __init__(self, theme_manager, db_manager, config_manager, parent=None):
        super().__init__(parent)

        self.theme_manager = theme_manager
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 数据管理
        self.current_items = []
        self.filtered_items = []

        # 搜索引擎
        self.search_engine = FastSearchEngine()

        # 性能优化
        self.update_timer = QTimer()
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self._perform_update)

        self.setup_ui()
        self.setup_connections()

        # 加载测试数据
        self.load_test_data()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 创建工具栏
        self.create_toolbar(layout)

        # 创建主要内容区域
        self.create_main_content(layout)

        # 创建状态栏
        self.create_status_bar(layout)

    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setMaximumHeight(60)

        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 5, 10, 5)

        # 搜索框
        search_label = QLabel("🔍 搜索:")
        toolbar_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索...")
        self.search_input.setMaximumWidth(200)
        toolbar_layout.addWidget(self.search_input)

        # 排序选择
        sort_label = QLabel("📊 排序:")
        toolbar_layout.addWidget(sort_label)

        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["名称", "大小", "日期", "类型"])
        self.sort_combo.setMaximumWidth(100)
        toolbar_layout.addWidget(self.sort_combo)

        # 排序方向
        self.sort_desc_checkbox = QCheckBox("降序")
        toolbar_layout.addWidget(self.sort_desc_checkbox)

        # 文件类型过滤
        type_label = QLabel("📁 类型:")
        toolbar_layout.addWidget(type_label)

        self.type_filter_combo = QComboBox()
        self.type_filter_combo.addItems(["全部", "图片", "视频", "音频", "文档"])
        self.type_filter_combo.setMaximumWidth(80)
        toolbar_layout.addWidget(self.type_filter_combo)

        toolbar_layout.addStretch()

        parent_layout.addWidget(toolbar_frame)

    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 高性能列表视图
        self.list_view = HighPerformanceListView()
        splitter.addWidget(self.list_view)

        # 性能监控面板（可选）
        self.performance_monitor = PerformanceMonitorWidget(self.list_view)
        self.performance_monitor.setMaximumWidth(200)
        self.performance_monitor.setVisible(False)  # 默认隐藏
        splitter.addWidget(self.performance_monitor)

        # 设置分割器比例
        splitter.setSizes([800, 200])

        parent_layout.addWidget(splitter)

    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setMaximumHeight(30)

        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 2, 10, 2)

        # 项目统计
        self.items_count_label = QLabel("项目: 0")
        status_layout.addWidget(self.items_count_label)

        # 选中统计
        self.selected_count_label = QLabel("选中: 0")
        status_layout.addWidget(self.selected_count_label)

        status_layout.addStretch()

        # 性能开关
        self.performance_toggle = QPushButton("显示性能监控")
        self.performance_toggle.setCheckable(True)
        self.performance_toggle.setMaximumWidth(120)
        status_layout.addWidget(self.performance_toggle)

        # 加载状态
        self.loading_label = QLabel("")
        status_layout.addWidget(self.loading_label)

        parent_layout.addWidget(status_frame)

    def setup_connections(self):
        """设置信号连接"""
        # 搜索和过滤
        self.search_input.textChanged.connect(self._on_search_changed)
        self.sort_combo.currentTextChanged.connect(self._on_sort_changed)
        self.sort_desc_checkbox.toggled.connect(self._on_sort_changed)
        self.type_filter_combo.currentTextChanged.connect(self._on_filter_changed)



        # 列表视图
        self.list_view.item_activated.connect(self._on_item_activated)
        self.list_view.selection_changed_signal.connect(self._on_selection_changed)

        # 性能监控
        self.performance_toggle.toggled.connect(self._toggle_performance_monitor)

        # 数据加载
        self.list_view.virtual_model.data_loading.connect(self._on_data_loading)
        self.list_view.virtual_model.data_loaded.connect(self._on_data_loaded)

    def load_test_data(self):
        """加载测试数据"""
        # 生成大量测试数据
        test_data = []
        file_types = ['image', 'video', 'audio', 'document']

        for i in range(10000):  # 10000个测试项目
            item = {
                'id': i,
                'name': f'测试文件_{i:05d}.jpg',
                'file_type': file_types[i % len(file_types)],
                'file_path': f'/test/path/file_{i}.jpg',
                'size': 1024 * (i % 1000 + 1),
                'created_time': f'2024-01-{(i % 30) + 1:02d}',
                'width': 1920,
                'height': 1080,
                'rating': i % 5 + 1
            }
            test_data.append(item)

        print(f"生成了 {len(test_data)} 个测试项目")
        self.set_items(test_data)

    def set_items(self, items: List[Dict[str, Any]]):
        """设置项目数据"""
        print(f"设置项目数据: {len(items)} 项")

        self.current_items = items
        self.search_engine.set_data_source(items)

        # 应用当前过滤和排序
        self._apply_filters_and_sort()

    def _apply_filters_and_sort(self):
        """应用过滤和排序"""
        # 获取搜索条件
        query = self.search_input.text().strip()

        # 获取类型过滤
        type_filter = self.type_filter_combo.currentText()
        filters = {}
        if type_filter != "全部":
            type_map = {"图片": "image", "视频": "video", "音频": "audio", "文档": "document"}
            filters['file_type'] = type_map.get(type_filter)

        # 搜索
        filtered_data = self.search_engine.search(query, filters)

        # 排序
        sort_text = self.sort_combo.currentText()
        sort_key_map = {"名称": "name", "大小": "size", "日期": "date", "类型": "file_type"}
        sort_key = sort_key_map.get(sort_text, "name")
        reverse = self.sort_desc_checkbox.isChecked()

        sorted_data = self.search_engine.sort_data(filtered_data, sort_key, reverse)

        # 更新显示
        self.filtered_items = sorted_data
        self.list_view.setDataSource(sorted_data)

        # 更新状态
        self.items_count_label.setText(f"项目: {len(sorted_data)}")

        print(f"过滤排序后: {len(sorted_data)} 项")

    def _on_search_changed(self, text: str):
        """搜索变更"""
        # 延迟搜索，避免频繁触发
        self.update_timer.stop()
        self.update_timer.start(300)  # 300ms延迟

    def _on_sort_changed(self):
        """排序变更"""
        self.update_timer.stop()
        self.update_timer.start(100)  # 100ms延迟

    def _on_filter_changed(self, text: str):
        """过滤变更"""
        self.update_timer.stop()
        self.update_timer.start(100)  # 100ms延迟



    def _perform_update(self):
        """执行延迟更新"""
        self._apply_filters_and_sort()

    def _on_item_activated(self, row: int, item_data: dict):
        """项目激活"""
        self.item_selected.emit(row, item_data)

    def _on_selection_changed(self, selected_rows: List[int]):
        """选择变更"""
        self.selected_count_label.setText(f"选中: {len(selected_rows)}")
        self.items_selection_changed.emit(len(selected_rows))

    def _toggle_performance_monitor(self, checked: bool):
        """切换性能监控显示"""
        self.performance_monitor.setVisible(checked)
        self.performance_toggle.setText("隐藏性能监控" if checked else "显示性能监控")

    def _on_data_loading(self, start: int, end: int):
        """数据加载开始"""
        self.loading_label.setText(f"加载中... {start}-{end}")

    def _on_data_loaded(self, start: int, end: int):
        """数据加载完成"""
        self.loading_label.setText("就绪")

    def select_all(self):
        """全选"""
        self.list_view.selectAll()

    def copy_selected(self):
        """复制选中项"""
        # TODO: 实现复制功能
        pass

    def delete_selected(self):
        """删除选中项"""
        # TODO: 实现删除功能
        pass
