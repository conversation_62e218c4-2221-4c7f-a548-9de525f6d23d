#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡顿修复验证测试脚本
测试滑块拖动和分类切换的性能优化效果
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QSlider, QPushButton, QLabel, QTextEdit, QHBoxLayout
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont

class SliderTestWidget(QWidget):
    """滑块测试控件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # 模拟防抖动定时器
        self.size_change_timer = QTimer()
        self.size_change_timer.setSingleShot(True)
        self.size_change_timer.timeout.connect(self._apply_size_change)
        
        self.pending_size = 150
        self.update_count = 0
        self.last_update_time = time.time()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("滑块拖动性能测试")
        title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(title)
        
        # 滑块
        slider_layout = QHBoxLayout()
        slider_layout.addWidget(QLabel("缩略图大小:"))
        
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setRange(100, 300)
        self.slider.setValue(150)
        self.slider.valueChanged.connect(self._on_size_changing)
        self.slider.sliderReleased.connect(self._on_size_released)
        slider_layout.addWidget(self.slider)
        
        self.size_label = QLabel("150px")
        slider_layout.addWidget(self.size_label)
        
        layout.addLayout(slider_layout)
        
        # 性能统计
        self.stats_label = QLabel("等待拖动...")
        layout.addWidget(self.stats_label)
        
        # 重置按钮
        reset_btn = QPushButton("重置统计")
        reset_btn.clicked.connect(self.reset_stats)
        layout.addWidget(reset_btn)
        
    def _on_size_changing(self, size):
        """滑块拖动中（防抖动处理）"""
        self.pending_size = size
        self.size_label.setText(f"{size}px")
        
        # 重启定时器，延迟应用更改
        self.size_change_timer.stop()
        self.size_change_timer.start(150)  # 150ms延迟
        
        # 更新统计
        current_time = time.time()
        time_diff = current_time - self.last_update_time
        self.last_update_time = current_time
        
        self.stats_label.setText(f"拖动中... 间隔: {time_diff*1000:.1f}ms (防抖动生效)")
        
    def _on_size_released(self):
        """滑块释放时立即应用更改"""
        self.size_change_timer.stop()
        self._apply_size_change()
        
    def _apply_size_change(self):
        """实际应用大小更改"""
        self.update_count += 1
        current_time = time.time()
        
        # 模拟UI更新操作
        time.sleep(0.01)  # 模拟10ms的UI更新时间
        
        update_time = (time.time() - current_time) * 1000
        
        self.stats_label.setText(f"""
✅ 大小已更新: {self.pending_size}px
📊 更新次数: {self.update_count}
⏱️ 更新耗时: {update_time:.1f}ms
🚀 防抖动: 已启用
        """)
        
    def reset_stats(self):
        """重置统计"""
        self.update_count = 0
        self.last_update_time = time.time()
        self.stats_label.setText("统计已重置")

class CategoryTestWidget(QWidget):
    """分类切换测试控件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # 模拟防抖动定时器
        self.category_timer = QTimer()
        self.category_timer.setSingleShot(True)
        self.category_timer.timeout.connect(self._apply_category_change)
        
        self.last_selected_category = None
        self.pending_category = None
        self.click_count = 0
        self.ignored_clicks = 0
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("分类切换性能测试")
        title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(title)
        
        # 分类按钮
        categories = ["全部素材", "图片文件", "视频文件", "音频文件", "文档文件"]
        
        for i, category in enumerate(categories):
            btn = QPushButton(category)
            btn.clicked.connect(lambda checked, cat_id=i, cat_name=category: self._on_category_clicked(cat_id, cat_name))
            layout.addWidget(btn)
        
        # 性能统计
        self.stats_label = QLabel("等待点击分类...")
        layout.addWidget(self.stats_label)
        
        # 重置按钮
        reset_btn = QPushButton("重置统计")
        reset_btn.clicked.connect(self.reset_stats)
        layout.addWidget(reset_btn)
        
    def _on_category_clicked(self, category_id, category_name):
        """分类点击处理（防抖动）"""
        self.click_count += 1
        
        # 防止重复点击同一分类
        if self.last_selected_category == category_id:
            self.ignored_clicks += 1
            self.stats_label.setText(f"""
🔄 重复点击被忽略: {category_name}
📊 总点击: {self.click_count}
🚫 忽略点击: {self.ignored_clicks}
⚡ 防抖动: 生效
            """)
            return
        
        self.last_selected_category = category_id
        self.pending_category = (category_id, category_name)
        
        # 延迟发送信号，避免频繁点击造成卡顿
        self.category_timer.stop()
        self.category_timer.start(50)  # 50ms延迟
        
        self.stats_label.setText(f"""
⏳ 准备切换到: {category_name}
📊 总点击: {self.click_count}
🚫 忽略点击: {self.ignored_clicks}
⏱️ 延迟: 50ms
        """)
        
    def _apply_category_change(self):
        """实际应用分类更改"""
        if self.pending_category:
            category_id, category_name = self.pending_category
            
            start_time = time.time()
            
            # 模拟异步搜索操作
            time.sleep(0.05)  # 模拟50ms的搜索时间
            
            search_time = (time.time() - start_time) * 1000
            
            self.stats_label.setText(f"""
✅ 已切换到: {category_name}
📊 总点击: {self.click_count}
🚫 忽略点击: {self.ignored_clicks}
⏱️ 搜索耗时: {search_time:.1f}ms
🚀 异步处理: 已启用
            """)
            
    def reset_stats(self):
        """重置统计"""
        self.click_count = 0
        self.ignored_clicks = 0
        self.last_selected_category = None
        self.stats_label.setText("统计已重置")

class PerformanceTestWindow(QMainWindow):
    """性能测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("智能素材管理器 - 卡顿修复验证测试")
        self.setGeometry(100, 100, 800, 600)
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QHBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 滑块测试
        slider_test = SliderTestWidget()
        layout.addWidget(slider_test)
        
        # 分类测试
        category_test = CategoryTestWidget()
        layout.addWidget(category_test)
        
        # 说明文本
        info_layout = QVBoxLayout()
        
        info_label = QLabel("测试说明")
        info_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        info_layout.addWidget(info_label)
        
        info_text = QTextEdit()
        info_text.setMaximumHeight(200)
        info_text.setPlainText("""
🔧 修复内容：

1. 滑块拖动优化：
   • 防抖动定时器（150ms）
   • 批量UI更新
   • 避免频繁重绘

2. 分类切换优化：
   • 异步搜索处理
   • 防重复点击
   • 50ms延迟机制

📊 测试方法：
   • 快速拖动滑块观察响应
   • 快速点击分类按钮
   • 观察防抖动效果
   • 检查性能统计

✅ 预期效果：
   • 滑块拖动流畅
   • 分类切换不卡顿
   • 减少无效操作
        """)
        info_layout.addWidget(info_text)
        
        layout.addLayout(info_layout)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = PerformanceTestWindow()
    window.show()
    
    print("卡顿修复验证测试启动成功！")
    print("测试项目：")
    print("1. 滑块拖动防抖动优化")
    print("2. 分类切换异步处理优化")
    print("3. UI更新性能优化")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
