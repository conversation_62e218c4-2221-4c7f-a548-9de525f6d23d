#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能图片压缩器
针对大尺寸图片进行智能压缩，确保列表流畅度
支持多种压缩策略和质量控制
"""

import os
import time
import threading
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import hashlib

from PySide6.QtCore import QObject, Signal, QThread, QSize, Qt
from PySide6.QtGui import QPixmap, QImage, QPainter, QTransform
from PySide6.QtWidgets import QApplication

class CompressionLevel(Enum):
    """压缩级别"""
    ULTRA_FAST = "ultra_fast"    # 超快速：简单缩放
    FAST = "fast"                # 快速：基本优化
    BALANCED = "balanced"        # 平衡：质量与速度兼顾
    HIGH_QUALITY = "high_quality" # 高质量：最佳效果

@dataclass
class CompressionConfig:
    """压缩配置"""
    max_width: int = 1920
    max_height: int = 1080
    thumbnail_size: int = 200
    jpeg_quality: int = 85
    compression_level: CompressionLevel = CompressionLevel.BALANCED
    enable_cache: bool = True
    cache_dir: Optional[Path] = None
    
    def __post_init__(self):
        if self.cache_dir is None:
            self.cache_dir = Path.home() / ".smart_asset_manager" / "compressed_cache"

@dataclass
class ImageInfo:
    """图片信息"""
    file_path: str
    original_size: Tuple[int, int]
    file_size: int
    needs_compression: bool
    compression_ratio: float = 1.0
    estimated_memory: int = 0

class SmartImageCompressor(QObject):
    """智能图片压缩器"""
    
    # 信号定义
    compression_completed = Signal(str, QPixmap, dict)  # 文件路径, 压缩后图片, 统计信息
    compression_progress = Signal(str, int)  # 文件路径, 进度百分比
    compression_error = Signal(str, str)  # 文件路径, 错误信息
    
    def __init__(self, config: Optional[CompressionConfig] = None):
        super().__init__()
        
        self.config = config or CompressionConfig()
        self.cache_dir = self.config.cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 压缩统计
        self.stats = {
            'total_processed': 0,
            'total_compressed': 0,
            'total_cache_hits': 0,
            'total_memory_saved': 0,
            'total_time_saved': 0
        }
        
        # 缓存管理
        self.memory_cache = {}
        self.cache_lock = threading.RLock()
        self.max_memory_cache = 50  # 最多缓存50张压缩图片
        
        print(f"智能图片压缩器初始化完成")
        print(f"  • 最大尺寸: {self.config.max_width}x{self.config.max_height}")
        print(f"  • 压缩级别: {self.config.compression_level.value}")
        print(f"  • 缓存目录: {self.cache_dir}")
    
    def analyze_image(self, file_path: str) -> ImageInfo:
        """分析图片信息"""
        try:
            # 获取文件信息
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size
            
            # 快速获取图片尺寸（不加载完整图片）
            image = QImage(file_path)
            if image.isNull():
                raise ValueError("无法读取图片")
            
            width, height = image.width(), image.height()
            original_size = (width, height)
            
            # 判断是否需要压缩
            needs_compression = (
                width > self.config.max_width or 
                height > self.config.max_height or
                file_size > 10 * 1024 * 1024  # 大于10MB
            )
            
            # 计算压缩比例
            if needs_compression:
                scale_w = self.config.max_width / width if width > self.config.max_width else 1.0
                scale_h = self.config.max_height / height if height > self.config.max_height else 1.0
                compression_ratio = min(scale_w, scale_h)
            else:
                compression_ratio = 1.0
            
            # 估算内存使用
            estimated_memory = width * height * 4  # RGBA
            
            return ImageInfo(
                file_path=file_path,
                original_size=original_size,
                file_size=file_size,
                needs_compression=needs_compression,
                compression_ratio=compression_ratio,
                estimated_memory=estimated_memory
            )
            
        except Exception as e:
            raise ValueError(f"分析图片失败: {e}")
    
    def get_cache_path(self, file_path: str, target_size: Tuple[int, int]) -> Path:
        """获取缓存文件路径"""
        # 创建唯一的缓存键
        file_stat = os.stat(file_path)
        cache_key = f"{file_path}_{target_size[0]}x{target_size[1]}_{file_stat.st_mtime}"
        cache_hash = hashlib.md5(cache_key.encode()).hexdigest()
        
        return self.cache_dir / f"{cache_hash}.jpg"
    
    def load_from_cache(self, file_path: str, target_size: Tuple[int, int]) -> Optional[QPixmap]:
        """从缓存加载图片"""
        if not self.config.enable_cache:
            return None
        
        try:
            # 检查内存缓存
            cache_key = f"{file_path}_{target_size[0]}x{target_size[1]}"
            with self.cache_lock:
                if cache_key in self.memory_cache:
                    self.stats['total_cache_hits'] += 1
                    return self.memory_cache[cache_key]
            
            # 检查磁盘缓存
            cache_path = self.get_cache_path(file_path, target_size)
            if cache_path.exists():
                pixmap = QPixmap(str(cache_path))
                if not pixmap.isNull():
                    # 添加到内存缓存
                    self._add_to_memory_cache(cache_key, pixmap)
                    self.stats['total_cache_hits'] += 1
                    return pixmap
            
            return None
            
        except Exception as e:
            print(f"缓存加载失败 {file_path}: {e}")
            return None
    
    def save_to_cache(self, file_path: str, target_size: Tuple[int, int], pixmap: QPixmap):
        """保存到缓存"""
        if not self.config.enable_cache:
            return
        
        try:
            # 保存到磁盘缓存
            cache_path = self.get_cache_path(file_path, target_size)
            pixmap.save(str(cache_path), "JPEG", self.config.jpeg_quality)
            
            # 添加到内存缓存
            cache_key = f"{file_path}_{target_size[0]}x{target_size[1]}"
            self._add_to_memory_cache(cache_key, pixmap)
            
        except Exception as e:
            print(f"缓存保存失败 {file_path}: {e}")
    
    def _add_to_memory_cache(self, key: str, pixmap: QPixmap):
        """添加到内存缓存"""
        with self.cache_lock:
            # 如果缓存已满，移除最旧的项
            if len(self.memory_cache) >= self.max_memory_cache:
                oldest_key = next(iter(self.memory_cache))
                del self.memory_cache[oldest_key]
            
            self.memory_cache[key] = pixmap
    
    def compress_image(self, file_path: str, target_size: Optional[Tuple[int, int]] = None) -> QPixmap:
        """压缩图片"""
        start_time = time.time()
        
        try:
            # 分析图片
            image_info = self.analyze_image(file_path)
            self.stats['total_processed'] += 1
            
            # 确定目标尺寸
            if target_size is None:
                if image_info.needs_compression:
                    new_width = int(image_info.original_size[0] * image_info.compression_ratio)
                    new_height = int(image_info.original_size[1] * image_info.compression_ratio)
                    target_size = (new_width, new_height)
                else:
                    target_size = image_info.original_size
            
            # 检查缓存
            cached_pixmap = self.load_from_cache(file_path, target_size)
            if cached_pixmap:
                return cached_pixmap
            
            # 执行压缩
            if image_info.needs_compression:
                compressed_pixmap = self._perform_compression(file_path, target_size, image_info)
                self.stats['total_compressed'] += 1
                
                # 计算节省的内存
                original_memory = image_info.estimated_memory
                compressed_memory = target_size[0] * target_size[1] * 4
                memory_saved = original_memory - compressed_memory
                self.stats['total_memory_saved'] += memory_saved
                
            else:
                # 不需要压缩，直接加载
                compressed_pixmap = QPixmap(file_path)
            
            # 保存到缓存
            self.save_to_cache(file_path, target_size, compressed_pixmap)
            
            # 统计时间
            compression_time = time.time() - start_time
            self.stats['total_time_saved'] += compression_time
            
            # 发送完成信号
            stats_info = {
                'original_size': image_info.original_size,
                'compressed_size': target_size,
                'compression_ratio': image_info.compression_ratio,
                'compression_time': compression_time,
                'memory_saved': memory_saved if image_info.needs_compression else 0
            }
            
            self.compression_completed.emit(file_path, compressed_pixmap, stats_info)
            
            return compressed_pixmap
            
        except Exception as e:
            error_msg = f"压缩图片失败: {e}"
            self.compression_error.emit(file_path, error_msg)
            print(f"压缩失败 {file_path}: {error_msg}")
            
            # 返回空图片
            return QPixmap()
    
    def _perform_compression(self, file_path: str, target_size: Tuple[int, int], image_info: ImageInfo) -> QPixmap:
        """执行图片压缩"""
        try:
            # 根据压缩级别选择算法
            if self.config.compression_level == CompressionLevel.ULTRA_FAST:
                return self._compress_ultra_fast(file_path, target_size)
            elif self.config.compression_level == CompressionLevel.FAST:
                return self._compress_fast(file_path, target_size)
            elif self.config.compression_level == CompressionLevel.BALANCED:
                return self._compress_balanced(file_path, target_size)
            else:  # HIGH_QUALITY
                return self._compress_high_quality(file_path, target_size)
                
        except Exception as e:
            raise ValueError(f"压缩执行失败: {e}")
    
    def _compress_ultra_fast(self, file_path: str, target_size: Tuple[int, int]) -> QPixmap:
        """超快速压缩：简单缩放"""
        pixmap = QPixmap(file_path)
        return pixmap.scaled(
            target_size[0], target_size[1],
            Qt.KeepAspectRatio,
            Qt.FastTransformation
        )
    
    def _compress_fast(self, file_path: str, target_size: Tuple[int, int]) -> QPixmap:
        """快速压缩：基本优化"""
        image = QImage(file_path)
        scaled_image = image.scaled(
            target_size[0], target_size[1],
            Qt.KeepAspectRatio,
            Qt.FastTransformation
        )
        return QPixmap.fromImage(scaled_image)
    
    def _compress_balanced(self, file_path: str, target_size: Tuple[int, int]) -> QPixmap:
        """平衡压缩：质量与速度兼顾"""
        image = QImage(file_path)
        
        # 如果原图太大，先进行预缩放
        if image.width() > target_size[0] * 2 or image.height() > target_size[1] * 2:
            pre_scale_w = target_size[0] * 1.5
            pre_scale_h = target_size[1] * 1.5
            image = image.scaled(
                int(pre_scale_w), int(pre_scale_h),
                Qt.KeepAspectRatio,
                Qt.FastTransformation
            )
        
        # 最终缩放使用平滑变换
        scaled_image = image.scaled(
            target_size[0], target_size[1],
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        
        return QPixmap.fromImage(scaled_image)
    
    def _compress_high_quality(self, file_path: str, target_size: Tuple[int, int]) -> QPixmap:
        """高质量压缩：最佳效果"""
        image = QImage(file_path)
        
        # 多步缩放以获得最佳质量
        current_image = image
        current_w, current_h = image.width(), image.height()
        target_w, target_h = target_size
        
        # 逐步缩放，每次不超过50%
        while current_w > target_w * 1.5 or current_h > target_h * 1.5:
            current_w = max(int(current_w * 0.7), target_w)
            current_h = max(int(current_h * 0.7), target_h)
            
            current_image = current_image.scaled(
                current_w, current_h,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
        
        # 最终缩放
        final_image = current_image.scaled(
            target_w, target_h,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        
        return QPixmap.fromImage(final_image)
    
    def compress_for_thumbnail(self, file_path: str) -> QPixmap:
        """为缩略图压缩"""
        thumbnail_size = (self.config.thumbnail_size, self.config.thumbnail_size)
        return self.compress_image(file_path, thumbnail_size)
    
    def clear_cache(self):
        """清理缓存"""
        with self.cache_lock:
            self.memory_cache.clear()
        
        # 清理磁盘缓存（可选）
        try:
            for cache_file in self.cache_dir.glob("*.jpg"):
                cache_file.unlink()
        except Exception as e:
            print(f"清理磁盘缓存失败: {e}")
    
    def get_compression_stats(self) -> Dict[str, Any]:
        """获取压缩统计信息"""
        return {
            'total_processed': self.stats['total_processed'],
            'total_compressed': self.stats['total_compressed'],
            'compression_rate': (self.stats['total_compressed'] / max(1, self.stats['total_processed'])) * 100,
            'cache_hit_rate': (self.stats['total_cache_hits'] / max(1, self.stats['total_processed'])) * 100,
            'total_memory_saved_mb': self.stats['total_memory_saved'] / 1024 / 1024,
            'average_compression_time': self.stats['total_time_saved'] / max(1, self.stats['total_compressed']),
            'memory_cache_size': len(self.memory_cache)
        }

# 全局压缩器实例
_image_compressor = None

def get_image_compressor(config: Optional[CompressionConfig] = None) -> SmartImageCompressor:
    """获取全局图片压缩器实例"""
    global _image_compressor
    if _image_compressor is None:
        _image_compressor = SmartImageCompressor(config)
    return _image_compressor

def cleanup_image_compressor():
    """清理全局图片压缩器"""
    global _image_compressor
    if _image_compressor:
        _image_compressor.clear_cache()
        _image_compressor = None
