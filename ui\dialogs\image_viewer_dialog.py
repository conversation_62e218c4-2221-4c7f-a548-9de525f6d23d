# 图片查看器对话框
# 功能：显示原图，支持缩放、平移等操作

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QScrollArea, QFrame, QSizePolicy,
                               QApplication, QMessageBox, QSlider, QSpacerItem)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QPixmap, QIcon, QKeySequence, QShortcut, QWheelEvent
import os
from pathlib import Path

class ImageViewerDialog(QDialog):
    """图片查看器对话框"""
    
    def __init__(self, image_path: str, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.original_pixmap = None
        self.current_scale = 1.0
        self.min_scale = 0.1
        self.max_scale = 5.0
        
        self.setup_ui()
        self.load_image()
        self.setup_shortcuts()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("图片查看器")
        self.setModal(True)
        self.resize(800, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # 图片显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignCenter)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #2c3e50;
            }
        """)
        
        # 图片标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("background-color: #2c3e50;")
        self.image_label.setSizePolicy(QSizePolicy.Ignored, QSizePolicy.Ignored)
        
        self.scroll_area.setWidget(self.image_label)
        main_layout.addWidget(self.scroll_area)
        
        # 状态栏
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setFixedHeight(50)
        toolbar.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-bottom: 1px solid #2c3e50;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 缩放按钮
        self.zoom_in_btn = QPushButton("放大 (+)")
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        layout.addWidget(self.zoom_in_btn)
        
        self.zoom_out_btn = QPushButton("缩小 (-)")
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        layout.addWidget(self.zoom_out_btn)
        
        self.fit_window_btn = QPushButton("适应窗口")
        self.fit_window_btn.clicked.connect(self.fit_to_window)
        layout.addWidget(self.fit_window_btn)
        
        self.actual_size_btn = QPushButton("实际大小")
        self.actual_size_btn.clicked.connect(self.actual_size)
        layout.addWidget(self.actual_size_btn)
        
        # 缩放滑块
        layout.addItem(QSpacerItem(20, 0, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
        zoom_label = QLabel("缩放:")
        zoom_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(zoom_label)
        
        self.zoom_slider = QSlider(Qt.Horizontal)
        self.zoom_slider.setMinimum(int(self.min_scale * 100))
        self.zoom_slider.setMaximum(int(self.max_scale * 100))
        self.zoom_slider.setValue(100)
        self.zoom_slider.setFixedWidth(150)
        self.zoom_slider.valueChanged.connect(self.on_zoom_slider_changed)
        layout.addWidget(self.zoom_slider)
        
        # 关闭按钮
        layout.addItem(QSpacerItem(20, 0, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        return toolbar
        
    def create_status_bar(self):
        """创建状态栏"""
        status_bar = QFrame()
        status_bar.setFixedHeight(30)
        status_bar.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-top: 1px solid #2c3e50;
            }
            QLabel {
                color: white;
                padding: 5px;
            }
        """)
        
        layout = QHBoxLayout(status_bar)
        layout.setContentsMargins(10, 0, 10, 0)
        
        # 文件信息
        self.file_info_label = QLabel()
        layout.addWidget(self.file_info_label)
        
        layout.addItem(QSpacerItem(0, 0, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
        # 缩放信息
        self.zoom_info_label = QLabel()
        layout.addWidget(self.zoom_info_label)
        
        return status_bar
        
    def setup_shortcuts(self):
        """设置快捷键"""
        # 缩放快捷键
        QShortcut(QKeySequence("Ctrl++"), self, self.zoom_in)
        QShortcut(QKeySequence("Ctrl+="), self, self.zoom_in)
        QShortcut(QKeySequence("Ctrl+-"), self, self.zoom_out)
        QShortcut(QKeySequence("Ctrl+0"), self, self.fit_to_window)
        QShortcut(QKeySequence("Ctrl+1"), self, self.actual_size)
        
        # 关闭快捷键
        QShortcut(QKeySequence("Escape"), self, self.close)
        
    def load_image(self):
        """加载图片"""
        try:
            if not os.path.exists(self.image_path):
                QMessageBox.warning(self, "错误", "图片文件不存在")
                self.close()
                return
                
            self.original_pixmap = QPixmap(self.image_path)
            if self.original_pixmap.isNull():
                QMessageBox.warning(self, "错误", "无法加载图片文件")
                self.close()
                return
                
            # 更新文件信息
            file_info = Path(self.image_path)
            size_info = f"{self.original_pixmap.width()} × {self.original_pixmap.height()}"
            file_size = file_info.stat().st_size
            size_str = self.format_file_size(file_size)
            
            self.file_info_label.setText(
                f"{file_info.name} | {size_info} | {size_str}"
            )
            
            # 自动适应窗口
            self.fit_to_window()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载图片失败: {e}")
            self.close()
            
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
            
    def update_image_display(self):
        """更新图片显示"""
        if not self.original_pixmap:
            return
            
        # 计算缩放后的尺寸
        scaled_size = self.original_pixmap.size() * self.current_scale
        
        # 缩放图片
        scaled_pixmap = self.original_pixmap.scaled(
            scaled_size,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        
        # 设置到标签
        self.image_label.setPixmap(scaled_pixmap)
        self.image_label.resize(scaled_pixmap.size())
        
        # 更新缩放信息
        self.zoom_info_label.setText(f"缩放: {self.current_scale * 100:.0f}%")
        
        # 更新滑块
        self.zoom_slider.blockSignals(True)
        self.zoom_slider.setValue(int(self.current_scale * 100))
        self.zoom_slider.blockSignals(False)
        
    def zoom_in(self):
        """放大"""
        new_scale = min(self.current_scale * 1.25, self.max_scale)
        self.set_scale(new_scale)
        
    def zoom_out(self):
        """缩小"""
        new_scale = max(self.current_scale / 1.25, self.min_scale)
        self.set_scale(new_scale)
        
    def fit_to_window(self):
        """适应窗口"""
        if not self.original_pixmap:
            return
            
        # 获取可用空间
        available_size = self.scroll_area.size()
        image_size = self.original_pixmap.size()
        
        # 计算缩放比例
        scale_w = available_size.width() / image_size.width()
        scale_h = available_size.height() / image_size.height()
        scale = min(scale_w, scale_h, 1.0)  # 不超过原始大小
        
        self.set_scale(scale)
        
    def actual_size(self):
        """实际大小"""
        self.set_scale(1.0)
        
    def set_scale(self, scale):
        """设置缩放比例"""
        self.current_scale = max(self.min_scale, min(scale, self.max_scale))
        self.update_image_display()
        
    def on_zoom_slider_changed(self, value):
        """缩放滑块变化"""
        scale = value / 100.0
        self.set_scale(scale)
        
    def wheelEvent(self, event: QWheelEvent):
        """鼠标滚轮事件"""
        if event.modifiers() == Qt.ControlModifier:
            # Ctrl + 滚轮缩放
            delta = event.angleDelta().y()
            if delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()
            event.accept()
        else:
            super().wheelEvent(event)
            
    def keyPressEvent(self, event):
        """键盘事件"""
        if event.key() == Qt.Key_Space:
            # 空格键适应窗口
            self.fit_to_window()
            event.accept()
        else:
            super().keyPressEvent(event)
