# 主窗口模块
# 功能：应用程序主界面，包含标题栏、工具栏、侧边栏、内容区域和状态栏的完整布局

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QSplitter, QToolBar, QStatusBar, QMenuBar, QMenu,
                               QLabel, QPushButton, QLineEdit, QFrame, QFileDialog,
                               QMessageBox, QProgressDialog, QApplication)
from PySide6.QtCore import Qt, Signal, QSize, QTimer, QThread
from PySide6.QtGui import QIcon, QKeySequence, QPixmap, QAction
from typing import List

from ui.components.sidebar import SidebarWidget
from ui.components.content_area import ContentAreaWidget

from ui.components.title_bar import CustomTitleBar
from ui.components.toolbar import MainToolBar
from ui.dialogs.settings_dialog import SettingsDialog
from ui.dialogs.about_dialog import AboutDialog
from core.file_manager import FileManager
from core.search_engine import SearchEngine

class CategorySearchThread(QThread):
    """分类搜索线程（异步处理）"""

    search_completed = Signal(list, int, str)  # results, total_count, category_name
    search_failed = Signal(str)  # error_message

    def __init__(self, search_engine, filters, category_name):
        super().__init__()
        self.search_engine = search_engine
        self.filters = filters
        self.category_name = category_name

    def run(self):
        """执行搜索"""
        try:
            # 执行搜索
            results, total_count = self.search_engine.search("", self.filters)

            # 发送完成信号
            self.search_completed.emit(results, total_count, self.category_name)

        except Exception as e:
            # 发送失败信号
            self.search_failed.emit(str(e))

class MainWindow(QMainWindow):
    """主窗口类"""

    # 信号定义
    file_imported = Signal(list)  # 文件导入信号
    search_requested = Signal(str, dict)  # 搜索请求信号

    def __init__(self, theme_manager, db_manager, config_manager):
        super().__init__()

        self.theme_manager = theme_manager
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 核心管理器
        self.file_manager = FileManager(db_manager, config_manager)
        self.search_engine = SearchEngine(db_manager, config_manager)

        # 界面组件
        self.sidebar = None
        self.content_area = None
        self.custom_title_bar = None
        self.main_toolbar = None
        self.splitter = None

        # 状态
        self.current_view_mode = "grid"

        self.setup_ui()
        self.setup_menu_bar()
        self.setup_status_bar()
        self.setup_connections()
        self.restore_window_state()

    def setup_ui(self):
        """设置用户界面"""
        # 设置窗口属性
        self.setWindowTitle("智能素材管理器")
        self.setMinimumSize(800, 600)

        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建自定义标题栏
        self.custom_title_bar = CustomTitleBar(self)
        main_layout.addWidget(self.custom_title_bar)

        # 创建工具栏
        self.main_toolbar = MainToolBar(self)
        main_layout.addWidget(self.main_toolbar)

        # 创建主要内容区域
        self.setup_main_content(main_layout)

    def setup_main_content(self, parent_layout):
        """设置主要内容区域"""
        # 创建水平分割器
        self.splitter = QSplitter(Qt.Horizontal)
        parent_layout.addWidget(self.splitter)

        # 创建侧边栏
        self.sidebar = SidebarWidget(
            theme_manager=self.theme_manager,
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
        self.splitter.addWidget(self.sidebar)

        # 创建内容区域
        self.content_area = ContentAreaWidget(
            theme_manager=self.theme_manager,
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
        self.splitter.addWidget(self.content_area)

        # 设置分割器比例
        ui_config = self.config_manager.get_ui_config()
        sidebar_width = ui_config.get("sidebar_width", 250)

        self.splitter.setSizes([sidebar_width, 800])

    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 导入文件
        import_action = QAction("导入文件(&I)", self)
        import_action.setShortcut(QKeySequence.Open)
        import_action.triggered.connect(self.import_files)
        file_menu.addAction(import_action)

        # 导入文件夹
        import_folder_action = QAction("导入文件夹(&F)", self)
        import_folder_action.setShortcut(QKeySequence("Ctrl+Shift+O"))
        import_folder_action.triggered.connect(self.import_folder)
        file_menu.addAction(import_folder_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        # 全选
        select_all_action = QAction("全选(&A)", self)
        select_all_action.setShortcut(QKeySequence.SelectAll)
        select_all_action.triggered.connect(self.select_all)
        edit_menu.addAction(select_all_action)

        # 反选
        invert_selection_action = QAction("反选(&I)", self)
        invert_selection_action.setShortcut("Ctrl+I")
        invert_selection_action.triggered.connect(self.invert_selection)
        edit_menu.addAction(invert_selection_action)

        # 清空选择
        clear_selection_action = QAction("清空选择(&N)", self)
        clear_selection_action.setShortcut("Ctrl+Shift+D")
        clear_selection_action.triggered.connect(self.clear_selection)
        edit_menu.addAction(clear_selection_action)

        # 清空列表
        clear_list_action = QAction("清空列表(&L)", self)
        clear_list_action.setShortcut("Ctrl+Shift+L")
        clear_list_action.triggered.connect(self.clear_list_display)
        edit_menu.addAction(clear_list_action)

        # 删除选中素材
        delete_selected_action = QAction("删除选中素材(&R)", self)
        delete_selected_action.setShortcut("Ctrl+D")
        delete_selected_action.triggered.connect(self.delete_selected_items)
        edit_menu.addAction(delete_selected_action)

        edit_menu.addSeparator()

        # 复制
        copy_action = QAction("复制(&C)", self)
        copy_action.setShortcut(QKeySequence.Copy)
        copy_action.triggered.connect(self.copy_selected)
        edit_menu.addAction(copy_action)

        edit_menu.addSeparator()

        # 删除
        delete_action = QAction("删除(&D)", self)
        delete_action.setShortcut(QKeySequence.Delete)
        delete_action.triggered.connect(self.delete_selected)
        edit_menu.addAction(delete_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")

        # 视图模式菜单已简化，移除视图切换选项



        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 重建索引
        rebuild_index_action = QAction("重建索引(&R)", self)
        rebuild_index_action.triggered.connect(self.rebuild_index)
        tools_menu.addAction(rebuild_index_action)

        # 清理缓存
        clear_cache_action = QAction("清理缓存(&C)", self)
        clear_cache_action.triggered.connect(self.clear_cache)
        tools_menu.addAction(clear_cache_action)

        tools_menu.addSeparator()

        # 设置
        settings_action = QAction("设置(&S)", self)
        settings_action.setShortcut(QKeySequence.Preferences)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """设置状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # 文件统计标签
        self.files_count_label = QLabel("文件: 0")
        status_bar.addWidget(self.files_count_label)

        # 选中项目标签
        self.selected_count_label = QLabel("选中: 0")
        status_bar.addWidget(self.selected_count_label)

        # 存储空间标签
        self.storage_label = QLabel("存储: 0 MB")
        status_bar.addWidget(self.storage_label)

        # 状态信息标签
        self.status_label = QLabel("就绪")
        status_bar.addPermanentWidget(self.status_label)

    def setup_connections(self):
        """设置信号连接"""
        # 工具栏信号连接
        self.main_toolbar.search_requested.connect(self.on_search_requested)
        self.main_toolbar.import_requested.connect(self.import_files)

        # 侧边栏信号连接
        self.sidebar.category_selected.connect(self.on_category_selected)
        self.sidebar.tag_selected.connect(self.on_tag_selected)
        self.sidebar.color_filter_changed.connect(self.on_color_filter_changed)

        # 内容区域信号连接
        self.content_area.item_selected.connect(self.on_item_selected)
        self.content_area.items_selection_changed.connect(self.on_selection_changed)
        self.content_area.files_dropped.connect(self.on_files_dropped)

        # 主题变更信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)

    def restore_window_state(self):
        """恢复窗口状态"""
        window_config = self.config_manager.get_window_config()

        # 恢复窗口大小和位置
        self.resize(window_config.get("width", 1200), window_config.get("height", 800))

        if window_config.get("maximized", False):
            self.showMaximized()
        else:
            pos = window_config.get("position", {"x": 100, "y": 100})
            self.move(pos["x"], pos["y"])

    def save_window_state(self):
        """保存窗口状态"""
        window_config = {
            "width": self.width(),
            "height": self.height(),
            "maximized": self.isMaximized(),
            "position": {"x": self.x(), "y": self.y()}
        }

        ui_config = self.config_manager.get_ui_config()
        ui_config.update({
            "sidebar_width": self.splitter.sizes()[0],
            "view_mode": self.current_view_mode
        })

        self.config_manager.set_window_config(window_config)
        self.config_manager.set_ui_config(ui_config)

    # 槽函数实现
    def import_files(self):
        """导入文件（增强版 - 支持分类选择）"""
        try:
            # 获取支持的文件格式
            file_config = self.config_manager.get_file_management_config()
            supported_formats = file_config.get('supported_formats', [])

            # 构建文件过滤器
            filter_parts = []
            for fmt in supported_formats:
                filter_parts.append(f"*{fmt}")
            file_filter = f"支持的文件 ({' '.join(filter_parts)})"

            # 打开文件选择对话框
            files, _ = QFileDialog.getOpenFileNames(
                self, "选择要导入的文件", "", file_filter
            )

            if not files:
                return  # 用户取消了文件选择

            # 显示分类选择对话框
            from ui.dialogs.import_category_dialog import show_import_category_dialog

            import_options = show_import_category_dialog(files, self)

            if not import_options:
                return  # 用户取消了分类选择

            # 执行导入
            self._import_files_with_category(files, import_options)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入文件失败: {e}")

    def import_folder(self):
        """导入文件夹"""
        try:
            # 打开文件夹选择对话框
            folder = QFileDialog.getExistingDirectory(
                self, "选择要导入的文件夹"
            )

            if folder:
                self._import_folder_with_progress(folder)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入文件夹失败: {e}")

    def _import_files_with_category(self, files, import_options):
        """带分类选择的文件导入"""
        try:
            category_id = import_options["category_id"]
            category_name = import_options["category_name"]

            print(f"📁 开始导入 {len(files)} 个文件到分类: {category_name}")

            # 如果选择了临时分组，确保临时分组存在
            if category_id == "temp":
                self._ensure_temp_category()

            # 创建进度对话框
            progress = QProgressDialog(f"正在导入文件到 {category_name}...", "取消", 0, len(files), self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # 连接文件管理器信号
            self.file_manager.import_progress.connect(progress.setValue)
            self.file_manager.import_completed.connect(
                lambda count: self._on_import_completed_with_category(count, category_name)
            )

            # 开始导入（传递分类信息）
            self.file_manager.import_files_with_category(files, import_options)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"分类导入失败: {e}")

    def _import_files_with_progress(self, files):
        """带进度条的文件导入（原版本，保持兼容性）"""
        # 如果没有分类选择，使用临时分组
        import_options = {
            "category_id": "temp",
            "category_name": "临时分组",
            "auto_categorize": True,
            "check_duplicates": True,
            "remember_choice": False
        }

        self._import_files_with_category(files, import_options)

    def _import_folder_with_progress(self, folder):
        """带进度条的文件夹导入"""
        # 创建进度对话框
        progress = QProgressDialog("正在扫描文件夹...", "取消", 0, 0, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.show()

        # 连接文件管理器信号
        self.file_manager.import_progress.connect(progress.setValue)
        self.file_manager.import_completed.connect(self._on_import_completed)

        # 开始导入
        self.file_manager.import_directory(folder)

    def _ensure_temp_category(self):
        """确保临时分组存在"""
        try:
            from core.category_manager import get_category_manager
            category_manager = get_category_manager()

            temp_category = category_manager.get_category("temp")
            if not temp_category:
                # 临时分组应该在系统分类中自动创建
                print("⚠️ 临时分组不存在，重新初始化系统分类")
                category_manager._init_system_categories()
                category_manager.save_categories()

        except Exception as e:
            print(f"❌ 确保临时分组失败: {e}")

    def _on_import_completed_with_category(self, imported_count, category_name):
        """带分类信息的导入完成处理"""
        result_text = f"成功导入 {imported_count} 个文件到分类: {category_name}"
        QMessageBox.information(self, "导入完成", result_text)

        # 刷新内容区域
        if self.content_area:
            self.content_area.load_items()

        # 刷新侧边栏
        if self.sidebar:
            self.sidebar.refresh_data()

        # 更新状态栏
        self.status_label.setText(f"已导入 {imported_count} 个文件到 {category_name}")

    def _on_import_completed(self, imported_count):
        """导入完成处理（原版本）"""
        self._on_import_completed_with_category(imported_count, "临时分组")

    def select_all(self):
        """全选所有素材"""
        if self.content_area:
            self.content_area.select_all_items()
            print("✅ 执行全选操作")

    def invert_selection(self):
        """反选素材"""
        if self.content_area:
            self.content_area.invert_selection()
            print("🔄 执行反选操作")

    def clear_selection(self):
        """清空所有选择"""
        if self.content_area:
            self.content_area.clear_selection()
            print("❌ 执行清空选择操作")

    def clear_list_display(self):
        """清空列表显示"""
        if self.content_area:
            self.content_area.clear_list_display()
            print("🗑️ 执行清空列表显示操作")

    def delete_selected_items(self):
        """删除选中的素材"""
        if self.content_area:
            self.content_area.delete_selected_items()
            print("🗑️ 执行删除选中素材操作")

    def copy_selected(self):
        """复制选中项"""
        if self.content_area:
            self.content_area.copy_selected()

    def delete_selected(self):
        """删除选中项"""
        if self.content_area:
            self.content_area.delete_selected()





    def rebuild_index(self):
        """重建索引"""
        try:
            reply = QMessageBox.question(
                self, "重建索引",
                "重建索引将重新扫描所有文件，这可能需要一些时间。确定继续吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.status_label.setText("正在重建索引...")

                # 清空数据库
                # TODO: 实现数据库清理

                # 重新扫描所有已知路径
                # TODO: 实现路径重新扫描

                self.status_label.setText("索引重建完成")
                QMessageBox.information(self, "完成", "索引重建完成")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"重建索引失败: {e}")

    def clear_cache(self):
        """清理缓存"""
        try:
            import shutil

            # 获取缓存目录
            cache_dir = self.config_manager.get_cache_dir()
            thumbnails_dir = self.config_manager.get_thumbnails_dir()

            # 计算缓存大小
            cache_size = 0
            if cache_dir.exists():
                for file_path in cache_dir.rglob('*'):
                    if file_path.is_file():
                        cache_size += file_path.stat().st_size

            cache_size_mb = cache_size / (1024 * 1024)

            reply = QMessageBox.question(
                self, "清理缓存",
                f"将清理 {cache_size_mb:.2f} MB 的缓存数据，确定继续吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.status_label.setText("正在清理缓存...")

                # 清理缩略图缓存
                if thumbnails_dir.exists():
                    shutil.rmtree(thumbnails_dir)
                    thumbnails_dir.mkdir(exist_ok=True)

                # 清理其他缓存文件
                for cache_file in cache_dir.glob('*.cache'):
                    cache_file.unlink()

                self.status_label.setText("缓存清理完成")
                QMessageBox.information(self, "完成", f"已清理 {cache_size_mb:.2f} MB 缓存")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"清理缓存失败: {e}")

    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self.config_manager, self.theme_manager, self)
        dialog.exec()

    def show_about(self):
        """显示关于对话框"""
        dialog = AboutDialog(self)
        dialog.exec()

    def on_search_requested(self, query: str, filters: dict):
        """处理搜索请求"""
        try:
            # 使用搜索引擎进行搜索
            results, total_count = self.search_engine.search(query, filters)

            # 更新内容区域显示结果
            if self.content_area:
                self.content_area.current_items = results
                self.content_area.update_current_view()

            # 更新状态栏
            self.files_count_label.setText(f"文件: {total_count}")
            self.status_label.setText(f"搜索到 {len(results)} 个结果")

        except Exception as e:
            QMessageBox.warning(self, "搜索错误", f"搜索失败: {e}")

    def on_category_selected(self, category_id):
        """处理分类选择"""
        try:
            print(f"主窗口收到分类选择: {category_id}")

            # 显示加载状态
            self.status_label.setText("正在加载...")
            QApplication.processEvents()  # 立即更新UI

            # 根据分类ID构建筛选条件
            filters = {}

            if category_id == -1:  # 全部素材
                filters = {}
                category_name = "全部素材"
            elif isinstance(category_id, str):  # 文件类型分类
                filters['file_type'] = category_id
                category_name = f"{category_id}文件"
            else:  # 自定义分类
                # TODO: 实现自定义分类筛选
                filters['category_id'] = category_id
                category_name = f"分类{category_id}"

            print(f"应用筛选条件: {filters}")

            # 异步执行搜索，避免UI阻塞
            self._perform_category_search(filters, category_name)

        except Exception as e:
            print(f"分类筛选失败: {e}")
            self.status_label.setText("筛选失败")
            QMessageBox.warning(self, "筛选错误", f"分类筛选失败: {e}")

    def _perform_category_search(self, filters, category_name):
        """执行分类搜索（异步优化性能）"""
        # 创建异步搜索线程
        self.search_thread = CategorySearchThread(self.search_engine, filters, category_name)
        self.search_thread.search_completed.connect(self._on_category_search_completed)
        self.search_thread.search_failed.connect(self._on_category_search_failed)
        self.search_thread.start()

    def _on_category_search_completed(self, results, total_count, category_name):
        """分类搜索完成回调"""
        try:
            print(f"搜索结果: {len(results)} 个文件")

            # 批量更新UI，减少重绘次数
            if self.content_area:
                # 暂时禁用更新
                self.content_area.setUpdatesEnabled(False)

                # 更新数据
                self.content_area.current_items = results
                self.content_area.update_current_view()

                # 重新启用更新
                self.content_area.setUpdatesEnabled(True)

            # 更新状态栏
            self.files_count_label.setText(f"文件: {total_count}")
            self.status_label.setText(f"显示 {category_name} - {len(results)} 个结果")

        except Exception as e:
            print(f"更新搜索结果失败: {e}")
            self.status_label.setText("更新失败")

    def _on_category_search_failed(self, error_message):
        """分类搜索失败回调"""
        print(f"执行分类搜索失败: {error_message}")
        self.status_label.setText("搜索失败")

    def on_tag_selected(self, tag_name: str):
        """处理标签选择"""
        try:
            # 使用标签进行搜索
            results, total_count = self.search_engine.search(tag_name)

            # 更新内容区域
            if self.content_area:
                self.content_area.current_items = results
                self.content_area.update_current_view()

            # 更新状态栏
            self.files_count_label.setText(f"文件: {total_count}")
            self.status_label.setText(f"标签 '{tag_name}' 的搜索结果")

        except Exception as e:
            print(f"标签筛选失败: {e}")

    def on_color_filter_changed(self, color: str):
        """处理颜色筛选"""
        try:
            # 使用颜色搜索功能
            results = self.search_engine.search_by_color(color)

            # 更新内容区域
            if self.content_area:
                self.content_area.current_items = results
                self.content_area.update_current_view()

            # 更新状态栏
            self.files_count_label.setText(f"文件: {len(results)}")
            self.status_label.setText(f"颜色 '{color}' 的搜索结果")

        except Exception as e:
            print(f"颜色筛选失败: {e}")

    def on_item_selected(self, item_id: int):
        """处理项目选择"""
        # 预览面板已移除，此处可以添加其他处理逻辑
        pass

    def on_selection_changed(self, selected_count: int):
        """处理选择变更"""
        self.selected_count_label.setText(f"选中: {selected_count}")

    def on_files_dropped(self, file_paths: List[str]):
        """处理拖拽文件"""
        try:
            print(f"主窗口收到文件拖拽: {len(file_paths)} 个文件")

            # 使用文件管理器的导入功能
            self._import_files_with_progress(file_paths)

        except Exception as e:
            print(f"文件导入异常: {e}")
            QMessageBox.critical(self, "导入失败", f"文件导入失败: {e}")

    def on_theme_changed(self, theme_name: str):
        """处理主题变更"""
        # 主题已在主程序中应用，这里可以做额外处理
        self.status_label.setText(f"主题已切换为: {theme_name}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 清理搜索线程
        if hasattr(self, 'search_thread') and self.search_thread:
            if self.search_thread.isRunning():
                self.search_thread.terminate()
                self.search_thread.wait(1000)

        # 清理内容区域资源
        if hasattr(self, 'content_area') and self.content_area:
            self.content_area.cleanup_resources()

        self.save_window_state()
        self.config_manager.save_config()
        event.accept()
