# 主窗口模块
# 功能：应用程序主界面，包含标题栏、工具栏、侧边栏、内容区域和状态栏的完整布局

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QSplitter, QToolBar, QStatusBar, QMenuBar, QMenu,
                               QLabel, QPushButton, QLineEdit, QFrame)
from PySide6.QtCore import Qt, Signal, QSize, QTimer
from PySide6.QtGui import QIcon, QKeySequence, QPixmap, QAction

from ui.components.sidebar import SidebarWidget
from ui.components.content_area import ContentAreaWidget
from ui.components.preview_panel import PreviewPanelWidget
from ui.components.title_bar import CustomTitleBar
from ui.components.toolbar import MainToolBar
from ui.dialogs.settings_dialog import SettingsDialog
from ui.dialogs.about_dialog import AboutDialog

class MainWindow(QMainWindow):
    """主窗口类"""

    # 信号定义
    file_imported = Signal(list)  # 文件导入信号
    search_requested = Signal(str, dict)  # 搜索请求信号
    view_mode_changed = Signal(str)  # 视图模式变更信号

    def __init__(self, theme_manager, db_manager, config_manager):
        super().__init__()

        self.theme_manager = theme_manager
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 界面组件
        self.sidebar = None
        self.content_area = None
        self.preview_panel = None
        self.custom_title_bar = None
        self.main_toolbar = None
        self.splitter = None

        # 状态
        self.current_view_mode = "grid"
        self.is_preview_panel_visible = True

        self.setup_ui()
        self.setup_menu_bar()
        self.setup_status_bar()
        self.setup_connections()
        self.restore_window_state()

    def setup_ui(self):
        """设置用户界面"""
        # 设置窗口属性
        self.setWindowTitle("智能素材管理器")
        self.setMinimumSize(800, 600)

        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建自定义标题栏
        self.custom_title_bar = CustomTitleBar(self)
        main_layout.addWidget(self.custom_title_bar)

        # 创建工具栏
        self.main_toolbar = MainToolBar(self)
        main_layout.addWidget(self.main_toolbar)

        # 创建主要内容区域
        self.setup_main_content(main_layout)

    def setup_main_content(self, parent_layout):
        """设置主要内容区域"""
        # 创建水平分割器
        self.splitter = QSplitter(Qt.Horizontal)
        parent_layout.addWidget(self.splitter)

        # 创建侧边栏
        self.sidebar = SidebarWidget(
            theme_manager=self.theme_manager,
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
        self.splitter.addWidget(self.sidebar)

        # 创建内容区域和预览面板的容器
        content_container = QSplitter(Qt.Horizontal)
        self.splitter.addWidget(content_container)

        # 创建内容区域
        self.content_area = ContentAreaWidget(
            theme_manager=self.theme_manager,
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
        content_container.addWidget(self.content_area)

        # 创建预览面板
        self.preview_panel = PreviewPanelWidget(
            theme_manager=self.theme_manager,
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
        content_container.addWidget(self.preview_panel)

        # 设置分割器比例
        ui_config = self.config_manager.get_ui_config()
        sidebar_width = ui_config.get("sidebar_width", 250)
        preview_width = ui_config.get("preview_panel_width", 300)

        self.splitter.setSizes([sidebar_width, 800, preview_width])
        content_container.setSizes([500, preview_width])

        # 设置预览面板可见性
        self.is_preview_panel_visible = ui_config.get("preview_panel_visible", True)
        self.preview_panel.setVisible(self.is_preview_panel_visible)

    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 导入文件
        import_action = QAction("导入文件(&I)", self)
        import_action.setShortcut(QKeySequence.Open)
        import_action.triggered.connect(self.import_files)
        file_menu.addAction(import_action)

        # 导入文件夹
        import_folder_action = QAction("导入文件夹(&F)", self)
        import_folder_action.setShortcut(QKeySequence("Ctrl+Shift+O"))
        import_folder_action.triggered.connect(self.import_folder)
        file_menu.addAction(import_folder_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        # 全选
        select_all_action = QAction("全选(&A)", self)
        select_all_action.setShortcut(QKeySequence.SelectAll)
        select_all_action.triggered.connect(self.select_all)
        edit_menu.addAction(select_all_action)

        # 复制
        copy_action = QAction("复制(&C)", self)
        copy_action.setShortcut(QKeySequence.Copy)
        copy_action.triggered.connect(self.copy_selected)
        edit_menu.addAction(copy_action)

        edit_menu.addSeparator()

        # 删除
        delete_action = QAction("删除(&D)", self)
        delete_action.setShortcut(QKeySequence.Delete)
        delete_action.triggered.connect(self.delete_selected)
        edit_menu.addAction(delete_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")

        # 视图模式
        grid_view_action = QAction("网格视图(&G)", self)
        grid_view_action.setCheckable(True)
        grid_view_action.setChecked(True)
        grid_view_action.triggered.connect(lambda: self.set_view_mode("grid"))
        view_menu.addAction(grid_view_action)

        list_view_action = QAction("列表视图(&L)", self)
        list_view_action.setCheckable(True)
        list_view_action.triggered.connect(lambda: self.set_view_mode("list"))
        view_menu.addAction(list_view_action)

        detail_view_action = QAction("详细视图(&D)", self)
        detail_view_action.setCheckable(True)
        detail_view_action.triggered.connect(lambda: self.set_view_mode("detail"))
        view_menu.addAction(detail_view_action)

        view_menu.addSeparator()

        # 切换预览面板
        toggle_preview_action = QAction("显示/隐藏预览面板(&P)", self)
        toggle_preview_action.setShortcut(QKeySequence("F3"))
        toggle_preview_action.triggered.connect(self.toggle_preview_panel)
        view_menu.addAction(toggle_preview_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 重建索引
        rebuild_index_action = QAction("重建索引(&R)", self)
        rebuild_index_action.triggered.connect(self.rebuild_index)
        tools_menu.addAction(rebuild_index_action)

        # 清理缓存
        clear_cache_action = QAction("清理缓存(&C)", self)
        clear_cache_action.triggered.connect(self.clear_cache)
        tools_menu.addAction(clear_cache_action)

        tools_menu.addSeparator()

        # 设置
        settings_action = QAction("设置(&S)", self)
        settings_action.setShortcut(QKeySequence.Preferences)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """设置状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # 文件统计标签
        self.files_count_label = QLabel("文件: 0")
        status_bar.addWidget(self.files_count_label)

        # 选中项目标签
        self.selected_count_label = QLabel("选中: 0")
        status_bar.addWidget(self.selected_count_label)

        # 存储空间标签
        self.storage_label = QLabel("存储: 0 MB")
        status_bar.addWidget(self.storage_label)

        # 状态信息标签
        self.status_label = QLabel("就绪")
        status_bar.addPermanentWidget(self.status_label)

    def setup_connections(self):
        """设置信号连接"""
        # 工具栏信号连接
        self.main_toolbar.search_requested.connect(self.on_search_requested)
        self.main_toolbar.view_mode_changed.connect(self.set_view_mode)
        self.main_toolbar.import_requested.connect(self.import_files)

        # 侧边栏信号连接
        self.sidebar.category_selected.connect(self.on_category_selected)
        self.sidebar.tag_selected.connect(self.on_tag_selected)
        self.sidebar.color_filter_changed.connect(self.on_color_filter_changed)

        # 内容区域信号连接
        self.content_area.item_selected.connect(self.on_item_selected)
        self.content_area.items_selection_changed.connect(self.on_selection_changed)

        # 主题变更信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)

    def restore_window_state(self):
        """恢复窗口状态"""
        window_config = self.config_manager.get_window_config()

        # 恢复窗口大小和位置
        self.resize(window_config.get("width", 1200), window_config.get("height", 800))

        if window_config.get("maximized", False):
            self.showMaximized()
        else:
            pos = window_config.get("position", {"x": 100, "y": 100})
            self.move(pos["x"], pos["y"])

    def save_window_state(self):
        """保存窗口状态"""
        window_config = {
            "width": self.width(),
            "height": self.height(),
            "maximized": self.isMaximized(),
            "position": {"x": self.x(), "y": self.y()}
        }

        ui_config = self.config_manager.get_ui_config()
        ui_config.update({
            "sidebar_width": self.splitter.sizes()[0],
            "preview_panel_visible": self.is_preview_panel_visible,
            "preview_panel_width": self.splitter.sizes()[-1] if len(self.splitter.sizes()) > 2 else 300,
            "view_mode": self.current_view_mode
        })

        self.config_manager.set_window_config(window_config)
        self.config_manager.set_ui_config(ui_config)

    # 槽函数实现
    def import_files(self):
        """导入文件"""
        # TODO: 实现文件导入对话框
        pass

    def import_folder(self):
        """导入文件夹"""
        # TODO: 实现文件夹导入对话框
        pass

    def select_all(self):
        """全选"""
        if self.content_area:
            self.content_area.select_all()

    def copy_selected(self):
        """复制选中项"""
        if self.content_area:
            self.content_area.copy_selected()

    def delete_selected(self):
        """删除选中项"""
        if self.content_area:
            self.content_area.delete_selected()

    def set_view_mode(self, mode: str):
        """设置视图模式"""
        self.current_view_mode = mode
        if self.content_area:
            self.content_area.set_view_mode(mode)
        self.view_mode_changed.emit(mode)

    def toggle_preview_panel(self):
        """切换预览面板显示"""
        self.is_preview_panel_visible = not self.is_preview_panel_visible
        self.preview_panel.setVisible(self.is_preview_panel_visible)

    def rebuild_index(self):
        """重建索引"""
        # TODO: 实现索引重建
        self.status_label.setText("正在重建索引...")

    def clear_cache(self):
        """清理缓存"""
        # TODO: 实现缓存清理
        self.status_label.setText("正在清理缓存...")

    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self.config_manager, self.theme_manager, self)
        dialog.exec()

    def show_about(self):
        """显示关于对话框"""
        dialog = AboutDialog(self)
        dialog.exec()

    def on_search_requested(self, query: str, filters: dict):
        """处理搜索请求"""
        self.search_requested.emit(query, filters)

    def on_category_selected(self, category_id: int):
        """处理分类选择"""
        # TODO: 根据分类筛选内容
        pass

    def on_tag_selected(self, tag_name: str):
        """处理标签选择"""
        # TODO: 根据标签筛选内容
        pass

    def on_color_filter_changed(self, color: str):
        """处理颜色筛选"""
        # TODO: 根据颜色筛选内容
        pass

    def on_item_selected(self, item_id: int):
        """处理项目选择"""
        if self.preview_panel:
            self.preview_panel.show_item(item_id)

    def on_selection_changed(self, selected_count: int):
        """处理选择变更"""
        self.selected_count_label.setText(f"选中: {selected_count}")

    def on_theme_changed(self, theme_name: str):
        """处理主题变更"""
        # 主题已在主程序中应用，这里可以做额外处理
        self.status_label.setText(f"主题已切换为: {theme_name}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.save_window_state()
        self.config_manager.save_config()
        event.accept()
