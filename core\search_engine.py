# 搜索引擎模块
# 功能：多维度搜索、高级筛选、以图搜图、搜索历史管理等搜索功能

import re
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3

from PySide6.QtCore import QObject, Signal

class SearchEngine(QObject):
    """搜索引擎类"""
    
    # 信号定义
    search_completed = Signal(list, int)  # 搜索完成信号
    search_progress = Signal(int, int)  # 搜索进度信号
    
    def __init__(self, db_manager, config_manager):
        super().__init__()
        
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.search_config = config_manager.get_search_config()
        
        # 搜索历史
        self.search_history = []
        self.load_search_history()
        
        # 搜索索引（用于快速搜索）
        self.search_index = SearchIndex()
        
    def search(self, query: str = "", filters: Dict[str, Any] = None, 
               sort_by: str = "created_time", sort_order: str = "desc",
               limit: int = 100, offset: int = 0) -> Tuple[List[Dict[str, Any]], int]:
        """执行搜索"""
        try:
            # 预处理查询
            processed_query = self._preprocess_query(query)
            
            # 构建搜索条件
            search_conditions = self._build_search_conditions(processed_query, filters)
            
            # 执行数据库查询
            results = self.db_manager.search_materials(
                query=processed_query,
                filters=filters,
                limit=limit,
                offset=offset
            )
            
            # 获取总数
            total_count = self.db_manager.get_materials_count(processed_query, filters)
            
            # 后处理结果
            processed_results = self._postprocess_results(results, processed_query)
            
            # 保存搜索历史
            if query.strip():
                self._save_search_to_history(query, filters, len(processed_results))
                
            return processed_results, total_count
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return [], 0
            
    def advanced_search(self, criteria: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], int]:
        """高级搜索"""
        try:
            # 构建复杂查询条件
            query_parts = []
            filters = {}
            
            # 文件名搜索
            if criteria.get('filename'):
                query_parts.append(criteria['filename'])
                
            # 标签搜索
            if criteria.get('tags'):
                tag_query = ' '.join(criteria['tags'])
                query_parts.append(tag_query)
                
            # 内容搜索
            if criteria.get('content'):
                query_parts.append(criteria['content'])
                
            # 组合查询字符串
            query = ' '.join(query_parts)
            
            # 文件类型筛选
            if criteria.get('file_types'):
                filters['file_type'] = criteria['file_types']
                
            # 日期范围筛选
            if criteria.get('date_from'):
                filters['date_from'] = criteria['date_from']
            if criteria.get('date_to'):
                filters['date_to'] = criteria['date_to']
                
            # 大小范围筛选
            if criteria.get('size_min'):
                filters['size_min'] = criteria['size_min']
            if criteria.get('size_max'):
                filters['size_max'] = criteria['size_max']
                
            # 评分筛选
            if criteria.get('rating_min'):
                filters['rating_min'] = criteria['rating_min']
                
            # 收藏状态筛选
            if criteria.get('favorite_only'):
                filters['favorite'] = True
                
            # 执行搜索
            return self.search(query, filters)
            
        except Exception as e:
            print(f"高级搜索失败: {e}")
            return [], 0
            
    def search_by_image(self, image_path: str, similarity_threshold: float = 0.8) -> List[Dict[str, Any]]:
        """以图搜图"""
        try:
            # 计算目标图片的特征哈希
            from ai.ai_analyzer import SimilarityAnalyzer
            similarity_analyzer = SimilarityAnalyzer()
            
            target_hash = similarity_analyzer.calculate_similarity_hash(image_path)
            if not target_hash:
                return []
                
            # 获取所有图片素材
            all_materials = self.db_manager.search_materials(filters={'file_type': 'image'}, limit=10000)
            
            # 计算相似度
            similar_materials = []
            for material in all_materials:
                material_hash = material.get('image_hash')
                if not material_hash:
                    continue
                    
                similarity = similarity_analyzer._calculate_hash_similarity(target_hash, material_hash)
                
                if similarity >= similarity_threshold:
                    material['similarity_score'] = similarity
                    similar_materials.append(material)
                    
            # 按相似度排序
            similar_materials.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            return similar_materials
            
        except Exception as e:
            print(f"以图搜图失败: {e}")
            return []
            
    def search_by_color(self, target_color: str, tolerance: float = 0.1) -> List[Dict[str, Any]]:
        """按颜色搜索"""
        try:
            # 获取所有有颜色信息的素材
            all_materials = self.db_manager.search_materials(limit=10000)
            
            matching_materials = []
            
            for material in all_materials:
                dominant_color = material.get('dominant_color')
                if not dominant_color:
                    continue
                    
                # 计算颜色相似度
                similarity = self._calculate_color_similarity(target_color, dominant_color)
                
                if similarity >= (1.0 - tolerance):
                    material['color_similarity'] = similarity
                    matching_materials.append(material)
                    
            # 按颜色相似度排序
            matching_materials.sort(key=lambda x: x['color_similarity'], reverse=True)
            
            return matching_materials
            
        except Exception as e:
            print(f"按颜色搜索失败: {e}")
            return []
            
    def get_search_suggestions(self, partial_query: str, limit: int = 10) -> List[str]:
        """获取搜索建议"""
        try:
            suggestions = []
            
            if not partial_query.strip():
                return suggestions
                
            # 从搜索历史中获取建议
            for history_item in self.search_history:
                query = history_item.get('query', '')
                if partial_query.lower() in query.lower():
                    suggestions.append(query)
                    
            # 从标签中获取建议
            tags = self.db_manager.get_all_tags()
            for tag in tags:
                tag_name = tag['name']
                if partial_query.lower() in tag_name.lower():
                    suggestions.append(tag_name)
                    
            # 去重并限制数量
            suggestions = list(set(suggestions))[:limit]
            
            return suggestions
            
        except Exception as e:
            print(f"获取搜索建议失败: {e}")
            return []
            
    def get_search_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取搜索历史"""
        return self.search_history[:limit]
        
    def clear_search_history(self):
        """清空搜索历史"""
        self.search_history.clear()
        self._save_search_history()
        
    def _preprocess_query(self, query: str) -> str:
        """预处理查询字符串"""
        if not query:
            return ""
            
        # 去除多余空格
        query = re.sub(r'\s+', ' ', query.strip())
        
        # 如果启用模糊搜索，处理拼写错误
        if self.search_config.get('fuzzy_search', True):
            query = self._apply_fuzzy_matching(query)
            
        return query
        
    def _apply_fuzzy_matching(self, query: str) -> str:
        """应用模糊匹配"""
        # 简化实现，实际项目中可以使用更复杂的模糊匹配算法
        return query
        
    def _build_search_conditions(self, query: str, filters: Dict[str, Any]) -> Dict[str, Any]:
        """构建搜索条件"""
        conditions = {}
        
        if query:
            conditions['query'] = query
            
        if filters:
            conditions.update(filters)
            
        return conditions
        
    def _postprocess_results(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """后处理搜索结果"""
        if not query:
            return results
            
        # 计算相关性得分
        for result in results:
            score = self._calculate_relevance_score(result, query)
            result['relevance_score'] = score
            
        # 按相关性排序
        results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        
        return results
        
    def _calculate_relevance_score(self, material: Dict[str, Any], query: str) -> float:
        """计算相关性得分"""
        score = 0.0
        query_lower = query.lower()
        
        # 文件名匹配
        name = material.get('name', '').lower()
        if query_lower in name:
            score += 10.0
            if name.startswith(query_lower):
                score += 5.0
                
        # 标签匹配
        user_tags = material.get('user_tags', '')
        ai_tags = material.get('ai_tags', '')
        
        if query_lower in user_tags.lower():
            score += 8.0
        if query_lower in ai_tags.lower():
            score += 6.0
            
        # 元数据匹配
        metadata = material.get('metadata', '')
        if query_lower in metadata.lower():
            score += 3.0
            
        return score
        
    def _calculate_color_similarity(self, color1: str, color2: str) -> float:
        """计算颜色相似度"""
        try:
            # 将十六进制颜色转换为RGB
            def hex_to_rgb(hex_color):
                hex_color = hex_color.lstrip('#')
                return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                
            rgb1 = hex_to_rgb(color1)
            rgb2 = hex_to_rgb(color2)
            
            # 计算欧几里得距离
            distance = sum((a - b) ** 2 for a, b in zip(rgb1, rgb2)) ** 0.5
            
            # 转换为相似度（0-1）
            max_distance = (255 ** 2 * 3) ** 0.5
            similarity = 1.0 - (distance / max_distance)
            
            return similarity
            
        except Exception as e:
            print(f"计算颜色相似度失败: {e}")
            return 0.0
            
    def _save_search_to_history(self, query: str, filters: Dict[str, Any], result_count: int):
        """保存搜索到历史"""
        try:
            # 检查是否已存在相同查询
            for item in self.search_history:
                if item['query'] == query and item.get('filters') == filters:
                    # 更新时间和结果数
                    item['search_time'] = datetime.now().isoformat()
                    item['result_count'] = result_count
                    return
                    
            # 添加新的搜索记录
            search_record = {
                'query': query,
                'filters': filters,
                'result_count': result_count,
                'search_time': datetime.now().isoformat()
            }
            
            self.search_history.insert(0, search_record)
            
            # 限制历史记录数量
            max_history = self.search_config.get('search_history_limit', 50)
            self.search_history = self.search_history[:max_history]
            
            # 保存到文件
            self._save_search_history()
            
        except Exception as e:
            print(f"保存搜索历史失败: {e}")
            
    def load_search_history(self):
        """加载搜索历史"""
        try:
            history_file = self.config_manager.get_cache_dir() / "search_history.json"
            
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.search_history = json.load(f)
            else:
                self.search_history = []
                
        except Exception as e:
            print(f"加载搜索历史失败: {e}")
            self.search_history = []
            
    def _save_search_history(self):
        """保存搜索历史"""
        try:
            history_file = self.config_manager.get_cache_dir() / "search_history.json"
            
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.search_history, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存搜索历史失败: {e}")

class SearchIndex:
    """搜索索引类"""
    
    def __init__(self):
        self.word_index = {}  # 词汇索引
        self.tag_index = {}   # 标签索引
        
    def build_index(self, materials: List[Dict[str, Any]]):
        """构建搜索索引"""
        try:
            self.word_index.clear()
            self.tag_index.clear()
            
            for material in materials:
                material_id = material['id']
                
                # 索引文件名
                name = material.get('name', '')
                self._index_text(name, material_id, self.word_index)
                
                # 索引标签
                user_tags = material.get('user_tags', '')
                ai_tags = material.get('ai_tags', '')
                
                for tag in (user_tags + ' ' + ai_tags).split():
                    tag = tag.strip().lower()
                    if tag:
                        if tag not in self.tag_index:
                            self.tag_index[tag] = set()
                        self.tag_index[tag].add(material_id)
                        
        except Exception as e:
            print(f"构建搜索索引失败: {e}")
            
    def _index_text(self, text: str, material_id: int, index: Dict[str, set]):
        """索引文本"""
        words = re.findall(r'\w+', text.lower())
        
        for word in words:
            if word not in index:
                index[word] = set()
            index[word].add(material_id)
            
    def search_index(self, query: str) -> set:
        """在索引中搜索"""
        try:
            words = re.findall(r'\w+', query.lower())
            
            if not words:
                return set()
                
            # 获取第一个词的结果
            result_set = self.word_index.get(words[0], set()).copy()
            
            # 与其他词的结果取交集
            for word in words[1:]:
                word_results = self.word_index.get(word, set())
                result_set &= word_results
                
            return result_set
            
        except Exception as e:
            print(f"索引搜索失败: {e}")
            return set()
