#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流畅缩放测试工具
对比主界面缩放和图片查看器缩放的性能差异
"""

import sys
import time
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                               QSlider, QGroupBox, QGridLayout, QSpinBox,
                               QTabWidget, QScrollArea, QFrame)
from PySide6.QtCore import Qt, QTimer, Signal, QSize
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor

from core.smooth_scaling_manager import SmoothScalingManager
from ui.dialogs.image_viewer_dialog import ImageViewerDialog

class ScalingPerformanceTest(QWidget):
    """缩放性能测试控件"""
    
    def __init__(self):
        super().__init__()
        self.test_images = []
        self.scaling_manager = SmoothScalingManager()
        self.setup_ui()
        self.generate_test_images()
        
        # 连接信号
        self.scaling_manager.scaling_completed.connect(self.on_scaling_completed)
        self.scaling_manager.batch_scaling_completed.connect(self.on_batch_completed)
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_group = QGroupBox("🎛️ 缩放控制")
        control_layout = QGridLayout(control_group)
        
        # 缩放大小滑块
        control_layout.addWidget(QLabel("缩放大小:"), 0, 0)
        self.size_slider = QSlider(Qt.Horizontal)
        self.size_slider.setRange(50, 400)
        self.size_slider.setValue(200)
        self.size_slider.valueChanged.connect(self.on_size_changed)
        control_layout.addWidget(self.size_slider, 0, 1)
        
        self.size_label = QLabel("200px")
        control_layout.addWidget(self.size_label, 0, 2)
        
        # 测试按钮
        self.single_test_btn = QPushButton("🔍 单张测试")
        self.single_test_btn.clicked.connect(self.test_single_scaling)
        control_layout.addWidget(self.single_test_btn, 1, 0)
        
        self.batch_test_btn = QPushButton("📦 批量测试")
        self.batch_test_btn.clicked.connect(self.test_batch_scaling)
        control_layout.addWidget(self.batch_test_btn, 1, 1)
        
        self.viewer_test_btn = QPushButton("🖼️ 查看器测试")
        self.viewer_test_btn.clicked.connect(self.test_viewer_scaling)
        control_layout.addWidget(self.viewer_test_btn, 1, 2)
        
        layout.addWidget(control_group)
        
        # 测试图片显示区域
        self.scroll_area = QScrollArea()
        self.image_container = QWidget()
        self.image_layout = QGridLayout(self.image_container)
        self.scroll_area.setWidget(self.image_container)
        self.scroll_area.setWidgetResizable(True)
        layout.addWidget(self.scroll_area)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(150)
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
    
    def generate_test_images(self):
        """生成测试图片"""
        test_dir = Path("test_scaling_images")
        test_dir.mkdir(exist_ok=True)
        
        # 生成不同尺寸的测试图片
        sizes = [(800, 600), (1920, 1080), (2560, 1440), (3840, 2160)]
        colors = [QColor(255, 100, 100), QColor(100, 255, 100), 
                 QColor(100, 100, 255), QColor(255, 255, 100)]
        
        for i, ((width, height), color) in enumerate(zip(sizes, colors)):
            pixmap = QPixmap(width, height)
            pixmap.fill(color)
            
            # 绘制文字
            painter = QPainter(pixmap)
            painter.setPen(QColor(255, 255, 255))
            painter.setFont(QFont("Arial", max(20, width // 100)))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, f"测试图片 {i+1}\n{width}x{height}")
            painter.end()
            
            # 保存文件
            file_path = test_dir / f"test_{width}x{height}.png"
            pixmap.save(str(file_path))
            self.test_images.append(str(file_path))
        
        self.log(f"✅ 已生成 {len(self.test_images)} 张测试图片")
    
    def on_size_changed(self, size):
        """尺寸变化"""
        self.size_label.setText(f"{size}px")
        
        # 实时更新显示的图片
        self.update_displayed_images(size)
    
    def update_displayed_images(self, size):
        """更新显示的图片"""
        # 清理现有控件
        for i in reversed(range(self.image_layout.count())):
            item = self.image_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.deleteLater()
        
        # 创建新的图片标签
        self.image_labels = []
        for i, image_path in enumerate(self.test_images):
            label = QLabel()
            label.setFixedSize(size + 20, size + 20)
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("border: 1px solid gray; margin: 5px;")
            
            # 请求缩放
            self.scaling_manager.request_scaling(image_path, size, 'high')
            
            self.image_layout.addWidget(label, i // 2, i % 2)
            self.image_labels.append((label, image_path))
    
    def on_scaling_completed(self, file_path, target_size, scaled_pixmap):
        """缩放完成"""
        # 更新对应的标签
        for label, path in self.image_labels:
            if path == file_path:
                label.setPixmap(scaled_pixmap)
                break
    
    def on_batch_completed(self, results):
        """批量缩放完成"""
        self.log(f"📦 批量缩放完成: {len(results)} 张图片")
    
    def test_single_scaling(self):
        """测试单张缩放"""
        if not self.test_images:
            return
        
        size = self.size_slider.value()
        image_path = self.test_images[0]
        
        start_time = time.time()
        
        # 使用流畅缩放管理器
        success = self.scaling_manager.request_scaling(image_path, size, 'urgent')
        
        if success:
            # 等待完成（简单实现）
            QTimer.singleShot(100, lambda: self._check_single_completion(start_time, image_path, size))
    
    def _check_single_completion(self, start_time, image_path, size):
        """检查单张缩放完成"""
        # 检查缓存中是否有结果
        cached = self.scaling_manager.cache.get_scaled(image_path, size)
        if cached:
            elapsed = time.time() - start_time
            self.log(f"🔍 单张缩放完成: {elapsed:.3f}s")
        else:
            # 继续等待
            QTimer.singleShot(50, lambda: self._check_single_completion(start_time, image_path, size))
    
    def test_batch_scaling(self):
        """测试批量缩放"""
        size = self.size_slider.value()
        
        start_time = time.time()
        
        # 批量请求
        self.scaling_manager.request_batch_scaling(self.test_images, size, 'high')
        
        # 记录开始时间
        self.batch_start_time = start_time
        self.batch_expected_count = len(self.test_images)
        self.batch_completed_count = 0
        
        self.log(f"📦 开始批量缩放测试: {len(self.test_images)} 张图片")
    
    def test_viewer_scaling(self):
        """测试查看器缩放"""
        if not self.test_images:
            return
        
        # 打开图片查看器
        viewer = ImageViewerDialog(self.test_images[0], self)
        
        # 记录缩放性能
        original_set_scale = viewer.set_scale
        
        def timed_set_scale(scale):
            start_time = time.time()
            original_set_scale(scale)
            elapsed = time.time() - start_time
            self.log(f"🖼️ 查看器缩放: {scale:.2f} -> {elapsed:.3f}s")
        
        viewer.set_scale = timed_set_scale
        viewer.show()
        
        self.log("🖼️ 图片查看器已打开，请手动测试缩放性能")
    
    def log(self, message):
        """记录日志"""
        self.result_text.append(f"[{time.strftime('%H:%M:%S')}] {message}")

class SmoothScalingTestWindow(QMainWindow):
    """流畅缩放测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎯 流畅缩放性能测试")
        self.setGeometry(100, 100, 1000, 800)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)
        
        # 性能测试标签页
        self.performance_test = ScalingPerformanceTest()
        self.tab_widget.addTab(self.performance_test, "🎯 性能测试")
        
        # 统计信息标签页
        self.stats_widget = self.create_stats_widget()
        self.tab_widget.addTab(self.stats_widget, "📊 统计信息")
        
        # 配置标签页
        self.config_widget = self.create_config_widget()
        self.tab_widget.addTab(self.config_widget, "⚙️ 配置")
        
        # 定时更新统计
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats)
        self.stats_timer.start(1000)  # 每秒更新
    
    def create_stats_widget(self):
        """创建统计信息控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title = QLabel("📊 缩放性能统计")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 统计显示
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.stats_text)
        
        # 控制按钮
        buttons_layout = QHBoxLayout()
        
        clear_cache_btn = QPushButton("🗑️ 清空缓存")
        clear_cache_btn.clicked.connect(self.clear_cache)
        buttons_layout.addWidget(clear_cache_btn)
        
        reset_stats_btn = QPushButton("🔄 重置统计")
        reset_stats_btn.clicked.connect(self.reset_stats)
        buttons_layout.addWidget(reset_stats_btn)
        
        layout.addLayout(buttons_layout)
        
        return widget
    
    def create_config_widget(self):
        """创建配置控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 配置组
        config_group = QGroupBox("⚙️ 缩放配置")
        config_layout = QGridLayout(config_group)
        
        # 快速预览
        config_layout.addWidget(QLabel("快速预览:"), 0, 0)
        self.fast_preview_check = QPushButton("启用")
        self.fast_preview_check.setCheckable(True)
        self.fast_preview_check.setChecked(True)
        self.fast_preview_check.clicked.connect(self.update_config)
        config_layout.addWidget(self.fast_preview_check, 0, 1)
        
        # 渐进式质量
        config_layout.addWidget(QLabel("渐进式质量:"), 1, 0)
        self.progressive_check = QPushButton("启用")
        self.progressive_check.setCheckable(True)
        self.progressive_check.setChecked(True)
        self.progressive_check.clicked.connect(self.update_config)
        config_layout.addWidget(self.progressive_check, 1, 1)
        
        # 批量大小
        config_layout.addWidget(QLabel("批量大小:"), 2, 0)
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 50)
        self.batch_size_spin.setValue(10)
        self.batch_size_spin.valueChanged.connect(self.update_config)
        config_layout.addWidget(self.batch_size_spin, 2, 1)
        
        # 最大并发
        config_layout.addWidget(QLabel("最大并发:"), 3, 0)
        self.max_concurrent_spin = QSpinBox()
        self.max_concurrent_spin.setRange(1, 16)
        self.max_concurrent_spin.setValue(4)
        self.max_concurrent_spin.valueChanged.connect(self.update_config)
        config_layout.addWidget(self.max_concurrent_spin, 3, 1)
        
        layout.addWidget(config_group)
        layout.addStretch()
        
        return widget
    
    def update_stats(self):
        """更新统计信息"""
        try:
            stats = self.performance_test.scaling_manager.get_scaling_stats()
            
            stats_text = "📊 流畅缩放性能统计\n"
            stats_text += "=" * 40 + "\n\n"
            
            # 请求统计
            req_stats = stats['requests']
            stats_text += "📈 请求统计:\n"
            stats_text += f"  • 总请求数: {req_stats['total']}\n"
            stats_text += f"  • 缓存命中: {req_stats['cache_hits']}\n"
            stats_text += f"  • 命中率: {req_stats['cache_hit_rate']:.1f}%\n"
            stats_text += f"  • 快速预览: {req_stats['fast_previews']}\n"
            stats_text += f"  • 高质量渲染: {req_stats['high_quality_renders']}\n"
            stats_text += f"  • 平均时间: {req_stats['average_time']:.3f}s\n\n"
            
            # 缓存统计
            cache_stats = stats['cache']
            stats_text += "💾 缓存统计:\n"
            stats_text += f"  • 原图缓存: {cache_stats['original_count']}\n"
            stats_text += f"  • 缩放图缓存: {cache_stats['scaled_count']}\n"
            stats_text += f"  • 内存使用: {cache_stats['total_memory_mb']} MB\n\n"
            
            # 队列统计
            queue_stats = stats['queue']
            stats_text += "⏳ 队列统计:\n"
            stats_text += f"  • 等待处理: {queue_stats['pending']}\n"
            stats_text += f"  • 正在处理: {queue_stats['active']}\n"
            
            self.stats_text.setPlainText(stats_text)
            
        except Exception as e:
            self.stats_text.setPlainText(f"统计信息获取失败: {e}")
    
    def update_config(self):
        """更新配置"""
        config = {
            'fast_preview_enabled': self.fast_preview_check.isChecked(),
            'progressive_quality': self.progressive_check.isChecked(),
            'batch_size': self.batch_size_spin.value(),
            'max_concurrent': self.max_concurrent_spin.value()
        }
        
        self.performance_test.scaling_manager.set_config(config)
    
    def clear_cache(self):
        """清空缓存"""
        self.performance_test.scaling_manager.clear_cache()
        self.performance_test.log("🗑️ 缓存已清空")
    
    def reset_stats(self):
        """重置统计"""
        # 重新创建缩放管理器
        self.performance_test.scaling_manager = SmoothScalingManager()
        self.performance_test.scaling_manager.scaling_completed.connect(
            self.performance_test.on_scaling_completed
        )
        self.performance_test.scaling_manager.batch_scaling_completed.connect(
            self.performance_test.on_batch_completed
        )
        self.performance_test.log("🔄 统计已重置")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = SmoothScalingTestWindow()
    window.show()
    
    print("流畅缩放测试工具启动成功！")
    print("功能特性：")
    print("1. 🎯 性能测试 - 对比不同缩放方式的性能")
    print("2. 📊 统计信息 - 实时缩放性能统计")
    print("3. ⚙️ 配置调优 - 调整缩放参数优化性能")
    print("4. 🖼️ 查看器对比 - 与图片查看器缩放对比")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
