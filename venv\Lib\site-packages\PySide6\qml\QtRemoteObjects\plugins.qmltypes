import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qremoteobjectsqml_p.h"
        name: "QRemoteObjectAbstractPersistedStore"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtRemoteObjects/PersistedStore 5.12",
            "QtRemoteObjects/PersistedStore 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1292, 1536]
    }
    Component {
        file: "qremoteobjectnode.h"
        name: "QRemoteObjectHostBase"
        accessSemantics: "reference"
        prototype: "QRemoteObjectNode"
        Enum {
            name: "AllowedSchemas"
            values: ["BuiltInSchemasOnly", "AllowExternalRegistration"]
        }
        Method {
            name: "enableRemoting"
            type: "bool"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "enableRemoting"
            type: "bool"
            isCloned: true
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "disableRemoting"
            type: "bool"
            Parameter { name: "remoteObject"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qremoteobjectsqml_p.h"
        name: "QRemoteObjectHost"
        accessSemantics: "reference"
        prototype: "QRemoteObjectHostBase"
        exports: ["QtRemoteObjects/Host 5.15", "QtRemoteObjects/Host 6.0"]
        exportMetaObjectRevisions: [1295, 1536]
        Property {
            name: "hostUrl"
            type: "QUrl"
            read: "hostUrl"
            write: "setHostUrl"
            notify: "hostUrlChanged"
            index: 0
        }
        Signal { name: "hostUrlChanged" }
    }
    Component {
        file: "private/qremoteobjectsqml_p.h"
        name: "QRemoteObjectNode"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtRemoteObjects/Node 5.12", "QtRemoteObjects/Node 6.0"]
        exportMetaObjectRevisions: [1292, 1536]
        Enum {
            name: "ErrorCode"
            values: [
                "NoError",
                "RegistryNotAcquired",
                "RegistryAlreadyHosted",
                "NodeIsNoServer",
                "ServerAlreadyCreated",
                "UnintendedRegistryHosting",
                "OperationNotValidOnClientNode",
                "SourceNotRegistered",
                "MissingObjectName",
                "HostUrlInvalid",
                "ProtocolMismatch",
                "ListenFailed",
                "SocketAccessError"
            ]
        }
        Property {
            name: "registryUrl"
            type: "QUrl"
            read: "registryUrl"
            write: "setRegistryUrl"
            index: 0
        }
        Property {
            name: "persistedStore"
            type: "QRemoteObjectAbstractPersistedStore"
            isPointer: true
            read: "persistedStore"
            write: "setPersistedStore"
            index: 1
        }
        Property {
            name: "heartbeatInterval"
            type: "int"
            read: "heartbeatInterval"
            write: "setHeartbeatInterval"
            notify: "heartbeatIntervalChanged"
            index: 2
        }
        Signal {
            name: "remoteObjectAdded"
            Parameter { type: "QRemoteObjectSourceLocation" }
        }
        Signal {
            name: "remoteObjectRemoved"
            Parameter { type: "QRemoteObjectSourceLocation" }
        }
        Signal {
            name: "error"
            Parameter { name: "errorCode"; type: "QRemoteObjectNode::ErrorCode" }
        }
        Signal {
            name: "heartbeatIntervalChanged"
            Parameter { name: "heartbeatInterval"; type: "int" }
        }
        Method {
            name: "connectToNode"
            type: "bool"
            Parameter { name: "address"; type: "QUrl" }
        }
    }
    Component {
        file: "private/qremoteobjectsqml_p.h"
        name: "QRemoteObjectSettingsStore"
        accessSemantics: "reference"
        prototype: "QRemoteObjectAbstractPersistedStore"
        exports: [
            "QtRemoteObjects/SettingsStore 5.12",
            "QtRemoteObjects/SettingsStore 6.0"
        ]
        exportMetaObjectRevisions: [1292, 1536]
    }
    Component {
        file: "private/qremoteobjectsqml_p.h"
        name: "QtQmlRemoteObjects"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtRemoteObjects/QtRemoteObjects 5.14",
            "QtRemoteObjects/QtRemoteObjects 6.0"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [1294, 1536]
        Method {
            name: "watch"
            type: "QJSValue"
            Parameter { name: "reply"; type: "QRemoteObjectPendingCall" }
            Parameter { name: "timeout"; type: "int" }
        }
        Method {
            name: "watch"
            type: "QJSValue"
            isCloned: true
            Parameter { name: "reply"; type: "QRemoteObjectPendingCall" }
        }
    }
}
