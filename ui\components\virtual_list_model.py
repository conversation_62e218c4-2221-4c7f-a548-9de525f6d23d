#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟列表模型
实现高性能的大数据集显示，支持懒加载和虚拟滚动
"""

from PySide6.QtCore import QAbstractListModel, Qt, QModelIndex, Signal, QTimer, QThread
from PySide6.QtGui import QPixmap, QIcon
from typing import List, Dict, Any, Optional
import threading
import time
from collections import OrderedDict

class VirtualListModel(QAbstractListModel):
    """虚拟列表模型 - 高性能大数据集支持"""
    
    # 信号定义
    data_loading = Signal(int, int)  # 开始加载数据
    data_loaded = Signal(int, int)   # 数据加载完成
    thumbnail_ready = Signal(int, QPixmap)  # 缩略图就绪
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 数据存储
        self._data_source = []  # 完整数据源
        self._visible_cache = OrderedDict()  # 可见项缓存
        self._thumbnail_cache = OrderedDict()  # 缩略图缓存
        
        # 性能参数
        self.cache_size = 200  # 缓存大小
        self.preload_distance = 50  # 预加载距离
        self.batch_size = 20  # 批量加载大小
        
        # 状态管理
        self._loading_ranges = set()  # 正在加载的范围
        self._last_visible_range = (0, 0)  # 上次可见范围
        
        # 延迟加载定时器
        self.load_timer = QTimer()
        self.load_timer.setSingleShot(True)
        self.load_timer.timeout.connect(self._perform_delayed_load)
        
        # 缩略图加载线程
        self.thumbnail_loader = ThumbnailLoaderThread()
        self.thumbnail_loader.thumbnail_loaded.connect(self._on_thumbnail_loaded)
        self.thumbnail_loader.start()
    
    def setDataSource(self, data: List[Dict[str, Any]]):
        """设置数据源"""
        self.beginResetModel()
        self._data_source = data
        self._visible_cache.clear()
        self._thumbnail_cache.clear()
        self._loading_ranges.clear()
        self.endResetModel()
        
        # 预加载前几项
        if data:
            self._request_data_load(0, min(self.batch_size, len(data)))
    
    def rowCount(self, parent=QModelIndex()) -> int:
        """返回行数"""
        return len(self._data_source)
    
    def data(self, index: QModelIndex, role: int = Qt.DisplayRole) -> Any:
        """获取数据"""
        if not index.isValid() or index.row() >= len(self._data_source):
            return None
        
        row = index.row()
        
        # 触发可见范围更新
        self._update_visible_range(row)
        
        # 从缓存获取数据
        if row in self._visible_cache:
            item_data = self._visible_cache[row]
        else:
            # 返回占位符数据，同时触发加载
            item_data = self._get_placeholder_data(row)
            self._request_data_load(row, row + 1)
        
        # 根据角色返回数据
        if role == Qt.DisplayRole:
            return item_data.get('name', f'Item {row}')
        elif role == Qt.DecorationRole:
            return self._get_thumbnail(row)
        elif role == Qt.UserRole:
            return item_data
        elif role == Qt.SizeHintRole:
            return self._get_item_size()
        
        return None
    
    def _get_placeholder_data(self, row: int) -> Dict[str, Any]:
        """获取占位符数据"""
        if row < len(self._data_source):
            return {
                'name': self._data_source[row].get('name', f'Loading... {row}'),
                'loading': True
            }
        return {'name': 'Loading...', 'loading': True}
    
    def _get_thumbnail(self, row: int) -> Optional[QIcon]:
        """获取缩略图"""
        if row in self._thumbnail_cache:
            pixmap = self._thumbnail_cache[row]
            return QIcon(pixmap)
        else:
            # 请求加载缩略图
            if row < len(self._data_source):
                file_path = self._data_source[row].get('file_path')
                if file_path:
                    self.thumbnail_loader.request_thumbnail(row, file_path)
            
            # 返回默认图标
            return self._get_default_icon(row)
    
    def _get_default_icon(self, row: int) -> QIcon:
        """获取默认图标"""
        # 根据文件类型返回不同的默认图标
        if row < len(self._data_source):
            file_type = self._data_source[row].get('file_type', 'unknown')
            icon_map = {
                'image': '🖼️',
                'video': '🎬', 
                'audio': '🎵',
                'document': '📄',
                'other': '📁'
            }
            icon_text = icon_map.get(file_type, '📁')
            
            # 创建文本图标
            pixmap = QPixmap(64, 64)
            pixmap.fill(Qt.transparent)
            return QIcon(pixmap)
        
        return QIcon()
    
    def _get_item_size(self):
        """获取项目大小"""
        from PySide6.QtCore import QSize
        return QSize(150, 180)  # 固定大小，提升性能
    
    def _update_visible_range(self, current_row: int):
        """更新可见范围"""
        # 计算新的可见范围
        start = max(0, current_row - self.preload_distance)
        end = min(len(self._data_source), current_row + self.preload_distance)
        
        new_range = (start, end)
        
        # 如果范围变化，触发预加载
        if new_range != self._last_visible_range:
            self._last_visible_range = new_range
            self._request_data_load(start, end)
    
    def _request_data_load(self, start: int, end: int):
        """请求数据加载"""
        # 检查是否已在加载
        load_range = (start, end)
        if load_range in self._loading_ranges:
            return
        
        self._loading_ranges.add(load_range)
        
        # 延迟加载，避免频繁触发
        self.load_timer.stop()
        self.load_timer.start(50)  # 50ms延迟
    
    def _perform_delayed_load(self):
        """执行延迟加载"""
        # 合并加载范围
        if not self._loading_ranges:
            return
        
        # 找到最大范围
        min_start = min(r[0] for r in self._loading_ranges)
        max_end = max(r[1] for r in self._loading_ranges)
        
        # 清空加载范围
        self._loading_ranges.clear()
        
        # 批量加载数据
        self._load_data_batch(min_start, max_end)
    
    def _load_data_batch(self, start: int, end: int):
        """批量加载数据"""
        self.data_loading.emit(start, end)
        
        # 分批处理，避免UI阻塞
        batch_start = start
        while batch_start < end:
            batch_end = min(batch_start + self.batch_size, end)
            
            # 加载这一批数据
            for i in range(batch_start, batch_end):
                if i < len(self._data_source) and i not in self._visible_cache:
                    # 复制数据到缓存
                    self._visible_cache[i] = self._data_source[i].copy()
            
            batch_start = batch_end
        
        # 清理过期缓存
        self._cleanup_cache()
        
        # 通知数据变更
        self.dataChanged.emit(
            self.index(start), 
            self.index(min(end - 1, len(self._data_source) - 1))
        )
        
        self.data_loaded.emit(start, end)
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        # 清理可见项缓存
        while len(self._visible_cache) > self.cache_size:
            self._visible_cache.popitem(last=False)
        
        # 清理缩略图缓存
        while len(self._thumbnail_cache) > self.cache_size:
            self._thumbnail_cache.popitem(last=False)
    
    def _on_thumbnail_loaded(self, row: int, pixmap: QPixmap):
        """缩略图加载完成"""
        self._thumbnail_cache[row] = pixmap
        
        # 通知视图更新
        model_index = self.index(row)
        self.dataChanged.emit(model_index, model_index, [Qt.DecorationRole])
        
        self.thumbnail_ready.emit(row, pixmap)
    
    def invalidateCache(self):
        """清空缓存"""
        self._visible_cache.clear()
        self._thumbnail_cache.clear()
        self._loading_ranges.clear()
    
    def prefetchData(self, start: int, count: int):
        """预取数据"""
        end = min(start + count, len(self._data_source))
        self._request_data_load(start, end)

class ThumbnailLoaderThread(QThread):
    """缩略图加载线程"""
    
    thumbnail_loaded = Signal(int, QPixmap)
    
    def __init__(self):
        super().__init__()
        self.request_queue = []
        self.queue_lock = threading.Lock()
        self.running = True
    
    def request_thumbnail(self, row: int, file_path: str):
        """请求加载缩略图"""
        with self.queue_lock:
            # 避免重复请求
            for existing_row, _ in self.request_queue:
                if existing_row == row:
                    return
            
            self.request_queue.append((row, file_path))
    
    def run(self):
        """线程运行"""
        while self.running:
            # 获取请求
            with self.queue_lock:
                if not self.request_queue:
                    time.sleep(0.1)
                    continue
                
                row, file_path = self.request_queue.pop(0)
            
            # 加载缩略图
            try:
                pixmap = self._load_thumbnail(file_path)
                if not pixmap.isNull():
                    self.thumbnail_loaded.emit(row, pixmap)
            except Exception as e:
                print(f"加载缩略图失败 {file_path}: {e}")
    
    def _load_thumbnail(self, file_path: str) -> QPixmap:
        """加载缩略图"""
        try:
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                # 快速缩放
                return pixmap.scaled(
                    128, 128, 
                    Qt.KeepAspectRatio, 
                    Qt.FastTransformation
                )
        except Exception as e:
            print(f"缩略图加载错误: {e}")
        
        return QPixmap()
    
    def stop(self):
        """停止线程"""
        self.running = False
        self.wait(3000)
