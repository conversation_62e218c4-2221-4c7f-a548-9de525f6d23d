#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误修复验证工具
验证启动画面、流畅缩放、缩略图加载器等错误修复
"""

import sys
import time
import gc
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPixmap

class ErrorFixVerificationWindow(QMainWindow):
    """错误修复验证窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 错误修复验证工具")
        self.setGeometry(100, 100, 800, 600)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔧 错误修复验证工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 测试按钮
        splash_btn = QPushButton("🎬 测试启动画面修复")
        splash_btn.clicked.connect(self.test_splash_screen_fix)
        layout.addWidget(splash_btn)
        
        scaling_btn = QPushButton("📏 测试流畅缩放修复")
        scaling_btn.clicked.connect(self.test_smooth_scaling_fix)
        layout.addWidget(scaling_btn)
        
        thumbnail_btn = QPushButton("🖼️ 测试缩略图加载器修复")
        thumbnail_btn.clicked.connect(self.test_thumbnail_loader_fix)
        layout.addWidget(thumbnail_btn)
        
        comprehensive_btn = QPushButton("🧪 综合测试")
        comprehensive_btn.clicked.connect(self.test_comprehensive)
        layout.addWidget(comprehensive_btn)
        
        gc_btn = QPushButton("🗑️ 强制垃圾回收")
        gc_btn.clicked.connect(self.force_gc)
        layout.addWidget(gc_btn)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.result_text)
    
    def log(self, message):
        """记录日志"""
        self.result_text.append(f"[{time.strftime('%H:%M:%S')}] {message}")
        QApplication.processEvents()
    
    def test_splash_screen_fix(self):
        """测试启动画面修复"""
        self.log("🎬 开始测试启动画面修复...")
        
        try:
            from core.startup_optimizer import AdvancedSplashScreen
            from PySide6.QtGui import QPixmap, QPainter, QColor
            
            # 创建测试图片
            pixmap = QPixmap(400, 300)
            pixmap.fill(QColor(240, 248, 255))
            
            painter = QPainter(pixmap)
            painter.setPen(QColor(0, 120, 204))
            painter.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "测试启动画面")
            painter.end()
            
            # 创建启动画面
            splash = AdvancedSplashScreen(pixmap)
            self.log("✅ 启动画面创建成功")
            
            # 显示启动画面
            splash.show()
            self.log("✅ 启动画面显示成功")
            
            # 测试进度更新
            for i in range(0, 101, 20):
                splash.update_progress(i, f"测试进度 {i}%")
                QApplication.processEvents()
                time.sleep(0.1)
            
            self.log("✅ 启动画面进度更新成功")
            
            # 关闭启动画面
            splash.close()
            self.log("✅ 启动画面关闭成功")
            
            self.log("🎉 启动画面修复验证通过！")
            
        except Exception as e:
            self.log(f"❌ 启动画面测试失败: {e}")
            import traceback
            self.log(traceback.format_exc())
    
    def test_smooth_scaling_fix(self):
        """测试流畅缩放修复"""
        self.log("📏 开始测试流畅缩放修复...")
        
        try:
            from core.smooth_scaling_manager import SmoothScalingManager
            
            # 创建缩放管理器
            scaling_manager = SmoothScalingManager()
            self.log("✅ 流畅缩放管理器创建成功")
            
            # 创建测试图片
            test_dir = Path("test_scaling")
            test_dir.mkdir(exist_ok=True)
            
            pixmap = QPixmap(1920, 1080)
            pixmap.fill(QColor(100, 150, 200))
            
            painter = QPainter(pixmap)
            painter.setPen(QColor(255, 255, 255))
            painter.setFont(QFont("Arial", 48))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "测试缩放图片\n1920x1080")
            painter.end()
            
            test_file = test_dir / "test_scaling.png"
            pixmap.save(str(test_file))
            self.log("✅ 测试图片创建成功")
            
            # 测试缩放请求
            success = scaling_manager.request_scaling(str(test_file), 200, 'urgent')
            if success:
                self.log("✅ 缩放请求提交成功")
            else:
                self.log("❌ 缩放请求提交失败")
            
            # 测试批量缩放
            test_files = [str(test_file)] * 3
            scaling_manager.request_batch_scaling(test_files, 150, 'normal')
            self.log("✅ 批量缩放请求提交成功")
            
            # 测试统计信息
            stats = scaling_manager.get_scaling_stats()
            self.log(f"✅ 统计信息获取成功: {stats['requests']['total']} 个请求")
            
            # 清理缓存
            scaling_manager.clear_cache()
            self.log("✅ 缓存清理成功")
            
            self.log("🎉 流畅缩放修复验证通过！")
            
        except Exception as e:
            self.log(f"❌ 流畅缩放测试失败: {e}")
            import traceback
            self.log(traceback.format_exc())
    
    def test_thumbnail_loader_fix(self):
        """测试缩略图加载器修复"""
        self.log("🖼️ 开始测试缩略图加载器修复...")
        
        try:
            from ui.components.content_area import HighPerformanceThumbnailLoader
            
            # 创建加载器
            loader = HighPerformanceThumbnailLoader()
            self.log("✅ 缩略图加载器创建成功")
            
            # 检查资源管理标志
            if hasattr(loader, '_is_destroyed'):
                self.log("✅ 资源管理标志存在")
            else:
                self.log("❌ 资源管理标志缺失")
            
            # 启动加载器
            loader.start()
            self.log("✅ 加载器启动成功")
            
            # 等待一下
            time.sleep(0.5)
            
            # 测试安全清理
            loader.safe_cleanup()
            self.log("✅ 安全清理完成")
            
            # 测试重复清理
            try:
                loader.safe_cleanup()
                self.log("✅ 重复清理不会出错")
            except Exception as e:
                self.log(f"❌ 重复清理出错: {e}")
            
            # 测试析构
            del loader
            self.log("✅ 析构函数调用完成")
            
            self.log("🎉 缩略图加载器修复验证通过！")
            
        except Exception as e:
            self.log(f"❌ 缩略图加载器测试失败: {e}")
            import traceback
            self.log(traceback.format_exc())
    
    def test_comprehensive(self):
        """综合测试"""
        self.log("🧪 开始综合测试...")
        
        # 测试所有组件
        self.test_splash_screen_fix()
        self.test_smooth_scaling_fix()
        self.test_thumbnail_loader_fix()
        
        # 测试组件交互
        self.log("\n🔄 测试组件交互...")
        
        try:
            # 同时创建多个组件
            from core.startup_optimizer import get_startup_optimizer
            from core.smooth_scaling_manager import get_smooth_scaling_manager
            from core.intelligent_cache import get_cache_manager
            
            startup_opt = get_startup_optimizer()
            scaling_mgr = get_smooth_scaling_manager()
            cache_mgr = get_cache_manager()
            
            self.log("✅ 所有核心组件创建成功")
            
            # 测试组件状态
            scaling_stats = scaling_mgr.get_scaling_stats()
            cache_stats = cache_mgr.get_stats()
            
            self.log(f"✅ 缩放管理器状态正常: {scaling_stats['requests']['total']} 请求")
            self.log(f"✅ 缓存管理器状态正常: {cache_stats['global']['hits']} 命中")
            
            # 清理组件
            from core.startup_optimizer import cleanup_startup_optimizer
            from core.smooth_scaling_manager import cleanup_smooth_scaling_manager
            from core.intelligent_cache import cleanup_cache_manager
            
            cleanup_startup_optimizer()
            cleanup_smooth_scaling_manager()
            cleanup_cache_manager()
            
            self.log("✅ 所有组件清理完成")
            
            self.log("🎉 综合测试验证通过！")
            
        except Exception as e:
            self.log(f"❌ 综合测试失败: {e}")
            import traceback
            self.log(traceback.format_exc())
    
    def force_gc(self):
        """强制垃圾回收"""
        self.log("🗑️ 执行强制垃圾回收...")
        
        # 执行垃圾回收
        collected = gc.collect()
        self.log(f"✅ 垃圾回收完成，回收了 {collected} 个对象")
        
        # 显示垃圾回收统计
        stats = gc.get_stats()
        for i, stat in enumerate(stats):
            self.log(f"  代 {i}: 收集次数={stat['collections']}, 对象数={stat.get('collected', 0)}")
        
        # 显示当前对象数量
        object_count = len(gc.get_objects())
        self.log(f"  当前对象总数: {object_count}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = ErrorFixVerificationWindow()
    window.show()
    
    print("错误修复验证工具启动成功！")
    print("功能特性：")
    print("1. 🎬 启动画面修复验证")
    print("2. 📏 流畅缩放修复验证")
    print("3. 🖼️ 缩略图加载器修复验证")
    print("4. 🧪 综合测试验证")
    print("5. 🗑️ 垃圾回收测试")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
