# 自定义标题栏组件
# 功能：自定义标题栏，包含应用图标、标题、主题切换按钮和窗口控制按钮

from PySide6.QtWidgets import (QWidget, QHBoxLayout, QLabel, QPushButton, 
                               QFrame, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QPoint
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont, QMouseEvent

class CustomTitleBar(QWidget):
    """自定义标题栏控件类"""
    
    # 信号定义
    minimize_clicked = Signal()
    maximize_clicked = Signal()
    close_clicked = Signal()
    theme_switch_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.parent_window = parent
        self.drag_position = QPoint()
        self.is_dragging = False
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        # 设置固定高度
        self.setFixedHeight(40)
        
        # 创建主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 0, 5, 0)
        main_layout.setSpacing(10)
        
        # 应用图标和标题
        self.create_title_section(main_layout)
        
        # 中间弹性空间
        main_layout.addStretch()
        
        # 主题切换按钮
        self.create_theme_button(main_layout)
        
        # 窗口控制按钮
        self.create_window_controls(main_layout)
        
        # 设置样式
        self.setStyleSheet("""
            CustomTitleBar {
                background-color: #2c3e50;
                border-bottom: 1px solid #34495e;
            }
        """)
        
    def create_title_section(self, layout):
        """创建标题区域"""
        # 应用图标
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(24, 24)
        self.icon_label.setAlignment(Qt.AlignCenter)
        
        # 设置默认图标
        self.icon_label.setText("📁")
        self.icon_label.setFont(QFont("", 16))
        
        layout.addWidget(self.icon_label)
        
        # 应用标题
        self.title_label = QLabel("智能素材管理器")
        self.title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        self.title_label.setStyleSheet("color: #ffffff;")
        layout.addWidget(self.title_label)
        
    def create_theme_button(self, layout):
        """创建主题切换按钮"""
        self.theme_button = TitleBarButton("🌙", "切换主题")
        self.theme_button.clicked.connect(self.theme_switch_clicked.emit)
        layout.addWidget(self.theme_button)
        
    def create_window_controls(self, layout):
        """创建窗口控制按钮"""
        # 最小化按钮
        self.minimize_button = WindowControlButton("minimize")
        self.minimize_button.clicked.connect(self.on_minimize_clicked)
        layout.addWidget(self.minimize_button)
        
        # 最大化/还原按钮
        self.maximize_button = WindowControlButton("maximize")
        self.maximize_button.clicked.connect(self.on_maximize_clicked)
        layout.addWidget(self.maximize_button)
        
        # 关闭按钮
        self.close_button = WindowControlButton("close")
        self.close_button.clicked.connect(self.on_close_clicked)
        layout.addWidget(self.close_button)
        
    def setup_connections(self):
        """设置信号连接"""
        if self.parent_window:
            self.minimize_clicked.connect(self.parent_window.showMinimized)
            self.maximize_clicked.connect(self.toggle_maximize)
            self.close_clicked.connect(self.parent_window.close)
            
    def set_title(self, title: str):
        """设置标题"""
        self.title_label.setText(title)
        
    def set_icon(self, icon_path: str):
        """设置图标"""
        try:
            pixmap = QPixmap(icon_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(24, 24, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.icon_label.setPixmap(scaled_pixmap)
        except Exception as e:
            print(f"设置图标失败: {e}")
            
    def update_theme_button(self, is_dark_theme: bool):
        """更新主题按钮"""
        if is_dark_theme:
            self.theme_button.setText("☀️")
            self.theme_button.setToolTip("切换到浅色主题")
        else:
            self.theme_button.setText("🌙")
            self.theme_button.setToolTip("切换到深色主题")
            
    def toggle_maximize(self):
        """切换最大化状态"""
        if self.parent_window:
            if self.parent_window.isMaximized():
                self.parent_window.showNormal()
                self.maximize_button.set_button_type("maximize")
            else:
                self.parent_window.showMaximized()
                self.maximize_button.set_button_type("restore")
                
    def on_minimize_clicked(self):
        """最小化按钮点击处理"""
        self.minimize_clicked.emit()
        
    def on_maximize_clicked(self):
        """最大化按钮点击处理"""
        self.maximize_clicked.emit()
        
    def on_close_clicked(self):
        """关闭按钮点击处理"""
        self.close_clicked.emit()
        
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            event.accept()
            
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self.is_dragging and event.buttons() == Qt.LeftButton:
            if self.parent_window and not self.parent_window.isMaximized():
                self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
            
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            event.accept()
            
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件"""
        if event.button() == Qt.LeftButton:
            self.toggle_maximize()
            event.accept()

class TitleBarButton(QPushButton):
    """标题栏按钮基类"""
    
    def __init__(self, text: str, tooltip: str = ""):
        super().__init__(text)
        
        self.setFixedSize(30, 30)
        self.setToolTip(tooltip)
        
        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 15px;
                color: #ffffff;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)

class WindowControlButton(QPushButton):
    """窗口控制按钮类"""
    
    def __init__(self, button_type: str):
        super().__init__()
        
        self.button_type = button_type
        self.setFixedSize(30, 30)
        
        # 设置按钮内容
        self.set_button_type(button_type)
        
        # 设置基础样式
        self.setStyleSheet(self.get_base_style())
        
    def set_button_type(self, button_type: str):
        """设置按钮类型"""
        self.button_type = button_type
        
        if button_type == "minimize":
            self.setText("−")
            self.setToolTip("最小化")
        elif button_type == "maximize":
            self.setText("□")
            self.setToolTip("最大化")
        elif button_type == "restore":
            self.setText("⧉")
            self.setToolTip("还原")
        elif button_type == "close":
            self.setText("×")
            self.setToolTip("关闭")
            
    def get_base_style(self) -> str:
        """获取基础样式"""
        if self.button_type == "close":
            return """
                QPushButton {
                    background-color: transparent;
                    border: none;
                    border-radius: 15px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e74c3c;
                }
                QPushButton:pressed {
                    background-color: #c0392b;
                }
            """
        else:
            return """
                QPushButton {
                    background-color: transparent;
                    border: none;
                    border-radius: 15px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }
                QPushButton:pressed {
                    background-color: rgba(255, 255, 255, 0.2);
                }
            """
            
    def paintEvent(self, event):
        """绘制事件"""
        super().paintEvent(event)
        
        # 可以在这里添加自定义绘制逻辑
        # 比如绘制更精美的图标
