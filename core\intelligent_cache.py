#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存系统
实现多层缓存、预测性缓存、自适应缓存策略等高级功能
"""

import time
import threading
import pickle
import hashlib
import sqlite3
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Callable
from collections import OrderedDict, defaultdict
import weakref
import gc

class CacheStats:
    """缓存统计信息"""
    
    def __init__(self):
        self.hits = 0
        self.misses = 0
        self.evictions = 0
        self.size = 0
        self.memory_usage = 0
        self.start_time = time.time()
    
    @property
    def hit_ratio(self) -> float:
        """缓存命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def uptime(self) -> float:
        """运行时间"""
        return time.time() - self.start_time
    
    def reset(self):
        """重置统计"""
        self.hits = 0
        self.misses = 0
        self.evictions = 0
        self.start_time = time.time()

class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.stats = CacheStats()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                self.stats.hits += 1
                return value
            else:
                self.stats.misses += 1
                return None
    
    def put(self, key: str, value: Any):
        """添加缓存项"""
        with self.lock:
            if key in self.cache:
                # 更新现有项
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # 移除最旧的项
                self.cache.popitem(last=False)
                self.stats.evictions += 1
            
            self.cache[key] = value
            self.stats.size = len(self.cache)
    
    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                self.stats.size = len(self.cache)
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.stats.size = 0
    
    def keys(self) -> List[str]:
        """获取所有键"""
        with self.lock:
            return list(self.cache.keys())

class PersistentCache:
    """持久化缓存"""
    
    def __init__(self, cache_dir: Path, max_size_mb: int = 100):
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        
        # 初始化数据库
        self.db_path = self.cache_dir / "cache_index.db"
        self._init_database()
        
        self.lock = threading.RLock()
    
    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache_index (
                    key TEXT PRIMARY KEY,
                    file_path TEXT NOT NULL,
                    size INTEGER NOT NULL,
                    created_time REAL NOT NULL,
                    access_time REAL NOT NULL,
                    access_count INTEGER DEFAULT 1
                )
            """)
            conn.execute("CREATE INDEX IF NOT EXISTS idx_access_time ON cache_index(access_time)")
    
    def _get_cache_file_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute(
                        "SELECT file_path FROM cache_index WHERE key = ?", (key,)
                    )
                    row = cursor.fetchone()
                    
                    if row:
                        file_path = Path(row[0])
                        if file_path.exists():
                            # 更新访问时间和次数
                            conn.execute("""
                                UPDATE cache_index 
                                SET access_time = ?, access_count = access_count + 1 
                                WHERE key = ?
                            """, (time.time(), key))
                            
                            # 读取缓存数据
                            with open(file_path, 'rb') as f:
                                return pickle.load(f)
                        else:
                            # 文件不存在，清理索引
                            conn.execute("DELETE FROM cache_index WHERE key = ?", (key,))
                
                return None
                
            except Exception as e:
                print(f"读取持久化缓存失败: {e}")
                return None
    
    def put(self, key: str, value: Any):
        """添加缓存项"""
        with self.lock:
            try:
                file_path = self._get_cache_file_path(key)
                
                # 序列化数据
                with open(file_path, 'wb') as f:
                    pickle.dump(value, f)
                
                file_size = file_path.stat().st_size
                current_time = time.time()
                
                # 更新索引
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT OR REPLACE INTO cache_index 
                        (key, file_path, size, created_time, access_time) 
                        VALUES (?, ?, ?, ?, ?)
                    """, (key, str(file_path), file_size, current_time, current_time))
                
                # 检查缓存大小限制
                self._cleanup_if_needed()
                
            except Exception as e:
                print(f"写入持久化缓存失败: {e}")
    
    def _cleanup_if_needed(self):
        """根据需要清理缓存"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 计算总大小
                cursor = conn.execute("SELECT SUM(size) FROM cache_index")
                total_size = cursor.fetchone()[0] or 0
                
                if total_size > self.max_size_bytes:
                    # 删除最旧的缓存项
                    cursor = conn.execute("""
                        SELECT key, file_path FROM cache_index 
                        ORDER BY access_time ASC 
                        LIMIT ?
                    """, (max(1, int(total_size * 0.2 / self.max_size_bytes)),))
                    
                    for key, file_path in cursor.fetchall():
                        # 删除文件
                        try:
                            Path(file_path).unlink(missing_ok=True)
                        except:
                            pass
                        
                        # 删除索引
                        conn.execute("DELETE FROM cache_index WHERE key = ?", (key,))
                        
        except Exception as e:
            print(f"清理持久化缓存失败: {e}")
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("SELECT file_path FROM cache_index")
                    for (file_path,) in cursor.fetchall():
                        try:
                            Path(file_path).unlink(missing_ok=True)
                        except:
                            pass
                    
                    conn.execute("DELETE FROM cache_index")
                    
            except Exception as e:
                print(f"清空持久化缓存失败: {e}")

class PredictiveCache:
    """预测性缓存"""
    
    def __init__(self, base_cache: LRUCache):
        self.base_cache = base_cache
        self.access_patterns = defaultdict(list)
        self.prediction_model = {}
        self.lock = threading.RLock()
        
        # 预测参数
        self.pattern_window = 10  # 模式窗口大小
        self.prediction_threshold = 0.7  # 预测阈值
    
    def record_access(self, key: str):
        """记录访问模式"""
        with self.lock:
            current_time = time.time()
            self.access_patterns[key].append(current_time)
            
            # 保持窗口大小
            if len(self.access_patterns[key]) > self.pattern_window:
                self.access_patterns[key] = self.access_patterns[key][-self.pattern_window:]
            
            # 更新预测模型
            self._update_prediction_model(key)
    
    def _update_prediction_model(self, key: str):
        """更新预测模型"""
        accesses = self.access_patterns[key]
        if len(accesses) < 3:
            return
        
        # 计算访问间隔
        intervals = []
        for i in range(1, len(accesses)):
            intervals.append(accesses[i] - accesses[i-1])
        
        # 简单的预测：平均间隔
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            next_access_time = accesses[-1] + avg_interval
            
            self.prediction_model[key] = {
                'next_access_time': next_access_time,
                'confidence': min(1.0, len(intervals) / self.pattern_window)
            }
    
    def get_predictions(self) -> List[Tuple[str, float]]:
        """获取预测结果"""
        with self.lock:
            current_time = time.time()
            predictions = []
            
            for key, model in self.prediction_model.items():
                if model['confidence'] >= self.prediction_threshold:
                    time_diff = model['next_access_time'] - current_time
                    if 0 <= time_diff <= 300:  # 5分钟内
                        predictions.append((key, model['confidence']))
            
            return sorted(predictions, key=lambda x: x[1], reverse=True)
    
    def preload_predicted_items(self, loader_func: Callable[[str], Any]):
        """预加载预测的项目"""
        predictions = self.get_predictions()
        
        for key, confidence in predictions[:5]:  # 最多预加载5个
            if self.base_cache.get(key) is None:
                try:
                    value = loader_func(key)
                    if value is not None:
                        self.base_cache.put(key, value)
                except Exception as e:
                    print(f"预加载失败 {key}: {e}")

class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, cache_dir: Optional[Path] = None):
        if cache_dir is None:
            cache_dir = Path.home() / ".smart_asset_manager" / "cache"
        
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 多层缓存
        self.memory_cache = LRUCache(max_size=1000)
        self.persistent_cache = PersistentCache(cache_dir, max_size_mb=200)
        self.predictive_cache = PredictiveCache(self.memory_cache)
        
        # 缓存策略
        self.strategies = {
            'thumbnails': {'ttl': 3600, 'persistent': True},
            'search_results': {'ttl': 300, 'persistent': False},
            'file_metadata': {'ttl': 1800, 'persistent': True},
            'ui_state': {'ttl': 86400, 'persistent': True}
        }
        
        # 统计信息
        self.global_stats = CacheStats()
        
        # 清理定时器
        self.cleanup_timer = threading.Timer(300, self._periodic_cleanup)
        self.cleanup_timer.daemon = True
        self.cleanup_timer.start()
    
    def get(self, key: str, category: str = 'default') -> Optional[Any]:
        """获取缓存项"""
        # 记录访问
        self.predictive_cache.record_access(key)
        
        # 首先尝试内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            self.global_stats.hits += 1
            return value
        
        # 然后尝试持久化缓存
        strategy = self.strategies.get(category, {})
        if strategy.get('persistent', False):
            value = self.persistent_cache.get(key)
            if value is not None:
                # 回填到内存缓存
                self.memory_cache.put(key, value)
                self.global_stats.hits += 1
                return value
        
        self.global_stats.misses += 1
        return None
    
    def put(self, key: str, value: Any, category: str = 'default'):
        """添加缓存项"""
        # 添加到内存缓存
        self.memory_cache.put(key, value)
        
        # 根据策略决定是否持久化
        strategy = self.strategies.get(category, {})
        if strategy.get('persistent', False):
            self.persistent_cache.put(key, value)
        
        self.global_stats.size += 1
    
    def remove(self, key: str):
        """移除缓存项"""
        removed = False
        
        if self.memory_cache.remove(key):
            removed = True
        
        # 从持久化缓存中移除（如果存在）
        # TODO: 实现持久化缓存的remove方法
        
        if removed:
            self.global_stats.size -= 1
    
    def clear_category(self, category: str):
        """清空指定类别的缓存"""
        # 简单实现：清空所有缓存
        # 在实际实现中，应该根据key的前缀或标记来清理
        self.memory_cache.clear()
        self.global_stats.size = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'memory_cache': {
                'hits': self.memory_cache.stats.hits,
                'misses': self.memory_cache.stats.misses,
                'hit_ratio': self.memory_cache.stats.hit_ratio,
                'size': self.memory_cache.stats.size,
                'evictions': self.memory_cache.stats.evictions
            },
            'global': {
                'hits': self.global_stats.hits,
                'misses': self.global_stats.misses,
                'hit_ratio': self.global_stats.hit_ratio,
                'total_size': self.global_stats.size,
                'uptime': self.global_stats.uptime
            },
            'predictions': len(self.predictive_cache.get_predictions())
        }
    
    def optimize_cache(self):
        """优化缓存性能"""
        # 执行预测性预加载
        def dummy_loader(key):
            return None  # 实际应用中应该实现真正的加载逻辑
        
        self.predictive_cache.preload_predicted_items(dummy_loader)
        
        # 清理过期项
        self._cleanup_expired_items()
        
        # 内存压缩
        gc.collect()
    
    def _cleanup_expired_items(self):
        """清理过期项目"""
        # TODO: 实现基于TTL的过期清理
        pass
    
    def _periodic_cleanup(self):
        """定期清理"""
        try:
            self.optimize_cache()
        except Exception as e:
            print(f"定期清理失败: {e}")
        finally:
            # 重新设置定时器
            self.cleanup_timer = threading.Timer(300, self._periodic_cleanup)
            self.cleanup_timer.daemon = True
            self.cleanup_timer.start()
    
    def shutdown(self):
        """关闭缓存管理器"""
        if hasattr(self, 'cleanup_timer'):
            self.cleanup_timer.cancel()

# 全局缓存管理器实例
_cache_manager = None

def get_cache_manager() -> IntelligentCacheManager:
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = IntelligentCacheManager()
    return _cache_manager

def cleanup_cache_manager():
    """清理全局缓存管理器"""
    global _cache_manager
    if _cache_manager:
        _cache_manager.shutdown()
        _cache_manager = None
