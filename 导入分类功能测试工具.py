#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入分类功能测试工具
测试导入素材时的分类选择功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QListWidget, QListWidgetItem, QFileDialog,
                               QMessageBox)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class ImportCategoryTestWindow(QMainWindow):
    """导入分类功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📁 导入分类功能测试工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.test_files = []
        self.setup_ui()
        self.create_test_files()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("📁 导入分类功能测试工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 测试按钮组
        button_layout = QHBoxLayout()
        
        test_dialog_btn = QPushButton("🗂️ 测试分类选择对话框")
        test_dialog_btn.clicked.connect(self.test_category_dialog)
        button_layout.addWidget(test_dialog_btn)
        
        test_temp_category_btn = QPushButton("📦 测试临时分组")
        test_temp_category_btn.clicked.connect(self.test_temp_category)
        button_layout.addWidget(test_temp_category_btn)
        
        test_auto_categorize_btn = QPushButton("🤖 测试自动分类")
        test_auto_categorize_btn.clicked.connect(self.test_auto_categorize)
        button_layout.addWidget(test_auto_categorize_btn)
        
        test_preferences_btn = QPushButton("💾 测试偏好保存")
        test_preferences_btn.clicked.connect(self.test_preferences)
        button_layout.addWidget(test_preferences_btn)
        
        layout.addLayout(button_layout)
        
        # 文件选择区域
        file_group = QGroupBox("📄 测试文件")
        file_layout = QVBoxLayout(file_group)
        
        file_button_layout = QHBoxLayout()
        
        select_files_btn = QPushButton("📁 选择测试文件")
        select_files_btn.clicked.connect(self.select_test_files)
        file_button_layout.addWidget(select_files_btn)
        
        create_test_files_btn = QPushButton("🔧 创建测试文件")
        create_test_files_btn.clicked.connect(self.create_test_files)
        file_button_layout.addWidget(create_test_files_btn)
        
        clear_files_btn = QPushButton("🗑️ 清空文件列表")
        clear_files_btn.clicked.connect(self.clear_test_files)
        file_button_layout.addWidget(clear_files_btn)
        
        file_layout.addLayout(file_button_layout)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(150)
        file_layout.addWidget(self.file_list)
        
        layout.addWidget(file_group)
        
        # 分类状态显示
        category_group = QGroupBox("🗂️ 分类状态")
        category_layout = QVBoxLayout(category_group)
        
        refresh_categories_btn = QPushButton("🔄 刷新分类列表")
        refresh_categories_btn.clicked.connect(self.refresh_categories)
        category_layout.addWidget(refresh_categories_btn)
        
        self.category_list = QListWidget()
        self.category_list.setMaximumHeight(150)
        category_layout.addWidget(self.category_list)
        
        layout.addWidget(category_group)
        
        # 测试日志
        log_group = QGroupBox("📝 测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
        
        # 初始化
        self.refresh_categories()
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def create_test_files(self):
        """创建测试文件"""
        try:
            test_dir = Path("test_import_files")
            test_dir.mkdir(exist_ok=True)
            
            # 创建不同类型的测试文件
            test_files_info = [
                ("test_image1.jpg", "图片文件1"),
                ("test_image2.png", "图片文件2"),
                ("test_audio1.mp3", "音频文件1"),
                ("test_audio2.wav", "音频文件2"),
                ("test_document1.pdf", "文档文件1"),
                ("test_document2.docx", "文档文件2"),
                ("test_design1.psd", "设计文件1"),
                ("test_video1.mp4", "视频文件1")
            ]
            
            created_files = []
            for filename, description in test_files_info:
                file_path = test_dir / filename
                
                # 创建空文件（用于测试）
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"测试文件: {description}\n创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                created_files.append(str(file_path))
            
            self.test_files = created_files
            self.update_file_list()
            
            self.log(f"✅ 已创建 {len(created_files)} 个测试文件")
            
        except Exception as e:
            self.log(f"❌ 创建测试文件失败: {e}")
    
    def select_test_files(self):
        """选择测试文件"""
        try:
            files, _ = QFileDialog.getOpenFileNames(
                self, "选择测试文件", "", 
                "所有文件 (*.*);;图片文件 (*.jpg *.png *.gif);;音频文件 (*.mp3 *.wav);;文档文件 (*.pdf *.doc *.docx)"
            )
            
            if files:
                self.test_files = files
                self.update_file_list()
                self.log(f"✅ 已选择 {len(files)} 个测试文件")
            
        except Exception as e:
            self.log(f"❌ 选择测试文件失败: {e}")
    
    def clear_test_files(self):
        """清空测试文件列表"""
        self.test_files = []
        self.update_file_list()
        self.log("🗑️ 已清空测试文件列表")
    
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_list.clear()
        
        for file_path in self.test_files:
            file_name = Path(file_path).name
            file_size = "未知大小"
            
            try:
                if Path(file_path).exists():
                    size_bytes = Path(file_path).stat().st_size
                    if size_bytes < 1024:
                        file_size = f"{size_bytes} B"
                    elif size_bytes < 1024 * 1024:
                        file_size = f"{size_bytes / 1024:.1f} KB"
                    else:
                        file_size = f"{size_bytes / (1024 * 1024):.1f} MB"
            except:
                pass
            
            item_text = f"{file_name} ({file_size})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, file_path)
            self.file_list.addItem(item)
    
    def refresh_categories(self):
        """刷新分类列表"""
        self.category_list.clear()
        
        try:
            from core.category_manager import get_category_manager
            category_manager = get_category_manager()
            
            all_categories = category_manager.get_all_categories()
            
            for category in all_categories:
                display_text = f"{category['icon']} {category['name']}"
                if category['type'] == 'system':
                    display_text += " [系统]"
                else:
                    display_text += " [自定义]"
                
                if category.get('file_count', 0) > 0:
                    display_text += f" ({category['file_count']} 文件)"
                
                item = QListWidgetItem(display_text)
                item.setData(Qt.UserRole, category['id'])
                
                # 设置颜色
                try:
                    color = QColor(category['color'])
                    item.setForeground(color)
                except:
                    pass
                
                self.category_list.addItem(item)
            
            self.log(f"🔄 已刷新分类列表，共 {len(all_categories)} 个分类")
            
        except Exception as e:
            self.log(f"❌ 刷新分类列表失败: {e}")
    
    def test_category_dialog(self):
        """测试分类选择对话框"""
        if not self.test_files:
            QMessageBox.warning(self, "警告", "请先选择或创建测试文件！")
            return
        
        try:
            self.log("🗂️ 开始测试分类选择对话框...")
            
            from ui.dialogs.import_category_dialog import show_import_category_dialog
            
            result = show_import_category_dialog(self.test_files, self)
            
            if result:
                self.log("✅ 用户完成了分类选择:")
                self.log(f"  • 选择分类: {result['category_name']} (ID: {result['category_id']})")
                self.log(f"  • 自动分类: {result['auto_categorize']}")
                self.log(f"  • 检查重复: {result['check_duplicates']}")
                self.log(f"  • 记住选择: {result['remember_choice']}")
            else:
                self.log("⚠️ 用户取消了分类选择")
            
        except Exception as e:
            self.log(f"❌ 测试分类选择对话框失败: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
    
    def test_temp_category(self):
        """测试临时分组"""
        try:
            self.log("📦 开始测试临时分组...")
            
            from core.category_manager import get_category_manager
            category_manager = get_category_manager()
            
            # 检查临时分组是否存在
            temp_category = category_manager.get_category("temp")
            
            if temp_category:
                self.log("✅ 临时分组已存在:")
                self.log(f"  • 名称: {temp_category.name}")
                self.log(f"  • 图标: {temp_category.icon}")
                self.log(f"  • 颜色: {temp_category.color}")
                self.log(f"  • 描述: {temp_category.description}")
            else:
                self.log("⚠️ 临时分组不存在，尝试创建...")
                
                # 重新初始化系统分类
                category_manager._init_system_categories()
                category_manager.save_categories()
                
                # 再次检查
                temp_category = category_manager.get_category("temp")
                if temp_category:
                    self.log("✅ 临时分组创建成功")
                else:
                    self.log("❌ 临时分组创建失败")
            
            # 刷新分类列表
            self.refresh_categories()
            
        except Exception as e:
            self.log(f"❌ 测试临时分组失败: {e}")
    
    def test_auto_categorize(self):
        """测试自动分类"""
        if not self.test_files:
            QMessageBox.warning(self, "警告", "请先选择或创建测试文件！")
            return
        
        try:
            self.log("🤖 开始测试自动分类...")
            
            # 模拟自动分类逻辑
            for file_path in self.test_files:
                file_ext = Path(file_path).suffix.lower()
                
                # 根据扩展名确定分类
                if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                    category = "images (图片文件)"
                elif file_ext in ['.mp3', '.wav', '.flac', '.aac']:
                    category = "audio (音频文件)"
                elif file_ext in ['.mp4', '.avi', '.mov', '.mkv']:
                    category = "video (视频文件)"
                elif file_ext in ['.pdf', '.doc', '.docx', '.txt']:
                    category = "documents (文档文件)"
                elif file_ext in ['.psd', '.ai', '.sketch']:
                    category = "design (设计文件)"
                else:
                    category = "temp (临时分组)"
                
                file_name = Path(file_path).name
                self.log(f"  • {file_name} -> {category}")
            
            self.log("✅ 自动分类测试完成")
            
        except Exception as e:
            self.log(f"❌ 测试自动分类失败: {e}")
    
    def test_preferences(self):
        """测试偏好保存"""
        try:
            self.log("💾 开始测试偏好保存...")
            
            import json
            from pathlib import Path
            
            # 模拟保存偏好
            preferences = {
                "default_category_id": "temp",
                "default_category_name": "临时分组",
                "auto_categorize": True,
                "check_duplicates": True,
                "last_saved": time.time()
            }
            
            config_dir = Path.home() / ".smart_asset_manager"
            config_dir.mkdir(parents=True, exist_ok=True)
            config_file = config_dir / "import_preferences.json"
            
            # 保存偏好
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(preferences, f, ensure_ascii=False, indent=2)
            
            self.log(f"✅ 偏好已保存到: {config_file}")
            
            # 读取偏好
            with open(config_file, 'r', encoding='utf-8') as f:
                loaded_preferences = json.load(f)
            
            self.log("✅ 偏好读取成功:")
            for key, value in loaded_preferences.items():
                self.log(f"  • {key}: {value}")
            
        except Exception as e:
            self.log(f"❌ 测试偏好保存失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = ImportCategoryTestWindow()
    window.show()
    
    print("导入分类功能测试工具启动成功！")
    print("功能特性：")
    print("1. 🗂️ 分类选择对话框测试")
    print("2. 📦 临时分组功能测试")
    print("3. 🤖 自动分类逻辑测试")
    print("4. 💾 用户偏好保存测试")
    print("5. 📄 测试文件管理")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
