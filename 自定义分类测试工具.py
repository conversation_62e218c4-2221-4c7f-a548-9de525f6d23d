#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义分类测试工具
测试分类的创建、编辑、删除等功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QGridLayout, QLineEdit, QComboBox,
                               QColorDialog, QMessageBox, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class CategoryTestWindow(QMainWindow):
    """自定义分类测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🗂️ 自定义分类测试工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.category_manager = None
        self.current_color = "#4A90E2"
        
        self.setup_ui()
        self.init_category_manager()
        self.load_categories()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QHBoxLayout(central_widget)
        
        # 左侧：分类操作面板
        self.setup_operation_panel(layout)
        
        # 右侧：分类列表和日志
        self.setup_display_panel(layout)
    
    def setup_operation_panel(self, parent_layout):
        """设置操作面板"""
        operation_widget = QWidget()
        operation_widget.setMaximumWidth(400)
        operation_layout = QVBoxLayout(operation_widget)
        
        # 标题
        title = QLabel("🗂️ 分类管理测试")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        operation_layout.addWidget(title)
        
        # 添加分类组
        add_group = QGroupBox("➕ 添加分类")
        add_layout = QGridLayout(add_group)
        
        add_layout.addWidget(QLabel("名称:"), 0, 0)
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入分类名称")
        add_layout.addWidget(self.name_edit, 0, 1)
        
        add_layout.addWidget(QLabel("图标:"), 1, 0)
        self.icon_combo = QComboBox()
        self.icon_combo.addItems(["📁", "🖼️", "🎵", "🎬", "📄", "🎨", "📦", "⭐", "❤️", "🔥"])
        self.icon_combo.setEditable(True)
        add_layout.addWidget(self.icon_combo, 1, 1)
        
        add_layout.addWidget(QLabel("颜色:"), 2, 0)
        self.color_btn = QPushButton()
        self.color_btn.setFixedHeight(30)
        self.color_btn.clicked.connect(self.choose_color)
        self.update_color_button()
        add_layout.addWidget(self.color_btn, 2, 1)
        
        add_layout.addWidget(QLabel("描述:"), 3, 0)
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("可选描述")
        add_layout.addWidget(self.description_edit, 3, 1)
        
        add_btn = QPushButton("➕ 添加分类")
        add_btn.clicked.connect(self.add_category)
        add_layout.addWidget(add_btn, 4, 0, 1, 2)
        
        operation_layout.addWidget(add_group)
        
        # 测试按钮组
        test_group = QGroupBox("🧪 测试功能")
        test_layout = QVBoxLayout(test_group)
        
        batch_add_btn = QPushButton("📦 批量添加测试分类")
        batch_add_btn.clicked.connect(self.batch_add_categories)
        test_layout.addWidget(batch_add_btn)
        
        update_test_btn = QPushButton("✏️ 测试更新分类")
        update_test_btn.clicked.connect(self.test_update_category)
        test_layout.addWidget(update_test_btn)
        
        delete_test_btn = QPushButton("🗑️ 测试删除分类")
        delete_test_btn.clicked.connect(self.test_delete_category)
        test_layout.addWidget(delete_test_btn)
        
        export_btn = QPushButton("📤 导出分类")
        export_btn.clicked.connect(self.export_categories)
        test_layout.addWidget(export_btn)
        
        import_btn = QPushButton("📥 导入分类")
        import_btn.clicked.connect(self.import_categories)
        test_layout.addWidget(import_btn)
        
        stats_btn = QPushButton("📊 显示统计")
        stats_btn.clicked.connect(self.show_stats)
        test_layout.addWidget(stats_btn)
        
        operation_layout.addWidget(test_group)
        
        # 管理器测试组
        manager_group = QGroupBox("⚙️ 管理器测试")
        manager_layout = QVBoxLayout(manager_group)
        
        dialog_btn = QPushButton("🗂️ 打开分类管理对话框")
        dialog_btn.clicked.connect(self.open_category_dialog)
        manager_layout.addWidget(dialog_btn)
        
        sidebar_btn = QPushButton("📋 测试侧边栏集成")
        sidebar_btn.clicked.connect(self.test_sidebar_integration)
        manager_layout.addWidget(sidebar_btn)
        
        operation_layout.addWidget(manager_group)
        
        operation_layout.addStretch()
        parent_layout.addWidget(operation_widget)
    
    def setup_display_panel(self, parent_layout):
        """设置显示面板"""
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)
        
        # 分类列表
        list_group = QGroupBox("📋 分类列表")
        list_layout = QVBoxLayout(list_group)
        
        self.category_list = QListWidget()
        self.category_list.itemDoubleClicked.connect(self.on_category_double_clicked)
        list_layout.addWidget(self.category_list)
        
        refresh_btn = QPushButton("🔄 刷新列表")
        refresh_btn.clicked.connect(self.load_categories)
        list_layout.addWidget(refresh_btn)
        
        display_layout.addWidget(list_group)
        
        # 日志显示
        log_group = QGroupBox("📝 操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        display_layout.addWidget(log_group)
        
        parent_layout.addWidget(display_widget)
    
    def init_category_manager(self):
        """初始化分类管理器"""
        try:
            from core.category_manager import get_category_manager
            self.category_manager = get_category_manager()
            
            # 连接信号
            self.category_manager.category_added.connect(self.on_category_added)
            self.category_manager.category_removed.connect(self.on_category_removed)
            self.category_manager.category_updated.connect(self.on_category_updated)
            
            self.log("✅ 分类管理器初始化成功")
            
        except Exception as e:
            self.log(f"❌ 分类管理器初始化失败: {e}")
    
    def choose_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(QColor(self.current_color), self)
        if color.isValid():
            self.current_color = color.name()
            self.update_color_button()
    
    def update_color_button(self):
        """更新颜色按钮"""
        self.color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.current_color};
                border: 2px solid #ccc;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border-color: #999;
            }}
        """)
        self.color_btn.setText(self.current_color)
    
    def add_category(self):
        """添加分类"""
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "错误", "请输入分类名称！")
            return
        
        icon = self.icon_combo.currentText()
        description = self.description_edit.text().strip()
        
        try:
            category_id = self.category_manager.add_category(
                name=name,
                icon=icon,
                color=self.current_color,
                description=description
            )
            
            if category_id:
                self.log(f"✅ 添加分类成功: {name} (ID: {category_id})")
                self.clear_form()
            else:
                self.log(f"❌ 添加分类失败: {name}")
                
        except Exception as e:
            self.log(f"❌ 添加分类异常: {e}")
    
    def batch_add_categories(self):
        """批量添加测试分类"""
        test_categories = [
            ("我的收藏", "⭐", "#FFD700", "收藏的重要文件"),
            ("工作项目", "💼", "#4A90E2", "工作相关的项目文件"),
            ("学习资料", "📚", "#27AE60", "学习和教育资料"),
            ("娱乐内容", "🎮", "#E74C3C", "游戏和娱乐文件"),
            ("临时文件", "⏰", "#95A5A6", "临时存放的文件")
        ]
        
        added_count = 0
        for name, icon, color, description in test_categories:
            try:
                category_id = self.category_manager.add_category(
                    name=name, icon=icon, color=color, description=description
                )
                if category_id:
                    added_count += 1
            except:
                pass
        
        self.log(f"📦 批量添加完成，成功添加 {added_count} 个分类")
    
    def test_update_category(self):
        """测试更新分类"""
        categories = self.category_manager.get_custom_categories()
        if not categories:
            self.log("❌ 没有自定义分类可以更新")
            return
        
        # 更新第一个自定义分类
        category = categories[0]
        category_id = category['id']
        
        success = self.category_manager.update_category(
            category_id,
            name=f"{category['name']} (已更新)",
            description=f"更新时间: {time.strftime('%H:%M:%S')}"
        )
        
        if success:
            self.log(f"✅ 更新分类成功: {category['name']}")
        else:
            self.log(f"❌ 更新分类失败: {category['name']}")
    
    def test_delete_category(self):
        """测试删除分类"""
        categories = self.category_manager.get_custom_categories()
        if not categories:
            self.log("❌ 没有自定义分类可以删除")
            return
        
        # 删除最后一个自定义分类
        category = categories[-1]
        category_id = category['id']
        
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除分类 '{category['name']}' 吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.category_manager.remove_category(category_id)
            if success:
                self.log(f"✅ 删除分类成功: {category['name']}")
            else:
                self.log(f"❌ 删除分类失败: {category['name']}")
    
    def export_categories(self):
        """导出分类"""
        from PySide6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出分类", "categories_export.json", "JSON文件 (*.json)"
        )
        
        if file_path:
            success = self.category_manager.export_categories(file_path)
            if success:
                self.log(f"✅ 分类导出成功: {file_path}")
            else:
                self.log(f"❌ 分类导出失败")
    
    def import_categories(self):
        """导入分类"""
        from PySide6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入分类", "", "JSON文件 (*.json)"
        )
        
        if file_path:
            success = self.category_manager.import_categories(file_path)
            if success:
                self.log(f"✅ 分类导入成功: {file_path}")
            else:
                self.log(f"❌ 分类导入失败")
    
    def show_stats(self):
        """显示统计信息"""
        stats = self.category_manager.get_category_stats()
        
        stats_text = f"""
📊 分类统计信息:
• 总分类数: {stats['total_categories']}
• 系统分类: {stats['system_categories']}
• 自定义分类: {stats['custom_categories']}
• 总文件数: {stats['total_files']}
• 有文件的分类: {stats['categories_with_files']}
        """.strip()
        
        QMessageBox.information(self, "统计信息", stats_text)
        self.log("📊 已显示统计信息")
    
    def open_category_dialog(self):
        """打开分类管理对话框"""
        try:
            from ui.dialogs.category_manager_dialog import CategoryManagerDialog
            
            dialog = CategoryManagerDialog(self)
            dialog.exec()
            
            self.log("🗂️ 分类管理对话框已关闭")
            
        except Exception as e:
            self.log(f"❌ 打开分类管理对话框失败: {e}")
    
    def test_sidebar_integration(self):
        """测试侧边栏集成"""
        try:
            from ui.components.sidebar import SidebarWidget
            from database.db_manager import DatabaseManager
            from theme.theme_manager import ThemeManager
            
            # 创建必要的管理器
            db_manager = DatabaseManager()
            theme_manager = ThemeManager()
            
            # 创建侧边栏
            sidebar = SidebarWidget(db_manager, theme_manager)
            sidebar.show()
            
            self.log("📋 侧边栏测试窗口已打开")
            
        except Exception as e:
            self.log(f"❌ 侧边栏集成测试失败: {e}")
    
    def load_categories(self):
        """加载分类列表"""
        self.category_list.clear()
        
        try:
            all_categories = self.category_manager.get_all_categories()
            
            for category in all_categories:
                item_text = f"{category['icon']} {category['name']}"
                if category['type'] == 'system':
                    item_text += " [系统]"
                else:
                    item_text += f" [自定义] ({category['file_count']} 文件)"
                
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, category['id'])
                
                # 设置颜色
                color = QColor(category['color'])
                item.setForeground(color)
                
                self.category_list.addItem(item)
            
            self.log(f"🔄 已加载 {len(all_categories)} 个分类")
            
        except Exception as e:
            self.log(f"❌ 加载分类失败: {e}")
    
    def on_category_double_clicked(self, item):
        """分类双击事件"""
        category_id = item.data(Qt.UserRole)
        category = self.category_manager.get_category(category_id)
        
        if category:
            info_text = f"""
分类信息:
• ID: {category.id}
• 名称: {category.name}
• 类型: {'系统分类' if category.type.value == 'system' else '自定义分类'}
• 图标: {category.icon}
• 颜色: {category.color}
• 描述: {category.description or '无'}
• 文件数: {category.file_count}
• 创建时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(category.created_time))}
            """.strip()
            
            QMessageBox.information(self, "分类详情", info_text)
    
    def clear_form(self):
        """清空表单"""
        self.name_edit.clear()
        self.icon_combo.setCurrentIndex(0)
        self.description_edit.clear()
        self.current_color = "#4A90E2"
        self.update_color_button()
    
    def on_category_added(self, category_data):
        """分类添加事件"""
        self.load_categories()
        self.log(f"🔔 分类已添加: {category_data['name']}")
    
    def on_category_removed(self, category_id):
        """分类删除事件"""
        self.load_categories()
        self.log(f"🔔 分类已删除: {category_id}")
    
    def on_category_updated(self, category_data):
        """分类更新事件"""
        self.load_categories()
        self.log(f"🔔 分类已更新: {category_data['name']}")
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = CategoryTestWindow()
    window.show()
    
    print("自定义分类测试工具启动成功！")
    print("功能特性：")
    print("1. 🗂️ 分类的创建、编辑、删除")
    print("2. 📦 批量操作和测试")
    print("3. 📤📥 导入导出功能")
    print("4. 📊 统计信息显示")
    print("5. 🗂️ 分类管理对话框")
    print("6. 📋 侧边栏集成测试")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
