# 侧边栏组件
# 功能：左侧边栏界面，包含文件夹树、智能分类、标签云、颜色筛选和最近使用区域

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget,
                               QTreeWidgetItem, QLabel, QPushButton, QFrame,
                               QScrollArea, QGridLayout, QListWidget, QListWidgetItem,
                               QSplitter, QTabWidget, QInputDialog, QMessageBox)
from PySide6.QtCore import Qt, Signal, QSize, QTimer
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont

class SidebarWidget(QWidget):
    """侧边栏控件类"""

    # 信号定义
    category_selected = Signal(int)  # 分类选择信号
    tag_selected = Signal(str)  # 标签选择信号
    color_filter_changed = Signal(str)  # 颜色筛选信号
    recent_item_selected = Signal(int)  # 最近项目选择信号

    def __init__(self, theme_manager, db_manager, config_manager):
        super().__init__()

        self.theme_manager = theme_manager
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 界面组件
        self.category_tree = None
        self.tag_cloud_widget = None
        self.color_filter_widget = None
        self.recent_items_widget = None

        # 数据
        self.categories = []
        self.tags = []
        self.recent_items = []

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置用户界面"""
        # 设置固定宽度
        self.setFixedWidth(250)

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)

        # 创建标签页控件
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)

        # 分类标签页
        category_tab = self.create_category_tab()
        tab_widget.addTab(category_tab, "分类")

        # 标签标签页
        tag_tab = self.create_tag_tab()
        tab_widget.addTab(tag_tab, "标签")

        # 筛选标签页
        filter_tab = self.create_filter_tab()
        tab_widget.addTab(filter_tab, "筛选")

    def create_category_tab(self):
        """创建分类标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)

        # 分类树标题
        title_label = QLabel("文件分类")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(title_label)

        # 创建分类树
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderHidden(True)
        self.category_tree.setRootIsDecorated(True)
        self.category_tree.itemClicked.connect(self.on_category_item_clicked)
        layout.addWidget(self.category_tree)

        # 添加分类按钮
        add_category_btn = QPushButton("添加分类")
        add_category_btn.clicked.connect(self.add_category)
        layout.addWidget(add_category_btn)

        return widget

    def create_tag_tab(self):
        """创建标签标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)

        # 标签云标题
        title_label = QLabel("标签云")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(title_label)

        # 创建标签云滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        layout.addWidget(scroll_area)

        # 标签云容器
        self.tag_cloud_widget = QWidget()
        self.tag_cloud_layout = QVBoxLayout(self.tag_cloud_widget)
        self.tag_cloud_layout.setAlignment(Qt.AlignTop)
        scroll_area.setWidget(self.tag_cloud_widget)

        return widget

    def create_filter_tab(self):
        """创建筛选标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)

        # 颜色筛选区域
        color_frame = QFrame()
        color_frame.setFrameStyle(QFrame.Box)
        layout.addWidget(color_frame)

        color_layout = QVBoxLayout(color_frame)

        # 颜色筛选标题
        color_title = QLabel("颜色筛选")
        color_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        color_layout.addWidget(color_title)

        # 创建颜色筛选控件
        self.color_filter_widget = self.create_color_filter_widget()
        color_layout.addWidget(self.color_filter_widget)

        # 最近使用区域
        recent_frame = QFrame()
        recent_frame.setFrameStyle(QFrame.Box)
        layout.addWidget(recent_frame)

        recent_layout = QVBoxLayout(recent_frame)

        # 最近使用标题
        recent_title = QLabel("最近使用")
        recent_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        recent_layout.addWidget(recent_title)

        # 创建最近使用列表
        self.recent_items_widget = QListWidget()
        self.recent_items_widget.setMaximumHeight(150)
        self.recent_items_widget.itemClicked.connect(self.on_recent_item_clicked)
        recent_layout.addWidget(self.recent_items_widget)

        return widget

    def create_color_filter_widget(self):
        """创建颜色筛选控件"""
        widget = QWidget()
        layout = QGridLayout(widget)
        layout.setSpacing(5)

        # 定义主要颜色
        colors = [
            ("#e74c3c", "红色"),
            ("#f39c12", "橙色"),
            ("#f1c40f", "黄色"),
            ("#27ae60", "绿色"),
            ("#3498db", "蓝色"),
            ("#9b59b6", "紫色"),
            ("#34495e", "灰色"),
            ("#2c3e50", "黑色")
        ]

        # 创建颜色按钮
        for i, (color_code, color_name) in enumerate(colors):
            row = i // 4
            col = i % 4

            color_btn = ColorFilterButton(color_code, color_name)
            color_btn.clicked.connect(lambda checked, c=color_code: self.on_color_filter_clicked(c))
            layout.addWidget(color_btn, row, col)

        return widget

    def load_data(self):
        """加载数据"""
        self.load_categories()
        self.load_tags()
        self.load_recent_items()

    def load_categories(self):
        """加载分类数据"""
        # 清空现有项目
        self.category_tree.clear()

        # 添加默认分类
        all_items = QTreeWidgetItem(self.category_tree, ["全部素材"])
        all_items.setData(0, Qt.UserRole, -1)  # 存储分类ID

        # 智能分类
        smart_categories = QTreeWidgetItem(self.category_tree, ["智能分类"])

        # 按文件类型分类
        type_categories = [
            ("图片文件", "image"),
            ("视频文件", "video"),
            ("音频文件", "audio"),
            ("设计文件", "design"),
            ("文档文件", "document")
        ]

        for name, file_type in type_categories:
            item = QTreeWidgetItem(smart_categories, [name])
            item.setData(0, Qt.UserRole, file_type)

        # 自定义分类
        custom_categories = QTreeWidgetItem(self.category_tree, ["自定义分类"])

        # TODO: 从数据库加载自定义分类

        # 展开所有项目
        self.category_tree.expandAll()

    def load_tags(self):
        """加载标签数据"""
        # 清空现有标签
        for i in reversed(range(self.tag_cloud_layout.count())):
            child = self.tag_cloud_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 从数据库获取标签
        tags = self.db_manager.get_all_tags()

        # 创建标签按钮
        current_row_widget = None
        current_row_layout = None
        buttons_in_row = 0
        max_buttons_per_row = 2

        for tag in tags:
            if buttons_in_row == 0:
                current_row_widget = QWidget()
                current_row_layout = QHBoxLayout(current_row_widget)
                current_row_layout.setContentsMargins(0, 0, 0, 0)
                self.tag_cloud_layout.addWidget(current_row_widget)

            # 创建标签按钮
            tag_btn = TagButton(tag['name'], tag.get('usage_count', 0))
            tag_btn.clicked.connect(lambda checked, name=tag['name']: self.on_tag_clicked(name))
            current_row_layout.addWidget(tag_btn)

            buttons_in_row += 1

            if buttons_in_row >= max_buttons_per_row:
                current_row_layout.addStretch()
                buttons_in_row = 0

        # 如果最后一行没有填满，添加弹性空间
        if buttons_in_row > 0 and current_row_layout:
            current_row_layout.addStretch()

        # 添加底部弹性空间
        self.tag_cloud_layout.addStretch()

    def load_recent_items(self):
        """加载最近使用项目"""
        self.recent_items_widget.clear()

        # TODO: 从数据库或配置中获取最近使用的项目
        # 这里添加一些示例项目
        recent_items = [
            {"id": 1, "name": "示例图片1.jpg", "thumbnail": None},
            {"id": 2, "name": "示例图片2.png", "thumbnail": None},
            {"id": 3, "name": "示例视频.mp4", "thumbnail": None}
        ]

        for item in recent_items:
            list_item = QListWidgetItem(item["name"])
            list_item.setData(Qt.UserRole, item["id"])
            self.recent_items_widget.addItem(list_item)

    def on_category_item_clicked(self, item, column):
        """分类项目点击处理"""
        category_id = item.data(0, Qt.UserRole)
        if category_id is not None:
            print(f"分类被点击: {item.text(0)}, ID: {category_id}")
            self.category_selected.emit(category_id)

    def on_tag_clicked(self, tag_name):
        """标签点击处理"""
        self.tag_selected.emit(tag_name)

    def on_color_filter_clicked(self, color_code):
        """颜色筛选点击处理"""
        self.color_filter_changed.emit(color_code)

    def on_recent_item_clicked(self, item):
        """最近项目点击处理"""
        item_id = item.data(Qt.UserRole)
        if item_id is not None:
            self.recent_item_selected.emit(item_id)

    def add_category(self):
        """添加分类"""
        try:
            # 弹出输入对话框
            category_name, ok = QInputDialog.getText(
                self, "添加分类", "请输入分类名称:"
            )

            if ok and category_name.strip():
                # TODO: 将分类保存到数据库
                # 这里先简单添加到界面
                custom_categories = None

                # 查找自定义分类节点
                for i in range(self.category_tree.topLevelItemCount()):
                    item = self.category_tree.topLevelItem(i)
                    if item.text(0) == "自定义分类":
                        custom_categories = item
                        break

                if custom_categories:
                    # 添加新分类
                    new_category = QTreeWidgetItem(custom_categories, [category_name.strip()])
                    new_category.setData(0, Qt.UserRole, f"custom_{category_name.strip()}")

                    # 展开自定义分类节点
                    custom_categories.setExpanded(True)

                    QMessageBox.information(self, "成功", f"分类 '{category_name.strip()}' 已添加")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加分类失败: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.load_data()

class ColorFilterButton(QPushButton):
    """颜色筛选按钮类"""

    def __init__(self, color_code, color_name):
        super().__init__()
        self.color_code = color_code
        self.color_name = color_name

        self.setFixedSize(30, 30)
        self.setToolTip(color_name)
        self.setCheckable(True)

        # 设置样式
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color_code};
                border: 2px solid #bdc3c7;
                border-radius: 15px;
            }}
            QPushButton:hover {{
                border-color: #3498db;
            }}
            QPushButton:checked {{
                border-color: #2980b9;
                border-width: 3px;
            }}
        """)

class TagButton(QPushButton):
    """标签按钮类"""

    def __init__(self, tag_name, usage_count):
        super().__init__(tag_name)
        self.tag_name = tag_name
        self.usage_count = usage_count

        # 根据使用次数调整字体大小
        font_size = min(12, max(8, 8 + usage_count // 10))
        font = QFont("Microsoft YaHei", font_size)
        self.setFont(font)

        # 设置工具提示
        self.setToolTip(f"{tag_name} (使用次数: {usage_count})")

        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 12px;
                padding: 4px 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #3498db;
                color: white;
            }
            QPushButton:pressed {
                background-color: #2980b9;
            }
        """)
