#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空列表功能测试工具
测试清空列表显示功能（不删除文件，只清空显示）
"""

import sys
import time
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QListWidget, QListWidgetItem, QCheckBox,
                               QSpinBox, QProgressBar, QMessageBox, QFileDialog)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class ClearListTestWindow(QMainWindow):
    """清空列表功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🗑️ 清空列表功能测试工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.test_items = []
        self.selected_items = []
        self.setup_ui()
        self.create_test_data()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🗑️ 清空列表功能测试工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 功能说明
        desc_group = QGroupBox("📋 功能说明")
        desc_layout = QVBoxLayout(desc_group)
        
        desc_text = QLabel("""
🎯 清空列表功能说明：
• 🗑️ 清空列表：清空所有列表素材显示，但不删除实际文件
• ❌ 清空选择：只清空选择状态，保留列表显示
• 🗑️ 删除选中：删除选中的素材文件（实际删除）

⚠️ 注意区别：
- 清空列表 ≠ 删除文件（只是不显示）
- 清空选择 ≠ 清空列表（只是取消选中）
- 删除选中 = 真正删除文件
        """)
        desc_text.setWordWrap(True)
        desc_text.setStyleSheet("color: #333; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        desc_layout.addWidget(desc_text)
        
        layout.addWidget(desc_group)
        
        # 测试控制面板
        control_group = QGroupBox("🎛️ 测试控制")
        control_layout = QVBoxLayout(control_group)
        
        # 基础操作按钮
        basic_layout = QHBoxLayout()
        
        create_data_btn = QPushButton("📁 创建测试数据")
        create_data_btn.clicked.connect(self.create_test_data)
        basic_layout.addWidget(create_data_btn)
        
        select_some_btn = QPushButton("✅ 选择部分项目")
        select_some_btn.clicked.connect(self.select_some_items)
        basic_layout.addWidget(select_some_btn)
        
        basic_layout.addStretch()
        control_layout.addLayout(basic_layout)
        
        # 清空功能测试按钮
        clear_layout = QHBoxLayout()
        
        clear_list_btn = QPushButton("🗑️ 清空列表")
        clear_list_btn.clicked.connect(self.test_clear_list)
        clear_list_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        clear_layout.addWidget(clear_list_btn)
        
        clear_selection_btn = QPushButton("❌ 清空选择")
        clear_selection_btn.clicked.connect(self.test_clear_selection)
        clear_selection_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        clear_layout.addWidget(clear_selection_btn)
        
        delete_selected_btn = QPushButton("🗑️ 删除选中")
        delete_selected_btn.clicked.connect(self.test_delete_selected)
        delete_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #732d91;
            }
        """)
        clear_layout.addWidget(delete_selected_btn)
        
        clear_layout.addStretch()
        control_layout.addLayout(clear_layout)
        
        # 主程序测试按钮
        main_layout = QHBoxLayout()
        
        open_main_btn = QPushButton("🚀 打开主程序测试")
        open_main_btn.clicked.connect(self.open_main_app)
        open_main_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        main_layout.addWidget(open_main_btn)
        
        main_layout.addStretch()
        control_layout.addLayout(main_layout)
        
        layout.addWidget(control_group)
        
        # 状态显示
        status_group = QGroupBox("📊 当前状态")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("准备就绪")
        self.status_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        # 测试项目列表
        list_group = QGroupBox("📄 测试项目列表")
        list_layout = QVBoxLayout(list_group)
        
        self.item_list = QListWidget()
        self.item_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.item_list.itemSelectionChanged.connect(self.on_selection_changed)
        list_layout.addWidget(self.item_list)
        
        layout.addWidget(list_group)
        
        # 测试日志
        log_group = QGroupBox("📝 测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumHeight(120)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def create_test_data(self):
        """创建测试数据"""
        try:
            self.log("📁 创建测试数据...")
            
            # 清空现有数据
            self.item_list.clear()
            self.test_items = []
            self.selected_items = []
            
            # 生成测试项目
            file_types = [
                ("图片", "🖼️"),
                ("音频", "🎵"),
                ("视频", "🎬"),
                ("文档", "📄"),
                ("设计", "🎨")
            ]
            
            for i in range(25):
                file_type, icon = file_types[i % len(file_types)]
                item_name = f"{icon} {file_type}文件_{i+1:03d}.ext"
                
                # 创建列表项
                list_item = QListWidgetItem(item_name)
                list_item.setData(Qt.UserRole, i)
                self.item_list.addItem(list_item)
                
                # 保存到测试数据
                self.test_items.append({
                    'id': i,
                    'name': item_name,
                    'type': file_type,
                    'icon': icon
                })
            
            self.update_status_display()
            self.log(f"✅ 成功创建 {len(self.test_items)} 个测试项目")
            
        except Exception as e:
            self.log(f"❌ 创建测试数据失败: {e}")
    
    def select_some_items(self):
        """选择部分项目"""
        try:
            self.log("✅ 选择部分项目...")
            
            # 清空当前选择
            self.item_list.clearSelection()
            
            # 选择前5个项目
            count = min(5, self.item_list.count())
            for i in range(count):
                self.item_list.item(i).setSelected(True)
            
            selected_count = len(self.item_list.selectedItems())
            self.log(f"✅ 已选择 {selected_count} 个项目")
            
        except Exception as e:
            self.log(f"❌ 选择项目失败: {e}")
    
    def test_clear_list(self):
        """测试清空列表功能"""
        try:
            self.log("🗑️ 测试清空列表功能...")
            
            before_count = self.item_list.count()
            before_selected = len(self.item_list.selectedItems())
            
            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认清空列表",
                f"确定要清空所有列表素材显示吗？\n\n当前有 {before_count} 个项目\n选中 {before_selected} 个项目\n\n注意：这只会清空显示列表，不会删除实际文件。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 执行清空列表
                self.item_list.clear()
                self.test_items = []
                
                after_count = self.item_list.count()
                
                self.log(f"🗑️ 清空列表完成")
                self.log(f"📋 清空前: {before_count} 个项目")
                self.log(f"📋 清空后: {after_count} 个项目")
                
                if after_count == 0:
                    self.log("✅ 清空列表功能正常 - 所有显示项目已清空")
                    QMessageBox.information(
                        self,
                        "清空完成",
                        f"已清空列表显示，共清空 {before_count} 个项目\n\n实际文件未被删除。"
                    )
                else:
                    self.log("❌ 清空列表功能异常")
                    
                self.update_status_display()
            else:
                self.log("🚫 用户取消了清空列表操作")
                
        except Exception as e:
            self.log(f"❌ 清空列表测试失败: {e}")
    
    def test_clear_selection(self):
        """测试清空选择功能"""
        try:
            self.log("❌ 测试清空选择功能...")
            
            before_count = self.item_list.count()
            before_selected = len(self.item_list.selectedItems())
            
            # 执行清空选择
            self.item_list.clearSelection()
            
            after_count = self.item_list.count()
            after_selected = len(self.item_list.selectedItems())
            
            self.log(f"❌ 清空选择完成")
            self.log(f"📋 项目数量: {before_count} -> {after_count}")
            self.log(f"📋 选中数量: {before_selected} -> {after_selected}")
            
            if after_selected == 0 and after_count == before_count:
                self.log("✅ 清空选择功能正常 - 只清空选择，保留所有项目")
            else:
                self.log("❌ 清空选择功能异常")
                
            self.update_status_display()
            
        except Exception as e:
            self.log(f"❌ 清空选择测试失败: {e}")
    
    def test_delete_selected(self):
        """测试删除选中功能"""
        try:
            self.log("🗑️ 测试删除选中功能...")
            
            selected_items = self.item_list.selectedItems()
            if not selected_items:
                self.log("⚠️ 没有选中的项目需要删除")
                QMessageBox.warning(self, "警告", "请先选择要删除的项目！")
                return
            
            before_count = self.item_list.count()
            before_selected = len(selected_items)
            
            # 确认删除对话框
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {before_selected} 个项目吗？\n\n此操作会从列表中移除这些项目。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 执行删除选中项目
                for item in selected_items:
                    row = self.item_list.row(item)
                    self.item_list.takeItem(row)
                
                after_count = self.item_list.count()
                after_selected = len(self.item_list.selectedItems())
                
                self.log(f"🗑️ 删除选中完成")
                self.log(f"📋 项目数量: {before_count} -> {after_count}")
                self.log(f"📋 选中数量: {before_selected} -> {after_selected}")
                
                if after_count == before_count - before_selected:
                    self.log("✅ 删除选中功能正常 - 删除了选中的项目")
                else:
                    self.log("❌ 删除选中功能异常")
                    
                self.update_status_display()
            else:
                self.log("🚫 用户取消了删除操作")
                
        except Exception as e:
            self.log(f"❌ 删除选中测试失败: {e}")
    
    def open_main_app(self):
        """打开主程序"""
        try:
            self.log("🚀 启动主程序进行实际测试...")
            
            import subprocess
            import sys
            
            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"], 
                           cwd=str(project_root))
            
            self.log("✅ 主程序已启动")
            self.log("💡 请在主程序中测试清空列表功能")
            
        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")
    
    def on_selection_changed(self):
        """选择变更处理"""
        self.update_status_display()
    
    def update_status_display(self):
        """更新状态显示"""
        try:
            selected_count = len(self.item_list.selectedItems())
            total_count = self.item_list.count()
            
            if total_count == 0:
                self.status_label.setText("列表为空")
                self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            elif selected_count == 0:
                self.status_label.setText(f"总计 {total_count} 个项目，未选择")
                self.status_label.setStyleSheet("color: #666;")
            elif selected_count == total_count:
                self.status_label.setText(f"已全选 ({selected_count})")
                self.status_label.setStyleSheet("color: #27AE60; font-weight: bold;")
            else:
                self.status_label.setText(f"已选择 {selected_count}/{total_count}")
                self.status_label.setStyleSheet("color: #3498DB; font-weight: bold;")
                
        except Exception as e:
            self.log(f"❌ 更新状态显示失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = ClearListTestWindow()
    window.show()
    
    print("清空列表功能测试工具启动成功！")
    print("功能测试：")
    print("1. 🗑️ 清空列表 - 清空所有显示，不删除文件")
    print("2. ❌ 清空选择 - 只清空选择状态")
    print("3. 🗑️ 删除选中 - 删除选中的项目")
    print("4. 🚀 主程序测试 - 在实际程序中测试")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
