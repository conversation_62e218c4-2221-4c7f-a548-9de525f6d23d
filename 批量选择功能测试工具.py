#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量选择功能测试工具
测试全选、反选、清空选择等批量操作功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QListWidget, QListWidgetItem, QCheckBox,
                               QSpinBox, QProgressBar, QMessageBox)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class BatchSelectionTestWindow(QMainWindow):
    """批量选择功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("📋 批量选择功能测试工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.test_items = []
        self.selected_items = []
        self.setup_ui()
        self.create_test_data()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("📋 批量选择功能测试工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 控制面板
        control_group = QGroupBox("🎛️ 测试控制")
        control_layout = QVBoxLayout(control_group)
        
        # 数据生成控制
        data_layout = QHBoxLayout()
        
        data_layout.addWidget(QLabel("测试数据数量:"))
        self.item_count_spin = QSpinBox()
        self.item_count_spin.setRange(10, 10000)
        self.item_count_spin.setValue(100)
        data_layout.addWidget(self.item_count_spin)
        
        generate_data_btn = QPushButton("🔧 生成测试数据")
        generate_data_btn.clicked.connect(self.create_test_data)
        data_layout.addWidget(generate_data_btn)
        
        data_layout.addStretch()
        control_layout.addLayout(data_layout)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("✅ 全选")
        select_all_btn.clicked.connect(self.test_select_all)
        batch_layout.addWidget(select_all_btn)
        
        invert_selection_btn = QPushButton("🔄 反选")
        invert_selection_btn.clicked.connect(self.test_invert_selection)
        batch_layout.addWidget(invert_selection_btn)
        
        clear_selection_btn = QPushButton("❌ 清空")
        clear_selection_btn.clicked.connect(self.test_clear_selection)
        batch_layout.addWidget(clear_selection_btn)
        
        random_select_btn = QPushButton("🎲 随机选择")
        random_select_btn.clicked.connect(self.test_random_selection)
        batch_layout.addWidget(random_select_btn)
        
        batch_layout.addStretch()
        control_layout.addLayout(batch_layout)
        
        # 性能测试按钮
        perf_layout = QHBoxLayout()
        
        perf_test_btn = QPushButton("⚡ 性能测试")
        perf_test_btn.clicked.connect(self.test_performance)
        perf_layout.addWidget(perf_test_btn)
        
        stress_test_btn = QPushButton("💪 压力测试")
        stress_test_btn.clicked.connect(self.test_stress)
        perf_layout.addWidget(stress_test_btn)
        
        perf_layout.addStretch()
        control_layout.addLayout(perf_layout)
        
        layout.addWidget(control_group)
        
        # 状态显示
        status_group = QGroupBox("📊 选择状态")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("未选择")
        self.status_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(status_group)
        
        # 测试项目列表
        list_group = QGroupBox("📄 测试项目列表")
        list_layout = QVBoxLayout(list_group)
        
        self.item_list = QListWidget()
        self.item_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.item_list.itemSelectionChanged.connect(self.on_selection_changed)
        list_layout.addWidget(self.item_list)
        
        layout.addWidget(list_group)
        
        # 测试日志
        log_group = QGroupBox("📝 测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def create_test_data(self):
        """创建测试数据"""
        try:
            count = self.item_count_spin.value()
            self.log(f"🔧 开始生成 {count} 个测试项目...")
            
            # 清空现有数据
            self.item_list.clear()
            self.test_items = []
            self.selected_items = []
            
            # 生成测试项目
            file_types = ["图片", "音频", "视频", "文档", "设计"]
            for i in range(count):
                file_type = file_types[i % len(file_types)]
                item_name = f"{file_type}文件_{i+1:04d}.ext"
                
                # 创建列表项
                list_item = QListWidgetItem(item_name)
                list_item.setData(Qt.UserRole, i)
                self.item_list.addItem(list_item)
                
                # 保存到测试数据
                self.test_items.append({
                    'id': i,
                    'name': item_name,
                    'type': file_type
                })
            
            self.update_status_display()
            self.log(f"✅ 成功生成 {count} 个测试项目")
            
        except Exception as e:
            self.log(f"❌ 生成测试数据失败: {e}")
    
    def test_select_all(self):
        """测试全选功能"""
        try:
            start_time = time.time()
            self.log("✅ 开始测试全选功能...")
            
            # 执行全选
            self.item_list.selectAll()
            
            end_time = time.time()
            elapsed = (end_time - start_time) * 1000
            
            selected_count = len(self.item_list.selectedItems())
            total_count = self.item_list.count()
            
            self.log(f"✅ 全选完成: {selected_count}/{total_count} 项目，耗时 {elapsed:.2f}ms")
            
            if selected_count == total_count:
                self.log("✅ 全选功能测试通过")
            else:
                self.log("❌ 全选功能测试失败")
            
        except Exception as e:
            self.log(f"❌ 全选测试失败: {e}")
    
    def test_invert_selection(self):
        """测试反选功能"""
        try:
            start_time = time.time()
            self.log("🔄 开始测试反选功能...")
            
            # 记录当前选择
            before_selection = set(item.data(Qt.UserRole) for item in self.item_list.selectedItems())
            all_items = set(range(self.item_list.count()))
            
            # 执行反选
            self.invert_list_selection()
            
            end_time = time.time()
            elapsed = (end_time - start_time) * 1000
            
            # 检查反选结果
            after_selection = set(item.data(Qt.UserRole) for item in self.item_list.selectedItems())
            expected_selection = all_items - before_selection
            
            self.log(f"🔄 反选完成: {len(after_selection)} 项目，耗时 {elapsed:.2f}ms")
            
            if after_selection == expected_selection:
                self.log("✅ 反选功能测试通过")
            else:
                self.log("❌ 反选功能测试失败")
            
        except Exception as e:
            self.log(f"❌ 反选测试失败: {e}")
    
    def test_clear_selection(self):
        """测试清空选择功能"""
        try:
            start_time = time.time()
            self.log("❌ 开始测试清空选择功能...")
            
            # 执行清空选择
            self.item_list.clearSelection()
            
            end_time = time.time()
            elapsed = (end_time - start_time) * 1000
            
            selected_count = len(self.item_list.selectedItems())
            
            self.log(f"❌ 清空选择完成，耗时 {elapsed:.2f}ms")
            
            if selected_count == 0:
                self.log("✅ 清空选择功能测试通过")
            else:
                self.log("❌ 清空选择功能测试失败")
            
        except Exception as e:
            self.log(f"❌ 清空选择测试失败: {e}")
    
    def test_random_selection(self):
        """测试随机选择"""
        try:
            import random
            
            self.log("🎲 开始随机选择测试...")
            
            # 清空当前选择
            self.item_list.clearSelection()
            
            # 随机选择一些项目
            total_count = self.item_list.count()
            select_count = random.randint(1, min(50, total_count))
            
            selected_indices = random.sample(range(total_count), select_count)
            
            start_time = time.time()
            
            for index in selected_indices:
                item = self.item_list.item(index)
                item.setSelected(True)
            
            end_time = time.time()
            elapsed = (end_time - start_time) * 1000
            
            actual_selected = len(self.item_list.selectedItems())
            
            self.log(f"🎲 随机选择完成: {actual_selected}/{total_count} 项目，耗时 {elapsed:.2f}ms")
            
        except Exception as e:
            self.log(f"❌ 随机选择测试失败: {e}")
    
    def test_performance(self):
        """性能测试"""
        try:
            self.log("⚡ 开始性能测试...")
            
            operations = [
                ("全选", self.test_select_all),
                ("反选", self.test_invert_selection),
                ("清空", self.test_clear_selection),
                ("随机选择", self.test_random_selection)
            ]
            
            for op_name, op_func in operations:
                self.log(f"⚡ 测试 {op_name} 性能...")
                
                # 多次执行取平均值
                times = []
                for i in range(5):
                    start_time = time.time()
                    op_func()
                    end_time = time.time()
                    times.append((end_time - start_time) * 1000)
                
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                
                self.log(f"⚡ {op_name} 性能: 平均 {avg_time:.2f}ms, 最快 {min_time:.2f}ms, 最慢 {max_time:.2f}ms")
            
            self.log("✅ 性能测试完成")
            
        except Exception as e:
            self.log(f"❌ 性能测试失败: {e}")
    
    def test_stress(self):
        """压力测试"""
        try:
            self.log("💪 开始压力测试...")
            
            # 生成大量数据
            original_count = self.item_count_spin.value()
            self.item_count_spin.setValue(5000)
            self.create_test_data()
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            
            # 执行大量操作
            operations = 100
            for i in range(operations):
                self.progress_bar.setValue(int((i + 1) / operations * 100))
                QApplication.processEvents()
                
                # 随机执行操作
                import random
                op = random.choice([
                    self.test_select_all,
                    self.test_invert_selection,
                    self.test_clear_selection,
                    self.test_random_selection
                ])
                op()
            
            # 恢复原始数据量
            self.item_count_spin.setValue(original_count)
            self.create_test_data()
            
            self.progress_bar.setVisible(False)
            self.log("✅ 压力测试完成")
            
        except Exception as e:
            self.log(f"❌ 压力测试失败: {e}")
            self.progress_bar.setVisible(False)
    
    def invert_list_selection(self):
        """反选列表项目"""
        # 获取当前选中的项目
        selected_items = self.item_list.selectedItems()
        selected_indices = set()
        
        for item in selected_items:
            row = self.item_list.row(item)
            selected_indices.add(row)
        
        # 清空选择
        self.item_list.clearSelection()
        
        # 选择未选中的项目
        for i in range(self.item_list.count()):
            if i not in selected_indices:
                self.item_list.item(i).setSelected(True)
    
    def on_selection_changed(self):
        """选择变更处理"""
        self.update_status_display()
    
    def update_status_display(self):
        """更新状态显示"""
        try:
            selected_count = len(self.item_list.selectedItems())
            total_count = self.item_list.count()
            
            if selected_count == 0:
                self.status_label.setText("未选择")
                self.status_label.setStyleSheet("color: #666;")
            elif selected_count == total_count and total_count > 0:
                self.status_label.setText(f"已全选 ({selected_count})")
                self.status_label.setStyleSheet("color: #27AE60; font-weight: bold;")
            else:
                self.status_label.setText(f"已选择 {selected_count}/{total_count}")
                self.status_label.setStyleSheet("color: #3498DB; font-weight: bold;")
            
        except Exception as e:
            self.log(f"❌ 更新状态显示失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = BatchSelectionTestWindow()
    window.show()
    
    print("批量选择功能测试工具启动成功！")
    print("功能特性：")
    print("1. ✅ 全选功能测试")
    print("2. 🔄 反选功能测试")
    print("3. ❌ 清空选择测试")
    print("4. 🎲 随机选择测试")
    print("5. ⚡ 性能测试")
    print("6. 💪 压力测试")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
