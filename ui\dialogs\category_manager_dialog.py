#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类管理对话框
提供分类的创建、编辑、删除等功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTreeWidget, 
                               QTreeWidgetItem, QPushButton, QLineEdit, QLabel,
                               QColorDialog, QComboBox, QTextEdit, QMessageBox,
                               QGroupBox, QGridLayout, QSplitter, QFrame,
                               QHeaderView, QMenu, QInputDialog, QFileDialog)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QFont, QColor, QPalette, QAction, QIcon

from core.category_manager import get_category_manager, CategoryType

class CategoryManagerDialog(QDialog):
    """分类管理对话框"""
    
    # 信号定义
    category_selected = Signal(str)  # 分类选择信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🗂️ 分类管理")
        self.setGeometry(200, 200, 800, 600)
        
        # 获取分类管理器
        self.category_manager = get_category_manager()
        
        # 当前选中的分类
        self.current_category_id = None
        
        self.setup_ui()
        self.connect_signals()
        self.load_categories()
        
        print("分类管理对话框初始化完成")
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🗂️ 分类管理")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：分类树
        self.setup_category_tree(splitter)
        
        # 右侧：分类详情和操作
        self.setup_category_details(splitter)
        
        # 底部按钮
        self.setup_bottom_buttons(layout)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
    
    def setup_category_tree(self, parent):
        """设置分类树"""
        tree_frame = QFrame()
        tree_layout = QVBoxLayout(tree_frame)
        
        # 树标题和工具栏
        tree_header = QHBoxLayout()
        tree_header.addWidget(QLabel("📁 分类列表"))
        tree_header.addStretch()
        
        # 添加分类按钮
        add_btn = QPushButton("➕ 添加")
        add_btn.clicked.connect(self.add_category)
        tree_header.addWidget(add_btn)
        
        tree_layout.addLayout(tree_header)
        
        # 分类树控件
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderLabels(["分类名称", "类型", "文件数"])
        self.category_tree.setRootIsDecorated(True)
        self.category_tree.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.category_tree.header()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        # 右键菜单
        self.category_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.category_tree.customContextMenuRequested.connect(self.show_context_menu)
        
        # 选择事件
        self.category_tree.itemSelectionChanged.connect(self.on_category_selected)
        
        tree_layout.addWidget(self.category_tree)
        parent.addWidget(tree_frame)
    
    def setup_category_details(self, parent):
        """设置分类详情面板"""
        details_frame = QFrame()
        details_layout = QVBoxLayout(details_frame)
        
        # 详情标题
        details_label = QLabel("📝 分类详情")
        details_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        details_layout.addWidget(details_label)
        
        # 分类信息组
        info_group = QGroupBox("基本信息")
        info_layout = QGridLayout(info_group)
        
        # 分类名称
        info_layout.addWidget(QLabel("名称:"), 0, 0)
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入分类名称")
        info_layout.addWidget(self.name_edit, 0, 1)
        
        # 分类图标
        info_layout.addWidget(QLabel("图标:"), 1, 0)
        icon_layout = QHBoxLayout()
        self.icon_combo = QComboBox()
        self.icon_combo.addItems(["📁", "🖼️", "🎵", "🎬", "📄", "🎨", "📦", "⭐", "❤️", "🔥"])
        self.icon_combo.setEditable(True)
        icon_layout.addWidget(self.icon_combo)
        info_layout.addLayout(icon_layout, 1, 1)
        
        # 分类颜色
        info_layout.addWidget(QLabel("颜色:"), 2, 0)
        color_layout = QHBoxLayout()
        self.color_btn = QPushButton()
        self.color_btn.setFixedSize(50, 30)
        self.color_btn.clicked.connect(self.choose_color)
        self.current_color = "#4A90E2"
        self.update_color_button()
        color_layout.addWidget(self.color_btn)
        color_layout.addStretch()
        info_layout.addLayout(color_layout, 2, 1)
        
        details_layout.addWidget(info_group)
        
        # 描述组
        desc_group = QGroupBox("描述")
        desc_layout = QVBoxLayout(desc_group)
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("输入分类描述（可选）")
        desc_layout.addWidget(self.description_edit)
        details_layout.addWidget(desc_group)
        
        # 操作按钮组
        action_group = QGroupBox("操作")
        action_layout = QHBoxLayout(action_group)
        
        self.save_btn = QPushButton("💾 保存")
        self.save_btn.clicked.connect(self.save_category)
        self.save_btn.setEnabled(False)
        action_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.clicked.connect(self.delete_category)
        self.delete_btn.setEnabled(False)
        action_layout.addWidget(self.delete_btn)
        
        self.clear_btn = QPushButton("🔄 清空")
        self.clear_btn.clicked.connect(self.clear_form)
        action_layout.addWidget(self.clear_btn)
        
        details_layout.addWidget(action_group)
        
        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout(stats_group)
        self.stats_label = QLabel("选择分类查看统计信息")
        self.stats_label.setWordWrap(True)
        stats_layout.addWidget(self.stats_label)
        details_layout.addWidget(stats_group)
        
        details_layout.addStretch()
        parent.addWidget(details_frame)
    
    def setup_bottom_buttons(self, layout):
        """设置底部按钮"""
        button_layout = QHBoxLayout()
        
        # 导入导出按钮
        import_btn = QPushButton("📥 导入分类")
        import_btn.clicked.connect(self.import_categories)
        button_layout.addWidget(import_btn)
        
        export_btn = QPushButton("📤 导出分类")
        export_btn.clicked.connect(self.export_categories)
        button_layout.addWidget(export_btn)
        
        button_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def connect_signals(self):
        """连接信号"""
        # 分类管理器信号
        self.category_manager.category_added.connect(self.on_category_added)
        self.category_manager.category_removed.connect(self.on_category_removed)
        self.category_manager.category_updated.connect(self.on_category_updated)
        
        # 表单变化信号
        self.name_edit.textChanged.connect(self.on_form_changed)
        self.icon_combo.currentTextChanged.connect(self.on_form_changed)
        self.description_edit.textChanged.connect(self.on_form_changed)
    
    def load_categories(self):
        """加载分类到树控件"""
        self.category_tree.clear()
        
        # 获取所有分类
        categories = self.category_manager.get_all_categories()
        
        # 创建分类项字典
        category_items = {}
        
        # 先创建所有项目
        for category in categories:
            item = QTreeWidgetItem()
            item.setText(0, f"{category['icon']} {category['name']}")
            item.setText(1, "系统" if category['type'] == 'system' else "自定义")
            item.setText(2, str(category['file_count']))
            item.setData(0, Qt.UserRole, category['id'])
            
            # 设置颜色
            color = QColor(category['color'])
            item.setForeground(0, color)
            
            # 系统分类设置为不可编辑
            if category['type'] == 'system':
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
            
            category_items[category['id']] = item
        
        # 构建树结构
        root_items = []
        for category in categories:
            item = category_items[category['id']]
            parent_id = category.get('parent_id')
            
            if parent_id and parent_id in category_items:
                category_items[parent_id].addChild(item)
            else:
                root_items.append(item)
        
        # 添加到树控件
        self.category_tree.addTopLevelItems(root_items)
        
        # 展开所有项目
        self.category_tree.expandAll()
        
        print(f"✅ 已加载 {len(categories)} 个分类")
    
    def on_category_selected(self):
        """分类选择事件"""
        current_item = self.category_tree.currentItem()
        if not current_item:
            self.clear_form()
            return
        
        category_id = current_item.data(0, Qt.UserRole)
        category = self.category_manager.get_category(category_id)
        
        if category:
            self.current_category_id = category_id
            self.load_category_to_form(category)
            
            # 更新按钮状态
            is_custom = category.type.value == 'custom'
            self.save_btn.setEnabled(is_custom)
            self.delete_btn.setEnabled(is_custom)
    
    def load_category_to_form(self, category):
        """加载分类到表单"""
        self.name_edit.setText(category.name)
        self.icon_combo.setCurrentText(category.icon)
        self.description_edit.setPlainText(category.description)
        self.current_color = category.color
        self.update_color_button()
        
        # 更新统计信息
        stats_text = f"""
创建时间: {self.format_time(category.created_time)}
修改时间: {self.format_time(category.modified_time)}
文件数量: {category.file_count}
分类类型: {'系统分类' if category.type.value == 'system' else '自定义分类'}
        """.strip()
        self.stats_label.setText(stats_text)
    
    def clear_form(self):
        """清空表单"""
        self.current_category_id = None
        self.name_edit.clear()
        self.icon_combo.setCurrentIndex(0)
        self.description_edit.clear()
        self.current_color = "#4A90E2"
        self.update_color_button()
        self.stats_label.setText("选择分类查看统计信息")
        
        # 重置按钮状态
        self.save_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
    
    def on_form_changed(self):
        """表单变化事件"""
        if self.current_category_id:
            category = self.category_manager.get_category(self.current_category_id)
            if category and category.type.value == 'custom':
                self.save_btn.setEnabled(True)
    
    def add_category(self):
        """添加分类"""
        name, ok = QInputDialog.getText(self, "添加分类", "请输入分类名称:")
        if ok and name.strip():
            category_id = self.category_manager.add_category(name.strip())
            if category_id:
                QMessageBox.information(self, "成功", f"分类 '{name}' 添加成功！")
            else:
                QMessageBox.warning(self, "失败", "分类添加失败，可能名称已存在。")
    
    def save_category(self):
        """保存分类"""
        if not self.current_category_id:
            return
        
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "错误", "分类名称不能为空！")
            return
        
        icon = self.icon_combo.currentText()
        description = self.description_edit.toPlainText()
        
        success = self.category_manager.update_category(
            self.current_category_id,
            name=name,
            icon=icon,
            color=self.current_color,
            description=description
        )
        
        if success:
            QMessageBox.information(self, "成功", "分类更新成功！")
        else:
            QMessageBox.warning(self, "失败", "分类更新失败！")
    
    def delete_category(self):
        """删除分类"""
        if not self.current_category_id:
            return
        
        category = self.category_manager.get_category(self.current_category_id)
        if not category:
            return
        
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除分类 '{category.name}' 吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.category_manager.remove_category(self.current_category_id)
            if success:
                QMessageBox.information(self, "成功", "分类删除成功！")
                self.clear_form()
            else:
                QMessageBox.warning(self, "失败", "分类删除失败！")
    
    def choose_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(QColor(self.current_color), self)
        if color.isValid():
            self.current_color = color.name()
            self.update_color_button()
            self.on_form_changed()
    
    def update_color_button(self):
        """更新颜色按钮"""
        self.color_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.current_color};
                border: 2px solid #ccc;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border-color: #999;
            }}
        """)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.category_tree.itemAt(position)
        if not item:
            return
        
        category_id = item.data(0, Qt.UserRole)
        category = self.category_manager.get_category(category_id)
        
        if not category or category.type.value == 'system':
            return
        
        menu = QMenu(self)
        
        edit_action = QAction("✏️ 编辑", self)
        edit_action.triggered.connect(lambda: self.edit_category_inline(item))
        menu.addAction(edit_action)
        
        delete_action = QAction("🗑️ 删除", self)
        delete_action.triggered.connect(lambda: self.delete_category_from_menu(category_id))
        menu.addAction(delete_action)
        
        menu.exec(self.category_tree.mapToGlobal(position))
    
    def edit_category_inline(self, item):
        """内联编辑分类"""
        self.category_tree.editItem(item, 0)
    
    def delete_category_from_menu(self, category_id):
        """从菜单删除分类"""
        self.current_category_id = category_id
        self.delete_category()
    
    def import_categories(self):
        """导入分类"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入分类配置", "", "JSON文件 (*.json)"
        )
        
        if file_path:
            success = self.category_manager.import_categories(file_path)
            if success:
                QMessageBox.information(self, "成功", "分类导入成功！")
            else:
                QMessageBox.warning(self, "失败", "分类导入失败！")
    
    def export_categories(self):
        """导出分类"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出分类配置", "categories.json", "JSON文件 (*.json)"
        )
        
        if file_path:
            success = self.category_manager.export_categories(file_path)
            if success:
                QMessageBox.information(self, "成功", "分类导出成功！")
            else:
                QMessageBox.warning(self, "失败", "分类导出失败！")
    
    def on_category_added(self, category_data):
        """分类添加事件"""
        self.load_categories()
    
    def on_category_removed(self, category_id):
        """分类删除事件"""
        self.load_categories()
    
    def on_category_updated(self, category_data):
        """分类更新事件"""
        self.load_categories()
    
    def format_time(self, timestamp):
        """格式化时间"""
        import datetime
        return datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")

def main():
    """测试函数"""
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = CategoryManagerDialog()
    dialog.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
