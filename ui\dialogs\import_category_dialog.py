#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入分类选择对话框
在导入素材时提醒用户选择分类
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QComboBox, QCheckBox, QGroupBox,
                               QTextEdit, QGridLayout, QButtonGroup, QRadioButton,
                               QScrollArea, QWidget, QFrame, QMessageBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QPixmap, QIcon

class ImportCategoryDialog(QDialog):
    """导入分类选择对话框"""
    
    # 信号定义
    category_selected = Signal(str, str)  # category_id, category_name
    
    def __init__(self, file_paths=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📁 选择导入分类")
        self.setModal(True)
        self.resize(500, 600)
        
        # 导入的文件路径
        self.file_paths = file_paths or []
        self.selected_category_id = None
        self.selected_category_name = None
        
        # 获取分类管理器
        try:
            from core.category_manager import get_category_manager
            self.category_manager = get_category_manager()
        except:
            self.category_manager = None
        
        self.setup_ui()
        self.load_categories()
        
        print(f"导入分类选择对话框初始化完成，文件数量: {len(self.file_paths)}")
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("📁 选择导入分类")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 文件信息
        self.setup_file_info(layout)
        
        # 分类选择
        self.setup_category_selection(layout)
        
        # 选项设置
        self.setup_options(layout)
        
        # 按钮
        self.setup_buttons(layout)
    
    def setup_file_info(self, layout):
        """设置文件信息显示"""
        info_group = QGroupBox("📄 导入文件信息")
        info_layout = QVBoxLayout(info_group)
        
        # 文件数量
        file_count_label = QLabel(f"文件数量: {len(self.file_paths)} 个")
        file_count_label.setFont(QFont("Microsoft YaHei", 10))
        info_layout.addWidget(file_count_label)
        
        # 文件列表（如果文件不多的话）
        if len(self.file_paths) <= 10:
            file_list_text = QTextEdit()
            file_list_text.setMaximumHeight(100)
            file_list_text.setReadOnly(True)
            
            file_list = []
            for file_path in self.file_paths:
                file_name = Path(file_path).name
                file_list.append(f"• {file_name}")
            
            file_list_text.setPlainText("\n".join(file_list))
            info_layout.addWidget(file_list_text)
        else:
            # 文件太多，只显示前几个
            preview_label = QLabel("文件预览（前5个）:")
            info_layout.addWidget(preview_label)
            
            for i, file_path in enumerate(self.file_paths[:5]):
                file_name = Path(file_path).name
                file_label = QLabel(f"• {file_name}")
                info_layout.addWidget(file_label)
            
            if len(self.file_paths) > 5:
                more_label = QLabel(f"... 还有 {len(self.file_paths) - 5} 个文件")
                more_label.setStyleSheet("color: #666; font-style: italic;")
                info_layout.addWidget(more_label)
        
        layout.addWidget(info_group)
    
    def setup_category_selection(self, layout):
        """设置分类选择"""
        category_group = QGroupBox("🗂️ 选择分类")
        category_layout = QVBoxLayout(category_group)
        
        # 分类选择说明
        instruction_label = QLabel("请选择要导入到的分类：")
        instruction_label.setFont(QFont("Microsoft YaHei", 10))
        category_layout.addWidget(instruction_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 分类单选按钮组
        self.category_button_group = QButtonGroup()
        self.category_buttons = {}
        
        # 临时分组选项（默认选中）
        temp_radio = QRadioButton("📦 临时分组")
        temp_radio.setFont(QFont("Microsoft YaHei", 10))
        temp_radio.setChecked(True)  # 默认选中
        temp_radio.setStyleSheet("""
            QRadioButton {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
                margin: 2px;
                background-color: #f8f9fa;
            }
            QRadioButton:checked {
                border-color: #007bff;
                background-color: #e3f2fd;
            }
            QRadioButton:hover {
                background-color: #f0f0f0;
            }
        """)
        
        self.category_button_group.addButton(temp_radio, -1)  # ID为-1表示临时分组
        self.category_buttons["temp"] = temp_radio
        scroll_layout.addWidget(temp_radio)
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        scroll_layout.addWidget(separator)
        
        # 存储分类按钮的容器
        self.category_buttons_container = scroll_layout
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(300)
        category_layout.addWidget(scroll_area)
        
        # 快速创建新分类
        quick_create_layout = QHBoxLayout()
        quick_create_layout.addWidget(QLabel("或者快速创建新分类:"))
        
        self.new_category_name = QComboBox()
        self.new_category_name.setEditable(True)
        self.new_category_name.setPlaceholderText("输入新分类名称")
        
        # 预设分类名称
        suggested_names = ["我的收藏", "工作项目", "学习资料", "设计素材", "临时文件"]
        self.new_category_name.addItems(suggested_names)
        self.new_category_name.setCurrentText("")
        
        quick_create_layout.addWidget(self.new_category_name)
        
        create_btn = QPushButton("➕ 创建")
        create_btn.clicked.connect(self.create_new_category)
        quick_create_layout.addWidget(create_btn)
        
        category_layout.addLayout(quick_create_layout)
        
        layout.addWidget(category_group)
    
    def setup_options(self, layout):
        """设置选项"""
        options_group = QGroupBox("⚙️ 导入选项")
        options_layout = QVBoxLayout(options_group)
        
        # 自动分类选项
        self.auto_categorize_check = QCheckBox("🤖 启用智能自动分类（根据文件类型）")
        self.auto_categorize_check.setToolTip("根据文件扩展名自动分类到对应的系统分类")
        options_layout.addWidget(self.auto_categorize_check)
        
        # 重复文件处理
        self.duplicate_check = QCheckBox("🔄 检查重复文件")
        self.duplicate_check.setChecked(True)
        self.duplicate_check.setToolTip("导入时检查是否有重复文件")
        options_layout.addWidget(self.duplicate_check)
        
        # 记住选择
        self.remember_choice_check = QCheckBox("💾 记住此次选择（下次导入时默认使用）")
        self.remember_choice_check.setToolTip("将此次的分类选择保存为默认选项")
        options_layout.addWidget(self.remember_choice_check)
        
        layout.addWidget(options_group)
    
    def setup_buttons(self, layout):
        """设置按钮"""
        button_layout = QHBoxLayout()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        button_layout.addStretch()
        
        # 确定按钮
        self.confirm_btn = QPushButton("📁 确定导入")
        self.confirm_btn.setDefault(True)
        self.confirm_btn.clicked.connect(self.confirm_import)
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        button_layout.addWidget(self.confirm_btn)
        
        layout.addLayout(button_layout)
    
    def load_categories(self):
        """加载分类列表"""
        if not self.category_manager:
            print("⚠️ 分类管理器不可用，只显示临时分组")
            return
        
        try:
            # 获取所有分类
            all_categories = self.category_manager.get_all_categories()
            
            # 按类型分组
            system_categories = [cat for cat in all_categories 
                               if self._get_category_type(cat) == 'system']
            custom_categories = [cat for cat in all_categories 
                               if self._get_category_type(cat) == 'custom']
            
            button_id = 0
            
            # 添加系统分类
            if system_categories:
                system_label = QLabel("📋 系统分类:")
                system_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                system_label.setStyleSheet("color: #666; margin-top: 10px;")
                self.category_buttons_container.addWidget(system_label)
                
                for category in system_categories:
                    self._add_category_button(category, button_id)
                    button_id += 1
            
            # 添加自定义分类
            if custom_categories:
                custom_label = QLabel("📁 自定义分类:")
                custom_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                custom_label.setStyleSheet("color: #666; margin-top: 10px;")
                self.category_buttons_container.addWidget(custom_label)
                
                for category in custom_categories:
                    self._add_category_button(category, button_id)
                    button_id += 1
            
            print(f"✅ 已加载 {len(all_categories)} 个分类选项")
            
        except Exception as e:
            print(f"❌ 加载分类失败: {e}")
    
    def _get_category_type(self, category):
        """获取分类类型"""
        cat_type = category.get('type', 'unknown')
        if cat_type == 'system' or (hasattr(cat_type, 'value') and cat_type.value == 'system'):
            return 'system'
        elif cat_type == 'custom' or (hasattr(cat_type, 'value') and cat_type.value == 'custom'):
            return 'custom'
        return 'unknown'
    
    def _add_category_button(self, category, button_id):
        """添加分类按钮"""
        try:
            display_name = f"{category['icon']} {category['name']}"
            if category.get('file_count', 0) > 0:
                display_name += f" ({category['file_count']})"
            
            radio_button = QRadioButton(display_name)
            radio_button.setFont(QFont("Microsoft YaHei", 9))
            radio_button.setStyleSheet("""
                QRadioButton {
                    padding: 6px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    margin: 1px;
                    background-color: #fafafa;
                }
                QRadioButton:checked {
                    border-color: #007bff;
                    background-color: #e3f2fd;
                }
                QRadioButton:hover {
                    background-color: #f0f0f0;
                }
            """)
            
            # 设置工具提示
            if category.get('description'):
                radio_button.setToolTip(category['description'])
            
            self.category_button_group.addButton(radio_button, button_id)
            self.category_buttons[category['id']] = radio_button
            self.category_buttons_container.addWidget(radio_button)
            
        except Exception as e:
            print(f"❌ 添加分类按钮失败 {category.get('name', 'Unknown')}: {e}")
    
    def create_new_category(self):
        """创建新分类"""
        category_name = self.new_category_name.currentText().strip()
        if not category_name:
            QMessageBox.warning(self, "错误", "请输入分类名称！")
            return
        
        if not self.category_manager:
            QMessageBox.warning(self, "错误", "分类管理器不可用！")
            return
        
        try:
            # 创建新分类
            category_id = self.category_manager.add_category(
                name=category_name,
                icon="📁",
                color="#4A90E2",
                description=f"导入时创建的分类"
            )
            
            if category_id:
                QMessageBox.information(self, "成功", f"分类 '{category_name}' 创建成功！")
                
                # 重新加载分类列表
                self._clear_category_buttons()
                self.load_categories()
                
                # 自动选择新创建的分类
                if category_id in self.category_buttons:
                    self.category_buttons[category_id].setChecked(True)
                
                # 清空输入框
                self.new_category_name.setCurrentText("")
                
            else:
                QMessageBox.warning(self, "失败", "分类创建失败，可能名称已存在。")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建分类失败: {e}")
    
    def _clear_category_buttons(self):
        """清空分类按钮"""
        # 清空除了临时分组之外的所有按钮
        for category_id, button in list(self.category_buttons.items()):
            if category_id != "temp":
                self.category_button_group.removeButton(button)
                button.deleteLater()
                del self.category_buttons[category_id]
    
    def confirm_import(self):
        """确认导入"""
        # 获取选中的分类
        checked_button = self.category_button_group.checkedButton()
        if not checked_button:
            QMessageBox.warning(self, "错误", "请选择一个分类！")
            return
        
        button_id = self.category_button_group.id(checked_button)
        
        if button_id == -1:  # 临时分组
            self.selected_category_id = "temp"
            self.selected_category_name = "临时分组"
        else:
            # 查找对应的分类
            for category_id, button in self.category_buttons.items():
                if button == checked_button and category_id != "temp":
                    if self.category_manager:
                        category = self.category_manager.get_category(category_id)
                        if category:
                            self.selected_category_id = category_id
                            self.selected_category_name = category.name
                            break
        
        if not self.selected_category_id:
            QMessageBox.warning(self, "错误", "无法确定选中的分类！")
            return
        
        # 保存选择（如果用户勾选了记住选择）
        if self.remember_choice_check.isChecked():
            self._save_default_choice()
        
        # 发送信号
        self.category_selected.emit(self.selected_category_id, self.selected_category_name)
        
        print(f"✅ 用户选择导入到分类: {self.selected_category_name} (ID: {self.selected_category_id})")
        
        # 关闭对话框
        self.accept()
    
    def _save_default_choice(self):
        """保存默认选择"""
        try:
            import json
            from pathlib import Path
            
            config_dir = Path.home() / ".smart_asset_manager"
            config_dir.mkdir(parents=True, exist_ok=True)
            config_file = config_dir / "import_preferences.json"
            
            preferences = {
                "default_category_id": self.selected_category_id,
                "default_category_name": self.selected_category_name,
                "auto_categorize": self.auto_categorize_check.isChecked(),
                "check_duplicates": self.duplicate_check.isChecked()
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(preferences, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 导入偏好已保存")
            
        except Exception as e:
            print(f"❌ 保存导入偏好失败: {e}")
    
    def load_default_choice(self):
        """加载默认选择"""
        try:
            import json
            from pathlib import Path
            
            config_file = Path.home() / ".smart_asset_manager" / "import_preferences.json"
            
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    preferences = json.load(f)
                
                # 应用默认选择
                default_category_id = preferences.get("default_category_id")
                if default_category_id and default_category_id in self.category_buttons:
                    self.category_buttons[default_category_id].setChecked(True)
                
                # 应用其他选项
                self.auto_categorize_check.setChecked(preferences.get("auto_categorize", False))
                self.duplicate_check.setChecked(preferences.get("check_duplicates", True))
                
                print(f"✅ 已加载导入偏好")
                
        except Exception as e:
            print(f"❌ 加载导入偏好失败: {e}")
    
    def get_import_options(self):
        """获取导入选项"""
        return {
            "category_id": self.selected_category_id,
            "category_name": self.selected_category_name,
            "auto_categorize": self.auto_categorize_check.isChecked(),
            "check_duplicates": self.duplicate_check.isChecked(),
            "remember_choice": self.remember_choice_check.isChecked()
        }

def show_import_category_dialog(file_paths, parent=None):
    """显示导入分类选择对话框"""
    dialog = ImportCategoryDialog(file_paths, parent)
    
    # 加载默认选择
    dialog.load_default_choice()
    
    if dialog.exec() == QDialog.Accepted:
        return dialog.get_import_options()
    else:
        return None

def main():
    """测试函数"""
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 测试文件路径
    test_files = [
        "test1.jpg",
        "test2.png", 
        "test3.mp3",
        "document.pdf"
    ]
    
    result = show_import_category_dialog(test_files)
    if result:
        print(f"导入选项: {result}")
    else:
        print("用户取消了导入")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
