# 关于对话框
# 功能：显示应用程序信息、版本、开发者信息和许可证等内容

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QTextEdit, QTabWidget, QWidget,
                               QFrame, QScrollArea)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor

class AboutDialog(QDialog):
    """关于对话框类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("关于智能素材管理器")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        
        # 创建头部区域
        self.create_header_section(main_layout)
        
        # 创建标签页
        self.create_tab_widget(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
        
    def create_header_section(self, layout):
        """创建头部区域"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 20, 20, 10)
        
        # 应用图标
        icon_label = QLabel()
        icon_label.setFixedSize(64, 64)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # 创建应用图标
        icon_pixmap = self.create_app_icon()
        icon_label.setPixmap(icon_pixmap)
        
        header_layout.addWidget(icon_label)
        
        # 应用信息
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(20, 0, 0, 0)
        
        # 应用名称
        app_name_label = QLabel("智能素材管理器")
        app_name_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        info_layout.addWidget(app_name_label)
        
        # 版本信息
        version_label = QLabel("版本 1.0.0")
        version_label.setFont(QFont("Microsoft YaHei", 10))
        info_layout.addWidget(version_label)
        
        # 简短描述
        desc_label = QLabel("基于AI技术的智能素材管理解决方案")
        desc_label.setFont(QFont("Microsoft YaHei", 9))
        desc_label.setStyleSheet("color: #7f8c8d;")
        info_layout.addWidget(desc_label)
        
        info_layout.addStretch()
        
        header_layout.addWidget(info_widget)
        header_layout.addStretch()
        
        layout.addWidget(header_widget)
        
    def create_tab_widget(self, layout):
        """创建标签页控件"""
        tab_widget = QTabWidget()
        
        # 关于标签页
        about_tab = self.create_about_tab()
        tab_widget.addTab(about_tab, "关于")
        
        # 功能特性标签页
        features_tab = self.create_features_tab()
        tab_widget.addTab(features_tab, "功能特性")
        
        # 技术信息标签页
        tech_tab = self.create_tech_tab()
        tab_widget.addTab(tech_tab, "技术信息")
        
        # 许可证标签页
        license_tab = self.create_license_tab()
        tab_widget.addTab(license_tab, "许可证")
        
        layout.addWidget(tab_widget)
        
    def create_about_tab(self):
        """创建关于标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 应用描述
        desc_text = """
智能素材管理器是一款基于人工智能技术的现代化素材管理软件，
旨在帮助设计师、摄影师和内容创作者更高效地组织和管理各种
数字素材。

主要特点：
• 非侵入式管理，不移动原始文件
• AI自动标签生成和内容识别
• 强大的搜索和筛选功能
• 智能重复检测和相似度分析
• 现代化的用户界面设计
• 高性能的缩略图生成和预览

本软件采用Python和PySide6开发，集成了多种AI技术，
为用户提供智能化的素材管理体验。
        """
        
        desc_label = QLabel(desc_text.strip())
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignTop)
        desc_label.setFont(QFont("Microsoft YaHei", 9))
        layout.addWidget(desc_label)
        
        layout.addStretch()
        
        # 开发者信息
        dev_frame = QFrame()
        dev_frame.setFrameStyle(QFrame.Box)
        dev_layout = QVBoxLayout(dev_frame)
        
        dev_title = QLabel("开发者信息")
        dev_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        dev_layout.addWidget(dev_title)
        
        dev_info = QLabel("开发者: SmartAsset Team\n邮箱: <EMAIL>\n网站: www.smartasset.com")
        dev_info.setFont(QFont("Microsoft YaHei", 9))
        dev_layout.addWidget(dev_info)
        
        layout.addWidget(dev_frame)
        
        return widget
        
    def create_features_tab(self):
        """创建功能特性标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        features_text = """
核心功能：

📁 文件管理
• 支持拖拽导入文件和文件夹
• 批量文件处理和索引
• 实时文件监控和同步
• 多格式文件支持

🤖 AI智能分析
• 自动内容识别和标签生成
• 人脸检测和物体识别
• 颜色分析和调色板提取
• 图像相似度检测

🔍 强大搜索
• 多维度搜索和筛选
• 以图搜图功能
• 智能搜索建议
• 搜索历史管理

🎨 界面设计
• 现代化扁平设计
• 浅色/深色主题切换
• 响应式布局
• 流畅动画效果

⚡ 性能优化
• 虚拟滚动技术
• 多线程处理
• 智能缓存管理
• 异步加载

🔧 高级功能
• 自定义分类管理
• 收藏夹系统
• 批量操作
• 数据备份恢复
        """
        
        features_label = QLabel(features_text.strip())
        features_label.setWordWrap(True)
        features_label.setAlignment(Qt.AlignTop)
        features_label.setFont(QFont("Microsoft YaHei", 9))
        
        scroll_area = QScrollArea()
        scroll_area.setWidget(features_label)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return widget
        
    def create_tech_tab(self):
        """创建技术信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        tech_text = """
技术架构：

🐍 开发语言
• Python 3.12+
• 面向对象设计
• 模块化架构

🖼️ 界面框架
• PySide6 (Qt6)
• 现代化UI组件
• 跨平台支持

🗄️ 数据存储
• SQLite 数据库
• JSON 配置文件
• 文件系统索引

🧠 AI技术
• PIL/Pillow 图像处理
• OpenCV 计算机视觉
• scikit-learn 机器学习
• ImageHash 感知哈希

📦 依赖库
• PySide6 - GUI框架
• Pillow - 图像处理
• opencv-python - 计算机视觉
• scikit-learn - 机器学习
• pillow-heif - HEIF格式支持
• imagehash - 图像哈希

🏗️ 设计模式
• MVC架构模式
• 观察者模式
• 单例模式
• 工厂模式

⚙️ 系统要求
• Windows 10/11
• 4GB+ 内存
• 1GB+ 可用磁盘空间
• 支持OpenGL的显卡
        """
        
        tech_label = QLabel(tech_text.strip())
        tech_label.setWordWrap(True)
        tech_label.setAlignment(Qt.AlignTop)
        tech_label.setFont(QFont("Microsoft YaHei", 9))
        
        scroll_area = QScrollArea()
        scroll_area.setWidget(tech_label)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return widget
        
    def create_license_tab(self):
        """创建许可证标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        license_text = """
MIT License

Copyright (c) 2024 SmartAsset Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

第三方库许可证：

本软件使用了以下开源库：
• PySide6 - LGPL v3
• Pillow - PIL Software License
• OpenCV - Apache 2.0
• scikit-learn - BSD 3-Clause
• ImageHash - BSD 2-Clause

详细的第三方库许可证信息请参考各库的官方文档。
        """
        
        license_edit = QTextEdit()
        license_edit.setPlainText(license_text.strip())
        license_edit.setReadOnly(True)
        license_edit.setFont(QFont("Consolas", 8))
        layout.addWidget(license_edit)
        
        return widget
        
    def create_button_section(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 确定按钮
        ok_button = QPushButton("确定")
        ok_button.setDefault(True)
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)
        
        layout.addLayout(button_layout)
        
    def create_app_icon(self):
        """创建应用图标"""
        # 创建一个简单的应用图标
        pixmap = QPixmap(64, 64)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制背景圆形
        painter.setBrush(QColor("#3498db"))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(4, 4, 56, 56)
        
        # 绘制文件夹图标
        painter.setBrush(QColor("#ffffff"))
        painter.drawRect(16, 20, 32, 24)
        painter.drawRect(16, 16, 16, 4)
        
        painter.end()
        
        return pixmap
