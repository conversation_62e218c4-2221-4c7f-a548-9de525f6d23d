#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能预加载系统
基于用户行为分析和机器学习的智能预加载
"""

import time
import threading
import pickle
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass
import concurrent.futures
import numpy as np
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

from PySide6.QtCore import QObject, Signal, QTimer

@dataclass
class UserAction:
    """用户行为数据"""
    timestamp: float
    action_type: str  # 'view', 'search', 'sort', 'filter', 'select'
    target: str       # 目标对象ID或路径
    context: Dict[str, Any]  # 上下文信息
    duration: float = 0.0    # 持续时间

class BehaviorAnalyzer:
    """用户行为分析器"""
    
    def __init__(self):
        self.action_history = deque(maxlen=10000)
        self.patterns = {}
        self.preferences = {}
        self.lock = threading.RLock()
        
        # 分析参数
        self.pattern_window = 100  # 模式分析窗口
        self.min_pattern_support = 3  # 最小模式支持度
        
    def record_action(self, action: UserAction):
        """记录用户行为"""
        with self.lock:
            self.action_history.append(action)
            
            # 定期分析模式
            if len(self.action_history) % 50 == 0:
                self._analyze_patterns()
    
    def _analyze_patterns(self):
        """分析用户行为模式"""
        try:
            recent_actions = list(self.action_history)[-self.pattern_window:]
            
            # 分析序列模式
            self._analyze_sequence_patterns(recent_actions)
            
            # 分析时间模式
            self._analyze_temporal_patterns(recent_actions)
            
            # 分析偏好模式
            self._analyze_preference_patterns(recent_actions)
            
        except Exception as e:
            print(f"行为模式分析失败: {e}")
    
    def _analyze_sequence_patterns(self, actions: List[UserAction]):
        """分析序列模式"""
        # 提取行为序列
        sequences = []
        current_sequence = []
        
        for action in actions:
            current_sequence.append(action.action_type)
            
            # 如果间隔超过5分钟，开始新序列
            if len(current_sequence) > 1:
                time_gap = action.timestamp - actions[actions.index(action)-1].timestamp
                if time_gap > 300:  # 5分钟
                    if len(current_sequence) > 2:
                        sequences.append(current_sequence[:-1])
                    current_sequence = [action.action_type]
        
        if len(current_sequence) > 2:
            sequences.append(current_sequence)
        
        # 查找频繁序列模式
        pattern_counts = defaultdict(int)
        for sequence in sequences:
            for i in range(len(sequence) - 1):
                pattern = tuple(sequence[i:i+2])
                pattern_counts[pattern] += 1
        
        # 更新模式
        self.patterns['sequences'] = {
            pattern: count for pattern, count in pattern_counts.items()
            if count >= self.min_pattern_support
        }
    
    def _analyze_temporal_patterns(self, actions: List[UserAction]):
        """分析时间模式"""
        # 按小时分组
        hourly_actions = defaultdict(list)
        for action in actions:
            hour = time.localtime(action.timestamp).tm_hour
            hourly_actions[hour].append(action)
        
        # 计算每小时的活跃度
        hourly_activity = {}
        for hour, hour_actions in hourly_actions.items():
            activity_score = len(hour_actions)
            hourly_activity[hour] = activity_score
        
        self.patterns['temporal'] = hourly_activity
    
    def _analyze_preference_patterns(self, actions: List[UserAction]):
        """分析偏好模式"""
        # 文件类型偏好
        file_type_counts = defaultdict(int)
        action_type_counts = defaultdict(int)
        
        for action in actions:
            action_type_counts[action.action_type] += 1
            
            # 从上下文中提取文件类型
            if 'file_type' in action.context:
                file_type_counts[action.context['file_type']] += 1
        
        self.preferences = {
            'file_types': dict(file_type_counts),
            'action_types': dict(action_type_counts)
        }
    
    def predict_next_actions(self, current_action: str, context: Dict[str, Any]) -> List[Tuple[str, float]]:
        """预测下一个可能的行为"""
        predictions = []
        
        # 基于序列模式预测
        sequence_patterns = self.patterns.get('sequences', {})
        for pattern, count in sequence_patterns.items():
            if pattern[0] == current_action:
                confidence = count / sum(sequence_patterns.values())
                predictions.append((pattern[1], confidence))
        
        # 基于偏好预测
        action_preferences = self.preferences.get('action_types', {})
        total_actions = sum(action_preferences.values())
        
        if total_actions > 0:
            for action_type, count in action_preferences.items():
                if action_type != current_action:
                    preference_score = count / total_actions
                    predictions.append((action_type, preference_score * 0.5))  # 降低权重
        
        # 合并和排序预测
        prediction_dict = defaultdict(float)
        for action, confidence in predictions:
            prediction_dict[action] += confidence
        
        sorted_predictions = sorted(prediction_dict.items(), key=lambda x: x[1], reverse=True)
        return sorted_predictions[:5]  # 返回前5个预测
    
    def get_preferred_file_types(self) -> List[str]:
        """获取偏好的文件类型"""
        file_types = self.preferences.get('file_types', {})
        return sorted(file_types.keys(), key=file_types.get, reverse=True)

class MLPredictor:
    """机器学习预测器"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.is_trained = False
        
    def extract_features(self, actions: List[UserAction]) -> np.ndarray:
        """提取特征"""
        if not actions:
            return np.array([])
        
        features = []
        
        # 时间特征
        timestamps = [action.timestamp for action in actions]
        if len(timestamps) > 1:
            time_intervals = np.diff(timestamps)
            features.extend([
                np.mean(time_intervals),
                np.std(time_intervals),
                np.min(time_intervals),
                np.max(time_intervals)
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        # 行为类型分布
        action_types = ['view', 'search', 'sort', 'filter', 'select']
        action_counts = {action_type: 0 for action_type in action_types}
        
        for action in actions:
            if action.action_type in action_counts:
                action_counts[action.action_type] += 1
        
        total_actions = len(actions)
        action_ratios = [action_counts[action_type] / total_actions for action_type in action_types]
        features.extend(action_ratios)
        
        # 持续时间特征
        durations = [action.duration for action in actions if action.duration > 0]
        if durations:
            features.extend([
                np.mean(durations),
                np.std(durations),
                np.median(durations)
            ])
        else:
            features.extend([0, 0, 0])
        
        return np.array(features)
    
    def train(self, behavior_data: List[List[UserAction]]):
        """训练模型"""
        try:
            # 提取特征
            feature_matrix = []
            for session_actions in behavior_data:
                features = self.extract_features(session_actions)
                if len(features) > 0:
                    feature_matrix.append(features)
            
            if len(feature_matrix) < 5:  # 数据不足
                return False
            
            X = np.array(feature_matrix)
            
            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)
            
            # 使用K-means聚类识别用户行为模式
            n_clusters = min(5, len(feature_matrix) // 2)
            self.model = KMeans(n_clusters=n_clusters, random_state=42)
            self.model.fit(X_scaled)
            
            self.is_trained = True
            return True
            
        except Exception as e:
            print(f"ML模型训练失败: {e}")
            return False
    
    def predict_cluster(self, actions: List[UserAction]) -> Optional[int]:
        """预测用户行为聚类"""
        if not self.is_trained:
            return None
        
        try:
            features = self.extract_features(actions)
            if len(features) == 0:
                return None
            
            features_scaled = self.scaler.transform([features])
            cluster = self.model.predict(features_scaled)[0]
            return cluster
            
        except Exception as e:
            print(f"ML预测失败: {e}")
            return None

class SmartPreloader(QObject):
    """智能预加载器"""
    
    # 信号定义
    preload_started = Signal(str, int)  # 预加载开始
    preload_completed = Signal(str, bool)  # 预加载完成
    prediction_updated = Signal(list)  # 预测更新
    
    def __init__(self):
        super().__init__()
        
        self.behavior_analyzer = BehaviorAnalyzer()
        self.ml_predictor = MLPredictor()
        
        # 预加载配置
        self.config = {
            'max_concurrent_preloads': 3,
            'preload_timeout': 30.0,
            'prediction_threshold': 0.3,
            'cache_size_limit': 100,
            'enable_ml_prediction': True
        }
        
        # 预加载管理
        self.preload_queue = deque()
        self.active_preloads = {}
        self.preload_cache = {}
        self.preload_executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.config['max_concurrent_preloads']
        )
        
        # 预加载器注册
        self.preloaders = {}
        
        # 定期训练定时器
        self.training_timer = QTimer()
        self.training_timer.timeout.connect(self._periodic_training)
        self.training_timer.start(300000)  # 5分钟
        
        self.lock = threading.RLock()
    
    def register_preloader(self, resource_type: str, preloader_func: Callable[[str], Any]):
        """注册预加载器"""
        self.preloaders[resource_type] = preloader_func
    
    def record_user_action(self, action_type: str, target: str, context: Dict[str, Any] = None, duration: float = 0.0):
        """记录用户行为"""
        action = UserAction(
            timestamp=time.time(),
            action_type=action_type,
            target=target,
            context=context or {},
            duration=duration
        )
        
        self.behavior_analyzer.record_action(action)
        
        # 触发预测和预加载
        self._trigger_smart_preload(action)
    
    def _trigger_smart_preload(self, current_action: UserAction):
        """触发智能预加载"""
        try:
            # 获取行为预测
            predictions = self.behavior_analyzer.predict_next_actions(
                current_action.action_type, current_action.context
            )
            
            # 发送预测更新信号
            self.prediction_updated.emit(predictions)
            
            # 基于预测进行预加载
            for predicted_action, confidence in predictions:
                if confidence >= self.config['prediction_threshold']:
                    self._schedule_preload(predicted_action, current_action.context, confidence)
                    
        except Exception as e:
            print(f"智能预加载触发失败: {e}")
    
    def _schedule_preload(self, action_type: str, context: Dict[str, Any], confidence: float):
        """调度预加载任务"""
        with self.lock:
            # 根据行为类型确定预加载资源
            preload_targets = self._determine_preload_targets(action_type, context)
            
            for resource_type, target in preload_targets:
                if resource_type in self.preloaders:
                    preload_key = f"{resource_type}:{target}"
                    
                    # 避免重复预加载
                    if (preload_key not in self.active_preloads and 
                        preload_key not in self.preload_cache):
                        
                        self.preload_queue.append((preload_key, resource_type, target, confidence))
            
            # 处理预加载队列
            self._process_preload_queue()
    
    def _determine_preload_targets(self, action_type: str, context: Dict[str, Any]) -> List[Tuple[str, str]]:
        """确定预加载目标"""
        targets = []
        
        if action_type == 'view':
            # 预加载相关文件的缩略图
            if 'file_path' in context:
                targets.append(('thumbnail', context['file_path']))
                
                # 预加载同目录的其他文件
                file_path = Path(context['file_path'])
                parent_dir = file_path.parent
                targets.append(('directory_listing', str(parent_dir)))
        
        elif action_type == 'search':
            # 预加载搜索结果的缩略图
            if 'query' in context:
                targets.append(('search_results', context['query']))
        
        elif action_type == 'sort':
            # 预加载排序后的数据
            if 'sort_key' in context:
                targets.append(('sorted_data', context['sort_key']))
        
        elif action_type == 'filter':
            # 预加载过滤结果
            if 'filter_type' in context:
                targets.append(('filtered_data', context['filter_type']))
        
        return targets
    
    def _process_preload_queue(self):
        """处理预加载队列"""
        while (self.preload_queue and 
               len(self.active_preloads) < self.config['max_concurrent_preloads']):
            
            preload_key, resource_type, target, confidence = self.preload_queue.popleft()
            
            # 提交预加载任务
            future = self.preload_executor.submit(
                self._execute_preload, preload_key, resource_type, target
            )
            
            self.active_preloads[preload_key] = {
                'future': future,
                'start_time': time.time(),
                'confidence': confidence
            }
            
            # 设置完成回调
            future.add_done_callback(lambda f, key=preload_key: self._on_preload_completed(key, f))
            
            self.preload_started.emit(preload_key, len(self.active_preloads))
    
    def _execute_preload(self, preload_key: str, resource_type: str, target: str) -> Any:
        """执行预加载"""
        try:
            preloader_func = self.preloaders.get(resource_type)
            if preloader_func:
                result = preloader_func(target)
                return result
            else:
                print(f"未找到预加载器: {resource_type}")
                return None
                
        except Exception as e:
            print(f"预加载执行失败 {preload_key}: {e}")
            return None
    
    def _on_preload_completed(self, preload_key: str, future: concurrent.futures.Future):
        """预加载完成回调"""
        with self.lock:
            try:
                result = future.result(timeout=1.0)
                success = result is not None
                
                if success:
                    # 添加到缓存
                    self.preload_cache[preload_key] = {
                        'data': result,
                        'timestamp': time.time(),
                        'access_count': 0
                    }
                    
                    # 限制缓存大小
                    self._cleanup_cache()
                
                self.preload_completed.emit(preload_key, success)
                
            except Exception as e:
                print(f"预加载完成处理失败 {preload_key}: {e}")
                self.preload_completed.emit(preload_key, False)
            
            finally:
                # 清理活跃预加载
                if preload_key in self.active_preloads:
                    del self.active_preloads[preload_key]
                
                # 继续处理队列
                self._process_preload_queue()
    
    def _cleanup_cache(self):
        """清理缓存"""
        if len(self.preload_cache) <= self.config['cache_size_limit']:
            return
        
        # 按访问时间排序，移除最旧的项
        sorted_items = sorted(
            self.preload_cache.items(),
            key=lambda x: (x[1]['access_count'], x[1]['timestamp'])
        )
        
        # 移除最旧的20%
        remove_count = len(sorted_items) // 5
        for i in range(remove_count):
            key = sorted_items[i][0]
            del self.preload_cache[key]
    
    def get_preloaded_data(self, resource_type: str, target: str) -> Optional[Any]:
        """获取预加载的数据"""
        preload_key = f"{resource_type}:{target}"
        
        with self.lock:
            if preload_key in self.preload_cache:
                cache_item = self.preload_cache[preload_key]
                cache_item['access_count'] += 1
                return cache_item['data']
        
        return None
    
    def _periodic_training(self):
        """定期训练ML模型"""
        if not self.config['enable_ml_prediction']:
            return
        
        try:
            # 收集训练数据
            actions = list(self.behavior_analyzer.action_history)
            if len(actions) < 100:  # 数据不足
                return
            
            # 按会话分组（5分钟间隔）
            sessions = []
            current_session = []
            
            for i, action in enumerate(actions):
                if i > 0:
                    time_gap = action.timestamp - actions[i-1].timestamp
                    if time_gap > 300:  # 5分钟
                        if len(current_session) > 5:
                            sessions.append(current_session)
                        current_session = [action]
                        continue
                
                current_session.append(action)
            
            if len(current_session) > 5:
                sessions.append(current_session)
            
            # 训练模型
            if len(sessions) >= 5:
                success = self.ml_predictor.train(sessions)
                if success:
                    print("ML预测模型训练完成")
                    
        except Exception as e:
            print(f"定期训练失败: {e}")
    
    def get_preload_stats(self) -> Dict[str, Any]:
        """获取预加载统计"""
        with self.lock:
            return {
                'active_preloads': len(self.active_preloads),
                'cached_items': len(self.preload_cache),
                'queue_size': len(self.preload_queue),
                'cache_hit_ratio': self._calculate_cache_hit_ratio(),
                'ml_model_trained': self.ml_predictor.is_trained
            }
    
    def _calculate_cache_hit_ratio(self) -> float:
        """计算缓存命中率"""
        total_access = sum(item['access_count'] for item in self.preload_cache.values())
        if total_access == 0:
            return 0.0
        
        hits = sum(1 for item in self.preload_cache.values() if item['access_count'] > 0)
        return hits / len(self.preload_cache) if self.preload_cache else 0.0
    
    def cleanup(self):
        """清理资源"""
        self.training_timer.stop()
        self.preload_executor.shutdown(wait=True)

# 全局智能预加载器实例
_smart_preloader = None

def get_smart_preloader() -> SmartPreloader:
    """获取全局智能预加载器实例"""
    global _smart_preloader
    if _smart_preloader is None:
        _smart_preloader = SmartPreloader()
    return _smart_preloader

def cleanup_smart_preloader():
    """清理全局智能预加载器"""
    global _smart_preloader
    if _smart_preloader:
        _smart_preloader.cleanup()
        _smart_preloader = None
