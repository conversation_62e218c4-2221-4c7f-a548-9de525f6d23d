# 文件管理核心模块
# 功能：文件导入、索引、缩略图生成、文件监控等核心文件管理功能

import os
import hashlib
import mimetypes
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import threading
import queue
import time

from PySide6.QtCore import QObject, Signal, QThread, QTimer, QFileSystemWatcher
from PySide6.QtGui import QPixmap, QImage
from PIL import Image, ImageHash
import pillow_heif

# 注册HEIF支持
pillow_heif.register_heif_opener()

class FileManager(QObject):
    """文件管理器类"""
    
    # 信号定义
    file_imported = Signal(dict)  # 文件导入信号
    import_progress = Signal(int, int, str)  # 导入进度信号
    import_completed = Signal(int)  # 导入完成信号
    file_changed = Signal(str)  # 文件变更信号
    thumbnail_generated = Signal(str, str)  # 缩略图生成信号
    
    def __init__(self, db_manager, config_manager):
        super().__init__()
        
        self.db_manager = db_manager
        self.config_manager = config_manager
        
        # 文件监控器
        self.file_watcher = QFileSystemWatcher()
        self.file_watcher.fileChanged.connect(self.on_file_changed)
        self.file_watcher.directoryChanged.connect(self.on_directory_changed)
        
        # 工作线程
        self.import_thread = None
        self.thumbnail_thread = None
        
        # 支持的文件格式
        self.supported_formats = self.config_manager.get_file_management_config().get('supported_formats', [])
        
        # 缩略图设置
        self.thumbnail_config = {
            'quality': self.config_manager.get_file_management_config().get('thumbnail_quality', 85),
            'max_size': self.config_manager.get_file_management_config().get('max_thumbnail_size', 300),
            'generate': self.config_manager.get_file_management_config().get('generate_thumbnails', True)
        }
        
    def import_files(self, file_paths: List[str], callback=None):
        """导入文件列表"""
        if self.import_thread and self.import_thread.isRunning():
            print("导入任务正在进行中...")
            return
            
        # 创建导入线程
        self.import_thread = FileImportThread(
            file_paths, 
            self.db_manager, 
            self.config_manager,
            self.supported_formats
        )
        
        # 连接信号
        self.import_thread.file_imported.connect(self.file_imported.emit)
        self.import_thread.progress_updated.connect(self.import_progress.emit)
        self.import_thread.import_completed.connect(self.import_completed.emit)
        
        if callback:
            self.import_thread.import_completed.connect(callback)
            
        # 启动线程
        self.import_thread.start()
        
    def import_directory(self, directory_path: str, recursive: bool = True, callback=None):
        """导入目录"""
        try:
            file_paths = []
            directory = Path(directory_path)
            
            if recursive:
                # 递归搜索所有支持的文件
                for ext in self.supported_formats:
                    pattern = f"**/*{ext}"
                    file_paths.extend([str(p) for p in directory.glob(pattern)])
            else:
                # 只搜索当前目录
                for ext in self.supported_formats:
                    pattern = f"*{ext}"
                    file_paths.extend([str(p) for p in directory.glob(pattern)])
                    
            if file_paths:
                self.import_files(file_paths, callback)
            else:
                print(f"在目录 {directory_path} 中未找到支持的文件")
                
        except Exception as e:
            print(f"导入目录失败: {e}")
            
    def generate_thumbnail(self, file_path: str, output_path: str = None) -> Optional[str]:
        """生成缩略图"""
        try:
            if not self.thumbnail_config['generate']:
                return None
                
            # 确定输出路径
            if not output_path:
                thumbnails_dir = self.config_manager.get_thumbnails_dir()
                file_hash = self.calculate_file_hash(file_path)
                output_path = thumbnails_dir / f"{file_hash}.jpg"
                
            # 检查缩略图是否已存在
            if os.path.exists(output_path):
                return str(output_path)
                
            # 根据文件类型生成缩略图
            mime_type, _ = mimetypes.guess_type(file_path)
            
            if mime_type and mime_type.startswith('image'):
                return self._generate_image_thumbnail(file_path, output_path)
            elif mime_type and mime_type.startswith('video'):
                return self._generate_video_thumbnail(file_path, output_path)
            else:
                return self._generate_default_thumbnail(file_path, output_path)
                
        except Exception as e:
            print(f"生成缩略图失败: {e}")
            return None
            
    def _generate_image_thumbnail(self, file_path: str, output_path: str) -> str:
        """生成图片缩略图"""
        try:
            with Image.open(file_path) as img:
                # 转换为RGB模式
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                    
                # 计算缩略图尺寸
                max_size = self.thumbnail_config['max_size']
                img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
                
                # 保存缩略图
                img.save(output_path, 'JPEG', quality=self.thumbnail_config['quality'])
                
            return str(output_path)
            
        except Exception as e:
            print(f"生成图片缩略图失败: {e}")
            return None
            
    def _generate_video_thumbnail(self, file_path: str, output_path: str) -> str:
        """生成视频缩略图"""
        try:
            # TODO: 使用ffmpeg或其他库生成视频缩略图
            # 这里先返回默认缩略图
            return self._generate_default_thumbnail(file_path, output_path)
            
        except Exception as e:
            print(f"生成视频缩略图失败: {e}")
            return None
            
    def _generate_default_thumbnail(self, file_path: str, output_path: str) -> str:
        """生成默认缩略图"""
        try:
            # 创建默认缩略图（纯色背景 + 文件类型图标）
            max_size = self.thumbnail_config['max_size']
            img = Image.new('RGB', (max_size, max_size), color='#f8f9fa')
            
            # TODO: 添加文件类型图标
            
            img.save(output_path, 'JPEG', quality=self.thumbnail_config['quality'])
            return str(output_path)
            
        except Exception as e:
            print(f"生成默认缩略图失败: {e}")
            return None
            
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件MD5哈希"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return ""
            
    def calculate_image_hash(self, file_path: str) -> str:
        """计算图片感知哈希"""
        try:
            with Image.open(file_path) as img:
                # 使用平均哈希算法
                hash_value = ImageHash.average_hash(img)
                return str(hash_value)
        except Exception as e:
            print(f"计算图片哈希失败: {e}")
            return ""
            
    def extract_file_metadata(self, file_path: str) -> Dict[str, Any]:
        """提取文件元数据"""
        try:
            file_stat = os.stat(file_path)
            file_path_obj = Path(file_path)
            
            metadata = {
                'name': file_path_obj.name,
                'file_path': str(file_path_obj.absolute()),
                'file_type': self._get_file_type(file_path),
                'mime_type': mimetypes.guess_type(file_path)[0],
                'size': file_stat.st_size,
                'created_time': datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                'modified_time': datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                'md5_hash': self.calculate_file_hash(file_path)
            }
            
            # 如果是图片，提取额外信息
            if metadata['file_type'] == 'image':
                image_info = self._extract_image_info(file_path)
                metadata.update(image_info)
                
            return metadata
            
        except Exception as e:
            print(f"提取文件元数据失败: {e}")
            return {}
            
    def _get_file_type(self, file_path: str) -> str:
        """获取文件类型"""
        mime_type, _ = mimetypes.guess_type(file_path)
        
        if mime_type:
            if mime_type.startswith('image'):
                return 'image'
            elif mime_type.startswith('video'):
                return 'video'
            elif mime_type.startswith('audio'):
                return 'audio'
            elif mime_type in ['application/pdf']:
                return 'document'
            elif mime_type in ['application/x-photoshop', 'application/postscript']:
                return 'design'
                
        # 根据扩展名判断
        ext = Path(file_path).suffix.lower()
        if ext in ['.psd', '.ai', '.eps', '.svg']:
            return 'design'
        elif ext in ['.pdf', '.doc', '.docx', '.txt']:
            return 'document'
            
        return 'other'
        
    def _extract_image_info(self, file_path: str) -> Dict[str, Any]:
        """提取图片信息"""
        try:
            with Image.open(file_path) as img:
                info = {
                    'width': img.width,
                    'height': img.height,
                    'image_hash': self.calculate_image_hash(file_path)
                }
                
                # 提取EXIF信息
                if hasattr(img, '_getexif') and img._getexif():
                    exif_data = img._getexif()
                    info['metadata'] = str(exif_data)
                    
                return info
                
        except Exception as e:
            print(f"提取图片信息失败: {e}")
            return {}
            
    def add_watch_path(self, path: str):
        """添加监控路径"""
        if os.path.exists(path):
            self.file_watcher.addPath(path)
            
    def remove_watch_path(self, path: str):
        """移除监控路径"""
        self.file_watcher.removePath(path)
        
    def on_file_changed(self, path: str):
        """文件变更处理"""
        self.file_changed.emit(path)
        
    def on_directory_changed(self, path: str):
        """目录变更处理"""
        # TODO: 实现目录变更处理逻辑
        pass

class FileImportThread(QThread):
    """文件导入线程"""
    
    file_imported = Signal(dict)
    progress_updated = Signal(int, int, str)
    import_completed = Signal(int)
    
    def __init__(self, file_paths, db_manager, config_manager, supported_formats):
        super().__init__()
        
        self.file_paths = file_paths
        self.db_manager = db_manager
        self.config_manager = config_manager
        self.supported_formats = supported_formats
        self.file_manager = FileManager(db_manager, config_manager)
        
    def run(self):
        """运行导入任务"""
        total_files = len(self.file_paths)
        imported_count = 0
        
        for i, file_path in enumerate(self.file_paths):
            try:
                # 发送进度信号
                self.progress_updated.emit(i + 1, total_files, file_path)
                
                # 检查文件是否已存在
                existing_file = self.db_manager.get_material_by_path(file_path)
                if existing_file:
                    continue
                    
                # 检查文件格式
                if not self._is_supported_file(file_path):
                    continue
                    
                # 提取文件元数据
                metadata = self.file_manager.extract_file_metadata(file_path)
                if not metadata:
                    continue
                    
                # 生成缩略图
                thumbnail_path = self.file_manager.generate_thumbnail(file_path)
                if thumbnail_path:
                    metadata['thumbnail_path'] = thumbnail_path
                    
                # 添加到数据库
                material_id = self.db_manager.add_material(metadata)
                if material_id:
                    metadata['id'] = material_id
                    self.file_imported.emit(metadata)
                    imported_count += 1
                    
            except Exception as e:
                print(f"导入文件 {file_path} 失败: {e}")
                continue
                
        # 发送完成信号
        self.import_completed.emit(imported_count)
        
    def _is_supported_file(self, file_path: str) -> bool:
        """检查文件是否支持"""
        ext = Path(file_path).suffix.lower()
        return ext in self.supported_formats
