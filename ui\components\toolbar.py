# 工具栏组件
# 功能：主工具栏，包含搜索框、视图切换按钮、导入按钮等主要操作控件

from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel,
                               QPushButton, QLineEdit, QComboBox, QFrame,
                               QButtonGroup, QToolButton, QMenu,
                               QSizePolicy, QSpacerItem)
from PySide6.QtCore import Qt, Signal, QSize, QTimer
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont, QKeySequence, QAction

class MainToolBar(QWidget):
    """主工具栏控件类"""

    # 信号定义
    search_requested = Signal(str, dict)  # 搜索请求信号
    view_mode_changed = Signal(str)  # 视图模式变更信号
    import_requested = Signal()  # 导入请求信号
    refresh_requested = Signal()  # 刷新请求信号
    settings_requested = Signal()  # 设置请求信号

    def __init__(self, parent=None):
        super().__init__(parent)

        # 界面组件
        self.search_input = None
        self.search_filters = None
        self.view_buttons = None
        self.import_button = None
        self.refresh_button = None
        self.settings_button = None

        # 状态
        self.current_view_mode = "grid"
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置用户界面"""
        # 设置固定高度
        self.setFixedHeight(60)

        # 创建主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(15, 10, 15, 10)
        main_layout.setSpacing(15)

        # 左侧：导入和刷新按钮
        self.create_left_section(main_layout)

        # 中间：搜索区域
        self.create_search_section(main_layout)

        # 右侧：视图控制和设置
        self.create_right_section(main_layout)

        # 设置样式
        self.setStyleSheet("""
            MainToolBar {
                background-color: #ecf0f1;
                border-bottom: 1px solid #bdc3c7;
            }
        """)

    def create_left_section(self, layout):
        """创建左侧区域"""
        # 导入按钮
        self.import_button = ToolBarButton("📁", "导入文件")
        self.import_button.clicked.connect(self.import_requested.emit)
        layout.addWidget(self.import_button)

        # 导入菜单按钮
        import_menu_button = ToolBarMenuButton("📂", "导入选项")
        import_menu = QMenu()

        import_files_action = QAction("导入文件", self)
        import_files_action.setShortcut(QKeySequence.Open)
        import_files_action.triggered.connect(self.import_files)
        import_menu.addAction(import_files_action)

        import_folder_action = QAction("导入文件夹", self)
        import_folder_action.setShortcut(QKeySequence("Ctrl+Shift+O"))
        import_folder_action.triggered.connect(self.import_folder)
        import_menu.addAction(import_folder_action)

        import_menu.addSeparator()

        import_url_action = QAction("从URL导入", self)
        import_url_action.triggered.connect(self.import_from_url)
        import_menu.addAction(import_url_action)

        import_menu_button.setMenu(import_menu)
        layout.addWidget(import_menu_button)

        # 分隔线
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.VLine)
        separator1.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator1)

        # 刷新按钮
        self.refresh_button = ToolBarButton("🔄", "刷新")
        self.refresh_button.clicked.connect(self.refresh_requested.emit)
        layout.addWidget(self.refresh_button)

    def create_search_section(self, layout):
        """创建搜索区域"""
        # 搜索容器
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(10)

        # 搜索输入框
        self.search_input = SearchLineEdit()
        self.search_input.setPlaceholderText("搜索素材...")
        self.search_input.setMinimumWidth(300)
        self.search_input.textChanged.connect(self.on_search_text_changed)
        self.search_input.returnPressed.connect(self.perform_search)
        search_layout.addWidget(self.search_input)

        # 高级搜索按钮
        advanced_search_button = ToolBarButton("⚙️", "高级搜索")
        advanced_search_button.clicked.connect(self.show_advanced_search)
        search_layout.addWidget(advanced_search_button)

        # 搜索筛选器
        self.search_filters = SearchFiltersWidget()
        search_layout.addWidget(self.search_filters)

        layout.addWidget(search_container)

    def create_right_section(self, layout):
        """创建右侧区域"""
        # 添加弹性空间
        layout.addStretch()

        # 视图模式按钮组
        view_container = QWidget()
        view_layout = QHBoxLayout(view_container)
        view_layout.setContentsMargins(0, 0, 0, 0)
        view_layout.setSpacing(2)

        # 创建按钮组
        self.view_button_group = QButtonGroup()

        # 网格视图按钮
        grid_button = ViewModeButton("grid", "⊞", "网格视图")
        grid_button.setChecked(True)
        self.view_button_group.addButton(grid_button, 0)
        view_layout.addWidget(grid_button)

        # 列表视图按钮
        list_button = ViewModeButton("list", "☰", "列表视图")
        self.view_button_group.addButton(list_button, 1)
        view_layout.addWidget(list_button)

        # 详细视图按钮
        detail_button = ViewModeButton("detail", "📋", "详细视图")
        self.view_button_group.addButton(detail_button, 2)
        view_layout.addWidget(detail_button)

        layout.addWidget(view_container)

        # 分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator2)

        # 设置按钮
        self.settings_button = ToolBarButton("⚙️", "设置")
        self.settings_button.clicked.connect(self.settings_requested.emit)
        layout.addWidget(self.settings_button)

    def setup_connections(self):
        """设置信号连接"""
        # 视图模式按钮组信号
        self.view_button_group.buttonClicked.connect(self.on_view_mode_changed)

        # 搜索筛选器信号
        self.search_filters.filter_changed.connect(self.on_filter_changed)

    def on_search_text_changed(self, text: str):
        """搜索文本变更处理"""
        # 延迟搜索，避免频繁触发
        self.search_timer.stop()
        self.search_timer.start(500)  # 500ms延迟

    def perform_search(self):
        """执行搜索"""
        query = self.search_input.text().strip()
        filters = self.search_filters.get_filters()
        self.search_requested.emit(query, filters)

    def on_view_mode_changed(self, button):
        """视图模式变更处理"""
        if hasattr(button, 'view_mode'):
            self.current_view_mode = button.view_mode
            self.view_mode_changed.emit(self.current_view_mode)

    def on_filter_changed(self):
        """筛选器变更处理"""
        # 当筛选器变更时，立即执行搜索
        self.perform_search()

    def set_view_mode(self, mode: str):
        """设置视图模式"""
        self.current_view_mode = mode

        # 更新按钮状态
        for button in self.view_button_group.buttons():
            if hasattr(button, 'view_mode') and button.view_mode == mode:
                button.setChecked(True)
                break

    def clear_search(self):
        """清空搜索"""
        self.search_input.clear()
        self.search_filters.clear_filters()

    def set_search_text(self, text: str):
        """设置搜索文本"""
        self.search_input.setText(text)

    # 导入相关方法
    def import_files(self):
        """导入文件"""
        self.import_requested.emit()

    def import_folder(self):
        """导入文件夹"""
        # TODO: 实现文件夹导入
        pass

    def import_from_url(self):
        """从URL导入"""
        # TODO: 实现URL导入
        pass

    def show_advanced_search(self):
        """显示高级搜索"""
        # TODO: 实现高级搜索对话框
        pass

class ToolBarButton(QPushButton):
    """工具栏按钮基类"""

    def __init__(self, icon_text: str, tooltip: str = ""):
        super().__init__(icon_text)

        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)

        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 6px;
                font-size: 16px;
                padding: 4px;
            }
            QPushButton:hover {
                background-color: #d5dbdb;
                border-color: #bdc3c7;
            }
            QPushButton:pressed {
                background-color: #bdc3c7;
            }
        """)

class ToolBarMenuButton(QToolButton):
    """工具栏菜单按钮类"""

    def __init__(self, icon_text: str, tooltip: str = ""):
        super().__init__()

        self.setText(icon_text)
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)
        self.setPopupMode(QToolButton.InstantPopup)

        # 设置样式
        self.setStyleSheet("""
            QToolButton {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 6px;
                font-size: 16px;
                padding: 4px;
            }
            QToolButton:hover {
                background-color: #d5dbdb;
                border-color: #bdc3c7;
            }
            QToolButton:pressed {
                background-color: #bdc3c7;
            }
            QToolButton::menu-indicator {
                image: none;
            }
        """)

class ViewModeButton(QPushButton):
    """视图模式按钮类"""

    def __init__(self, view_mode: str, icon_text: str, tooltip: str = ""):
        super().__init__(icon_text)

        self.view_mode = view_mode
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)
        self.setCheckable(True)

        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 6px;
                font-size: 16px;
                padding: 4px;
            }
            QPushButton:hover {
                background-color: #d5dbdb;
                border-color: #bdc3c7;
            }
            QPushButton:checked {
                background-color: #3498db;
                color: white;
                border-color: #2980b9;
            }
            QPushButton:checked:hover {
                background-color: #5dade2;
            }
        """)

class SearchLineEdit(QLineEdit):
    """搜索输入框类"""

    def __init__(self):
        super().__init__()

        # 设置样式
        self.setStyleSheet("""
            QLineEdit {
                background-color: #ffffff;
                border: 2px solid #bdc3c7;
                border-radius: 20px;
                padding: 8px 15px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

class SearchFiltersWidget(QWidget):
    """搜索筛选器控件"""

    filter_changed = Signal()

    def __init__(self):
        super().__init__()

        self.file_type_combo = None
        self.date_combo = None
        self.rating_combo = None

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # 文件类型筛选
        type_label = QLabel("类型:")
        layout.addWidget(type_label)

        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems(["全部", "图片", "视频", "音频", "文档", "设计"])
        self.file_type_combo.currentTextChanged.connect(self.filter_changed.emit)
        layout.addWidget(self.file_type_combo)

        # 日期筛选
        date_label = QLabel("日期:")
        layout.addWidget(date_label)

        self.date_combo = QComboBox()
        self.date_combo.addItems(["全部", "今天", "本周", "本月", "本年"])
        self.date_combo.currentTextChanged.connect(self.filter_changed.emit)
        layout.addWidget(self.date_combo)

        # 评分筛选
        rating_label = QLabel("评分:")
        layout.addWidget(rating_label)

        self.rating_combo = QComboBox()
        self.rating_combo.addItems(["全部", "5星", "4星以上", "3星以上", "2星以上", "1星以上"])
        self.rating_combo.currentTextChanged.connect(self.filter_changed.emit)
        layout.addWidget(self.rating_combo)

    def get_filters(self) -> dict:
        """获取当前筛选条件"""
        filters = {}

        # 文件类型筛选
        file_type = self.file_type_combo.currentText()
        if file_type != "全部":
            type_mapping = {
                "图片": "image",
                "视频": "video",
                "音频": "audio",
                "文档": "document",
                "设计": "design"
            }
            filters['file_type'] = type_mapping.get(file_type, file_type)

        # 评分筛选
        rating = self.rating_combo.currentText()
        if rating != "全部":
            rating_mapping = {
                "5星": 5,
                "4星以上": 4,
                "3星以上": 3,
                "2星以上": 2,
                "1星以上": 1
            }
            filters['rating_min'] = rating_mapping.get(rating, 0)

        # TODO: 实现日期筛选逻辑

        return filters

    def clear_filters(self):
        """清空筛选条件"""
        self.file_type_combo.setCurrentIndex(0)
        self.date_combo.setCurrentIndex(0)
        self.rating_combo.setCurrentIndex(0)
