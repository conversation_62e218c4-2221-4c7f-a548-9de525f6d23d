# 最终完整性检查脚本
# 功能：全面检查程序的完整性和功能实现情况

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_file_structure():
    """检查文件结构完整性"""
    print("检查文件结构...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "README.md",
        "LICENSE",
        "run.bat",
        "install.bat",
        "config_example.json",
        "theme/theme_manager.py",
        "ui/main_window.py",
        "ui/components/sidebar.py",
        "ui/components/content_area.py",
        "ui/components/preview_panel.py",
        "ui/components/title_bar.py",
        "ui/components/toolbar.py",
        "ui/dialogs/settings_dialog.py",
        "ui/dialogs/about_dialog.py",
        "database/db_manager.py",
        "core/file_manager.py",
        "core/search_engine.py",
        "ai/ai_analyzer.py",
        "utils/config_manager.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (project_root / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print(f"✓ 所有 {len(required_files)} 个必需文件都存在")
        return True

def check_imports():
    """检查导入完整性"""
    print("\n检查导入完整性...")
    
    modules_to_check = [
        ("主程序", "main"),
        ("主题管理器", "theme.theme_manager"),
        ("主窗口", "ui.main_window"),
        ("侧边栏", "ui.components.sidebar"),
        ("内容区域", "ui.components.content_area"),
        ("预览面板", "ui.components.preview_panel"),
        ("标题栏", "ui.components.title_bar"),
        ("工具栏", "ui.components.toolbar"),
        ("设置对话框", "ui.dialogs.settings_dialog"),
        ("关于对话框", "ui.dialogs.about_dialog"),
        ("数据库管理器", "database.db_manager"),
        ("文件管理器", "core.file_manager"),
        ("搜索引擎", "core.search_engine"),
        ("AI分析器", "ai.ai_analyzer"),
        ("配置管理器", "utils.config_manager")
    ]
    
    failed_imports = []
    
    for name, module in modules_to_check:
        try:
            __import__(module)
            print(f"✓ {name}")
        except ImportError as e:
            print(f"✗ {name}: {e}")
            failed_imports.append(name)
        except Exception as e:
            print(f"✗ {name}: {e}")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n导入失败的模块: {', '.join(failed_imports)}")
        return False
    else:
        print(f"\n✓ 所有 {len(modules_to_check)} 个模块导入成功")
        return True

def check_functionality():
    """检查功能完整性"""
    print("\n检查功能完整性...")
    
    try:
        # 检查核心功能类
        from theme.theme_manager import ThemeManager
        from utils.config_manager import ConfigManager
        from database.db_manager import DatabaseManager
        from core.file_manager import FileManager
        from core.search_engine import SearchEngine
        from ai.ai_analyzer import AIAnalyzer
        
        # 创建实例
        config_manager = ConfigManager()
        db_manager = DatabaseManager()
        theme_manager = ThemeManager()
        file_manager = FileManager(db_manager, config_manager)
        search_engine = SearchEngine(db_manager, config_manager)
        ai_analyzer = AIAnalyzer(config_manager)
        
        print("✓ 所有核心功能类创建成功")
        
        # 检查主要方法
        methods_to_check = [
            (theme_manager, "load_theme", ["light"]),
            (theme_manager, "get_current_theme", []),
            (config_manager, "get_theme", []),
            (config_manager, "get_ui_config", []),
            (db_manager, "initialize_database", []),
            (file_manager, "calculate_file_hash", [__file__]),
            (search_engine, "search", ["test"]),
            (ai_analyzer, "generate_auto_tags", [__file__, "other"])
        ]
        
        for obj, method_name, args in methods_to_check:
            try:
                method = getattr(obj, method_name)
                method(*args)
                print(f"✓ {obj.__class__.__name__}.{method_name}")
            except Exception as e:
                print(f"✗ {obj.__class__.__name__}.{method_name}: {e}")
        
        # 关闭数据库
        db_manager.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 功能检查失败: {e}")
        return False

def check_ui_components():
    """检查UI组件完整性"""
    print("\n检查UI组件...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 检查UI组件
        from theme.theme_manager import ThemeManager
        from utils.config_manager import ConfigManager
        from database.db_manager import DatabaseManager
        
        theme_manager = ThemeManager()
        config_manager = ConfigManager()
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # 检查主要UI组件
        ui_components = [
            ("主窗口", "ui.main_window", "MainWindow"),
            ("侧边栏", "ui.components.sidebar", "SidebarWidget"),
            ("内容区域", "ui.components.content_area", "ContentAreaWidget"),
            ("预览面板", "ui.components.preview_panel", "PreviewPanelWidget"),
            ("标题栏", "ui.components.title_bar", "CustomTitleBar"),
            ("工具栏", "ui.components.toolbar", "MainToolBar"),
            ("设置对话框", "ui.dialogs.settings_dialog", "SettingsDialog"),
            ("关于对话框", "ui.dialogs.about_dialog", "AboutDialog")
        ]
        
        for name, module_name, class_name in ui_components:
            try:
                module = __import__(module_name, fromlist=[class_name])
                widget_class = getattr(module, class_name)
                
                # 尝试创建实例
                if class_name == "MainWindow":
                    widget = widget_class(theme_manager, db_manager, config_manager)
                elif class_name in ["SidebarWidget", "ContentAreaWidget", "PreviewPanelWidget"]:
                    widget = widget_class(theme_manager, db_manager, config_manager)
                elif class_name == "CustomTitleBar":
                    widget = widget_class()
                elif class_name == "MainToolBar":
                    widget = widget_class()
                elif class_name == "SettingsDialog":
                    widget = widget_class(config_manager, theme_manager)
                elif class_name == "AboutDialog":
                    widget = widget_class()
                
                print(f"✓ {name}")
                
            except Exception as e:
                print(f"✗ {name}: {e}")
        
        # 关闭数据库
        db_manager.close()
        
        return True
        
    except Exception as e:
        print(f"✗ UI组件检查失败: {e}")
        return False

def check_dependencies():
    """检查依赖库"""
    print("\n检查依赖库...")
    
    dependencies = [
        ("PySide6", "PySide6"),
        ("Pillow", "PIL"),
        ("OpenCV", "cv2"),
        ("NumPy", "numpy"),
        ("scikit-learn", "sklearn"),
        ("ImageHash", "imagehash"),
        ("Pandas", "pandas"),
        ("Requests", "requests"),
        ("PyYAML", "yaml"),
        ("Loguru", "loguru"),
        ("tqdm", "tqdm"),
        ("Watchdog", "watchdog"),
        ("Colorama", "colorama"),
        ("psutil", "psutil"),
        ("Cryptography", "cryptography"),
        ("python-dateutil", "dateutil"),
        ("regex", "regex"),
        ("pillow-heif", "pillow_heif")
    ]
    
    missing_deps = []
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✓ {name}")
        except ImportError:
            print(f"✗ {name}")
            missing_deps.append(name)
    
    if missing_deps:
        print(f"\n缺少依赖: {', '.join(missing_deps)}")
        return False
    else:
        print(f"\n✓ 所有 {len(dependencies)} 个依赖库都已安装")
        return True

def main():
    """主检查函数"""
    print("=" * 70)
    print("智能素材管理器 - 最终完整性检查")
    print("=" * 70)
    
    checks = [
        ("文件结构", check_file_structure),
        ("依赖库", check_dependencies),
        ("模块导入", check_imports),
        ("核心功能", check_functionality),
        ("UI组件", check_ui_components)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"✗ {check_name}检查异常: {e}")
            results.append((check_name, False))
    
    # 输出最终结果
    print("\n" + "=" * 70)
    print("最终检查结果:")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{check_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 70)
    print(f"总计: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 程序完整性检查全部通过！")
        print("✅ 智能素材管理器已准备就绪，可以正常使用。")
        print("\n启动方式:")
        print("1. 双击 run.bat")
        print("2. 运行: python main.py")
        return 0
    else:
        print(f"\n❌ {total - passed} 项检查失败，请修复相关问题。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
