#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能素材管理器主程序 - 高性能优化版
集成启动优化、智能缓存、性能监控、智能预加载等高级功能
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QTranslator, QLocale, QTimer
from PySide6.QtGui import QIcon, QFont

# 导入核心模块
from theme.theme_manager import ThemeManager
from database.db_manager import DatabaseManager
from utils.config_manager import ConfigManager
from ui.main_window import MainWindow

# 导入高级优化模块
try:
    from core.startup_optimizer import get_startup_optimizer, cleanup_startup_optimizer
    from core.intelligent_cache import get_cache_manager, cleanup_cache_manager
    from core.performance_monitor import get_performance_monitor, cleanup_performance_monitor
    from core.smart_preloader import get_smart_preloader, cleanup_smart_preloader
    OPTIMIZATION_AVAILABLE = True
    print("✅ 高级优化模块加载成功")
except ImportError as e:
    print(f"⚠️ 高级优化模块不可用: {e}")
    print("🔄 将使用标准启动模式")
    OPTIMIZATION_AVAILABLE = False

class OptimizedSmartAssetManager:
    """优化的智能素材管理器主应用类"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.theme_manager = None
        self.db_manager = None
        self.config_manager = None
        
        # 优化组件
        self.startup_optimizer = None
        self.cache_manager = None
        self.performance_monitor = None
        self.smart_preloader = None
        
        # 启动时间记录
        self.start_time = time.time()
    
    def initialize_app(self):
        """初始化应用程序"""
        # 创建QApplication实例
        self.app = QApplication(sys.argv)
        
        # 设置应用程序信息
        self.app.setApplicationName("智能素材管理器")
        self.app.setApplicationVersion("2.0.0")
        self.app.setApplicationDisplayName("智能素材管理器 - 高性能版")
        self.app.setOrganizationName("Smart Asset Manager")
        self.app.setOrganizationDomain("smartassetmanager.com")
        
        # 启用高级功能
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        self.app.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)
        
        # 设置应用程序图标
        self.setup_application_icon()
        
        # 设置全局样式
        self.setup_global_style()
        
        print("✅ 应用程序初始化完成")
    
    def setup_application_icon(self):
        """设置应用程序图标"""
        icon_path = project_root / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        else:
            # 创建默认图标
            from PySide6.QtGui import QPixmap, QPainter, QColor
            pixmap = QPixmap(64, 64)
            pixmap.fill(QColor(0, 120, 204))
            
            painter = QPainter(pixmap)
            painter.setPen(QColor(255, 255, 255))
            painter.setFont(QFont("Arial", 20, QFont.Bold))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "SAM")
            painter.end()
            
            self.app.setWindowIcon(QIcon(pixmap))
    
    def setup_global_style(self):
        """设置全局样式"""
        style = """
        QApplication {
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
            font-size: 9pt;
        }
        
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QToolTip {
            background-color: #2b2b2b;
            color: white;
            border: 1px solid #555;
            padding: 5px;
            border-radius: 3px;
            font-size: 8pt;
        }
        """
        self.app.setStyleSheet(style)
    
    def initialize_optimization_systems(self):
        """初始化优化系统"""
        if not OPTIMIZATION_AVAILABLE:
            return
        
        try:
            # 启动优化器
            self.startup_optimizer = get_startup_optimizer()
            
            # 智能缓存管理器
            self.cache_manager = get_cache_manager()
            
            # 性能监控器
            self.performance_monitor = get_performance_monitor()
            
            # 智能预加载器
            self.smart_preloader = get_smart_preloader()
            
            print("✅ 优化系统初始化完成")
            
        except Exception as e:
            print(f"❌ 优化系统初始化失败: {e}")
    
    def run_optimized_startup(self):
        """运行优化启动流程"""
        if not OPTIMIZATION_AVAILABLE or not self.startup_optimizer:
            return
        
        try:
            # 连接启动优化器信号
            self.startup_optimizer.progress_updated.connect(self._on_startup_progress)
            self.startup_optimizer.optimization_completed.connect(self._on_startup_completed)
            self.startup_optimizer.error_occurred.connect(self._on_startup_error)
            
            # 执行启动优化
            result = self.startup_optimizer.optimize_startup(self.app)
            
            if result.get('success', False):
                print("🚀 启动优化完成")
                if 'profiler_report' in result:
                    print(result['profiler_report'])
            else:
                print(f"❌ 启动优化失败: {result.get('error', 'Unknown error')}")
        
        except Exception as e:
            print(f"启动优化执行失败: {e}")
    
    def _on_startup_progress(self, progress: int, message: str):
        """启动进度更新"""
        print(f"启动进度: {progress}% - {message}")
        
        # 更新启动画面
        if (self.startup_optimizer and 
            self.startup_optimizer.splash_screen):
            self.startup_optimizer.splash_screen.update_progress(progress, message)
    
    def _on_startup_completed(self, result: dict):
        """启动完成"""
        print("🎉 应用程序启动完成!")
        
        # 启动性能监控
        if self.performance_monitor:
            self.performance_monitor.start_monitoring()
        
        # 注册智能预加载器
        if self.smart_preloader:
            self._register_preloaders()
    
    def _on_startup_error(self, error_message: str):
        """启动错误"""
        print(f"❌ 启动错误: {error_message}")
        
        # 显示错误对话框
        QMessageBox.warning(None, "启动错误", f"应用程序启动时发生错误:\n{error_message}")
    
    def _register_preloaders(self):
        """注册预加载器"""
        try:
            # 注册缩略图预加载器
            def preload_thumbnail(file_path: str):
                from PySide6.QtGui import QPixmap
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    return pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                return None
            
            self.smart_preloader.register_preloader('thumbnail', preload_thumbnail)
            
            # 注册目录列表预加载器
            def preload_directory(dir_path: str):
                try:
                    path = Path(dir_path)
                    if path.is_dir():
                        return list(path.iterdir())
                except:
                    pass
                return []
            
            self.smart_preloader.register_preloader('directory_listing', preload_directory)
            
            print("✅ 预加载器注册完成")
            
        except Exception as e:
            print(f"预加载器注册失败: {e}")
    
    def initialize_managers(self):
        """初始化各种管理器"""
        # 初始化配置管理器
        self.config_manager = ConfigManager()
        
        # 从缓存获取预加载的配置
        if self.cache_manager:
            cached_config = self.cache_manager.get('config', 'ui_state')
            if cached_config:
                print("✅ 使用缓存的配置数据")
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager()
        self.db_manager.initialize_database()
        
        # 初始化主题管理器
        self.theme_manager = ThemeManager()
        self.theme_manager.load_theme(self.config_manager.get_theme())
        
        print("✅ 管理器初始化完成")
    
    def create_main_window(self):
        """创建主窗口"""
        self.main_window = MainWindow(
            theme_manager=self.theme_manager,
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
        
        # 集成优化组件
        self._integrate_optimization_components()
        
        # 应用主题到主窗口
        self.theme_manager.apply_theme_to_widget(self.main_window)
        
        print("✅ 主窗口创建完成")
    
    def _integrate_optimization_components(self):
        """集成优化组件到主窗口"""
        if not self.main_window:
            return
        
        try:
            # 集成性能监控
            if self.performance_monitor:
                # 可以在主窗口添加性能监控面板
                pass
            
            # 集成智能预加载
            if self.smart_preloader:
                # 连接用户行为记录
                # 这里可以连接主窗口的各种用户操作信号
                pass
            
            print("✅ 优化组件集成完成")
            
        except Exception as e:
            print(f"优化组件集成失败: {e}")
    
    def setup_signal_connections(self):
        """设置信号连接"""
        # 主题变更信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
        
        # 应用程序退出信号
        self.app.aboutToQuit.connect(self.on_app_quit)
        
        print("✅ 信号连接设置完成")
    
    def on_theme_changed(self, theme_name):
        """主题变更处理"""
        if self.main_window:
            self.theme_manager.apply_theme_to_widget(self.main_window)
        self.config_manager.set_theme(theme_name)
    
    def on_app_quit(self):
        """应用程序退出处理"""
        print("🔄 正在清理资源...")
        
        # 清理数据库连接
        if self.db_manager:
            self.db_manager.close()
        
        # 保存配置
        if self.config_manager:
            self.config_manager.save_config()
        
        # 清理优化系统
        self.cleanup_optimization_systems()
        
        print("✅ 资源清理完成")
    
    def cleanup_optimization_systems(self):
        """清理优化系统"""
        if not OPTIMIZATION_AVAILABLE:
            return
        
        try:
            cleanup_smart_preloader()
            cleanup_performance_monitor()
            cleanup_cache_manager()
            cleanup_startup_optimizer()
            
            print("✅ 优化系统清理完成")
            
        except Exception as e:
            print(f"优化系统清理失败: {e}")
    
    def run(self):
        """运行应用程序"""
        try:
            print("🚀 启动智能素材管理器 - 高性能版")
            print(f"📁 项目根目录: {project_root}")
            
            # 初始化应用程序
            self.initialize_app()
            
            # 初始化优化系统
            self.initialize_optimization_systems()
            
            # 运行优化启动流程
            self.run_optimized_startup()
            
            # 初始化管理器
            self.initialize_managers()
            
            # 创建主窗口
            self.create_main_window()
            
            # 设置信号连接
            self.setup_signal_connections()
            
            # 显示主窗口
            self.main_window.show()
            
            # 记录启动时间
            startup_time = time.time() - self.start_time
            print(f"⏱️ 应用程序启动耗时: {startup_time:.3f}秒")
            
            # 启动事件循环
            return self.app.exec()
            
        except Exception as e:
            print(f"❌ 应用程序启动失败: {e}")
            import traceback
            traceback.print_exc()
            return 1

def setup_error_handling():
    """设置错误处理"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        print(f"未处理的异常: {exc_type.__name__}: {exc_value}")
        import traceback
        traceback.print_exception(exc_type, exc_value, exc_traceback)
    
    sys.excepthook = handle_exception

def main():
    """主函数"""
    # 设置错误处理
    setup_error_handling()
    
    # 创建优化的应用程序实例
    app = OptimizedSmartAssetManager()
    
    # 运行应用程序
    exit_code = app.run()
    
    # 退出
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
