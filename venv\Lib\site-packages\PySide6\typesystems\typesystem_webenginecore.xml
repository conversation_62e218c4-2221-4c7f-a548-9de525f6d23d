<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtWebEngineCore" doc-package="PySide6.QtWebEngine"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_core.xml" generate="no"/>
  <load-typesystem name="typesystem_gui.xml" generate="no"/>
  <load-typesystem name="typesystem_network.xml" generate="no"/>
  <load-typesystem name="typesystem_printsupport.xml" generate="no"/>
  <load-typesystem name="typesystem_webchannel.xml" generate="no"/>

   <extra-includes>
       <include file-name="glue/webengineframe.h" location="global"/>
   </extra-includes>

   <rejection class="extensions"/>

  <function signature="qWebEngineChromiumVersion()"/>
  <function signature="qWebEngineChromiumSecurityPatchVersion()"/>
  <function signature="qWebEngineVersion()"/>

  <value-type name="QWebEngineClientCertificateSelection"/>
  <object-type name="QWebEngineClientCertificateStore"/>

  <object-type name="QWebEngineCookieStore">
    <inject-code class="native" position="beginning" file="../glue/qtwebenginecore.cpp"
                 snippet="qwebenginecookiestore-functor"/>
    <value-type name="FilterRequest" />
    <add-function signature="setCookieFilter(PyCallable* @filterCallback@)">
      <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                   snippet="qwebenginecookiestore-setcookiefilter"/>
    </add-function>
  </object-type>

  <value-type name="QWebEngineCertificateError">
    <enum-type name="Type"/>
  </value-type>

  <object-type name="QWebEngineClientHints" since="6.8"/>

  <object-type name="QWebEngineContextMenuRequest">
    <enum-type name="EditFlag" flags="EditFlags"/>
    <enum-type name="MediaFlag" flags="MediaFlags"/>
    <enum-type name="MediaType"/>
  </object-type>

  <object-type name="QWebEngineDownloadRequest">
    <enum-type name="DownloadInterruptReason"/>
    <enum-type name="DownloadState"/>
    <enum-type name="SavePageFormat"/>
  </object-type>

  <value-type name="QWebEngineFileSystemAccessRequest">
    <enum-type name="AccessFlag" flags="AccessFlags"/>
    <enum-type name="HandleType"/>
    <!-- No default constructor -->
    <modify-function signature="swap(QWebEngineFileSystemAccessRequest&amp;)" remove="all"/>
  </value-type>

  <value-type name="QWebEngineFrame" default-constructor="defaultConstructedWebEngineFrame()"
              since="6.8">
     <extra-includes>
         <include file-name="glue/webenginepage_functors.h" location="global"/>
         <include file-name="glue/webengineframe.h" location="global"/>
     </extra-includes>
    <modify-function signature="^runJavaScript\(.*\)$" remove="yes"/>
    <add-function signature="runJavaScript(const QString &amp;@scriptSource@,PyCallable*@resultCallback@)">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebenginepage-runjavascript-2"/>
    </add-function>
    <add-function signature="runJavaScript(const QString &amp;@scriptSource@,quint32@worldId@,PyCallable*@resultCallback@={})">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebenginepage-runjavascript-3"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-runjavascript"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-async-note"/>
    </add-function>
    <add-function signature="printToPdf(PyCallable*@resultCallback@)">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebengineframe-printtopdf"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebengineframe-printtopdf"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-async-note"/>
    </add-function>
  </value-type>

  <value-type name="QWebEngineFullScreenRequest"/>

  <object-type name="QWebEngineHistory"/>
  <value-type name="QWebEngineHistoryItem">
      <!-- No default constructor -->
      <modify-function signature="swap(QWebEngineHistoryItem&amp;)" remove="all"/>
  </value-type>
  <object-type name="QWebEngineHistoryModel">
    <enum-type name="Roles"/>
  </object-type>

  <object-type name="QWebEngineNavigationRequest">
      <enum-type name="NavigationType"/>
      <enum-type name="NavigationRequestAction"/>
  </object-type>

  <object-type name="QWebEngineNotification"/>

  <object-type name="QWebEnginePage">
     <extra-includes>
         <include file-name="glue/webenginepage_functors.h" location="global"/>
     </extra-includes>
    <enum-type name="LifecycleState"/>
    <enum-type name="WebAction"/>
    <enum-type name="FindFlag" flags="FindFlags"/>
    <enum-type name="WebWindowType"/>
    <enum-type name="PermissionPolicy"/>
    <enum-type name="NavigationType"/>
    <enum-type name="Feature"/>
    <enum-type name="FileSelectionMode"/>
    <enum-type name="JavaScriptConsoleMessageLevel"/>
    <enum-type name="RenderProcessTerminationStatus"/>
    <add-function signature="javaScriptPromptPyOverride(QUrl@securityOrigin@,QString@msg@,QString@defaultValue@)"
                  return-type="std::pair&lt;bool,QString&gt;" python-override="true"/>
    <modify-function signature="javaScriptPrompt(QUrl,QString,QString,QString*)">
      <inject-code class="shell" position="override" file="../glue/qtwebenginecore.cpp"
                   snippet="qwebenginepage-javascriptprompt-virtual-redirect"/>
      <modify-argument index="return" pyi-type="Tuple[bool, str]"/>
      <modify-argument index="4"><remove-default-expression/><remove-argument/></modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                   snippet="qwebenginepage-javascriptprompt-return"/>
    </modify-function>
    <add-function signature="findFrameByName(QString@name@)" return-type="QWebEngineFrame">
       <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                    snippet="qwebenginepage-findframebyname"/>
       <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                             snippet="qwebenginepage-findframebyname"/>
    </add-function>
    <add-function signature="findText(const QString &amp;@subString@,QWebEnginePage::FindFlags@options@,PyCallable*@resultCallback@)">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebenginepage-findtext"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-findtext"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-async-note"/>
    </add-function>
    <add-function signature="toPlainText(PyCallable*@resultCallback@) const">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebenginepage-convertto"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-toplaintext"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-async-note"/>
    </add-function>
    <add-function signature="toHtml(PyCallable*@resultCallback@) const">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebenginepage-convertto"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-tohtml"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-async-note"/>
    </add-function>
    <modify-function signature="^runJavaScript\(.*\)$" remove="yes"/>
    <add-function signature="runJavaScript(const QString &amp;@scriptSource@,PyCallable*@resultCallback@)">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebenginepage-runjavascript-2"/>
    </add-function>
    <add-function signature="runJavaScript(const QString &amp;@scriptSource@,quint32@worldId@=0,PyCallable*@resultCallback@={})">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebenginepage-runjavascript-3"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-runjavascript"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-async-note"/>
    </add-function>
    <add-function signature="printToPdf(PyCallable*@resultCallback@,const QPageLayout &amp;@pageLayout@=QPageLayout(QPageSize(QPageSize::A4),QPageLayout::Portrait,QMarginsF()),const QPageRanges &amp;@ranges@={})">
        <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                     snippet="qwebenginepage-printtopdf"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-printtopdf"/>
        <inject-documentation format="target" mode="append" file="../doc/qtwebenginecore.rst"
                              snippet="qwebenginepage-async-note"/>
    </add-function>
  </object-type>

  <value-type name="QWebEnginePermission" since="6.8">
    <enum-type name="PermissionType"/>
    <enum-type name="State"/>
  </value-type>

  <object-type name="QWebEngineProfile">
    <extra-includes>
        <include file-name="QtWebEngineCore/QWebEngineNotification" location="global"/>
    </extra-includes>
    <inject-code class="native" position="beginning" file="../glue/qtwebenginecore.cpp"
                 snippet="qwebengineprofile-functor"/>
    <enum-type name="HttpCacheType"/>
    <enum-type name="PersistentCookiesPolicy"/>
    <enum-type name="PersistentPermissionsPolicy" since="6.8"/>
    <add-function signature="setNotificationPresenter(PyCallable* @notificationPresenter@)">
      <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp" snippet="qwebengineprofile-setnotificationpresenter"/>
    </add-function>
  </object-type>

  <object-type name="QWebEngineProfileBuilder" since="6.9"/>

  <object-type name="QWebEngineNewWindowRequest">
      <enum-type name="DestinationType"/>
  </object-type>

  <value-type name="QWebEngineScript">
    <enum-type name="InjectionPoint"/>
    <enum-type name="ScriptWorldId" python-type="IntEnum"/>
  </value-type>

  <object-type name="QWebEngineScriptCollection"/>

  <object-type name="QWebEngineSettings">
    <enum-type name="FontFamily"/>
    <enum-type name="FontSize"/>
    <enum-type name="ImageAnimationPolicy" since="6.8"/>
    <enum-type name="UnknownUrlSchemePolicy"/>
    <enum-type name="WebAttribute"/>

  </object-type>

  <object-type name="QWebEngineHttpRequest">
    <enum-type name="Method"/>
  </object-type>

  <value-type name="QWebEngineLoadingInfo">
    <enum-type name="ErrorDomain"/>
    <enum-type name="LoadStatus"/>
  </value-type>

  <value-type name="QWebEngineRegisterProtocolHandlerRequest"/>

  <value-type name="QWebEngineFindTextResult"/>

  <object-type name="QWebEngineQuotaRequest"/>

  <object-type name="QWebEngineUrlRequestInfo">
    <enum-type name="NavigationType"/>
    <enum-type name="ResourceType"/>
  </object-type>

  <object-type name="QWebEngineUrlRequestInterceptor"/>

  <object-type name="QWebEngineUrlRequestJob">
    <enum-type name="Error"/>
  </object-type>
  <value-type name="QWebEngineUrlScheme">
      <enum-type name="Syntax"/>
      <enum-type name="SpecialPort"/>
      <enum-type name="Flag" flags="Flags"/>
  </value-type>

  <object-type name="QWebEngineUrlSchemeHandler"/>

  <!-- No default constructor. Used in signal QWebEnginePage.desktopMediaRequested(),
       but not in any other function. -->
  <value-type name="QWebEngineDesktopMediaRequest" since="6.7">
      <modify-function signature="swap(QWebEngineDesktopMediaRequest&amp;)" remove="yes"/>
  </value-type>
  <value-type name="QWebEngineWebAuthPinRequest" since="6.7"/>
  <object-type name="QWebEngineWebAuthUxRequest" since="6.7">
      <enum-type name="WebAuthUxState"/>
      <enum-type name="PinEntryReason"/>
      <enum-type name="PinEntryError"/>
      <enum-type name="RequestFailureReason"/>
  </object-type>
  <namespace-type name="QWebEngineGlobalSettings">
      <enum-type name="SecureDnsMode"/>
      <value-type name="DnsMode"/>
  </namespace-type>

  <!-- QtQml is pulled in via QtWebEngineCoreDepends. -->
  <suppress-warning text="^Scoped enum 'QQml.*' does not have a type entry.*$"/>

</typesystem>
