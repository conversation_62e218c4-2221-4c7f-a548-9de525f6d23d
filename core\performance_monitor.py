#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级性能监控系统
实时监控应用程序性能、资源使用、用户行为等
"""

import time
import threading
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable
from collections import deque, defaultdict
from dataclasses import dataclass, field
from pathlib import Path
import json

from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar, QTextEdit, QPushButton

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    value: float
    unit: str
    timestamp: float = field(default_factory=time.time)
    category: str = "general"
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    
    @property
    def status(self) -> str:
        """获取状态"""
        if self.threshold_critical and self.value >= self.threshold_critical:
            return "critical"
        elif self.threshold_warning and self.value >= self.threshold_warning:
            return "warning"
        else:
            return "normal"

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.metrics_history = defaultdict(lambda: deque(maxlen=1000))
        self.collectors = {}
        self.lock = threading.RLock()
        
        # 注册默认收集器
        self.register_collector("system", self._collect_system_metrics)
        self.register_collector("memory", self._collect_memory_metrics)
        self.register_collector("performance", self._collect_performance_metrics)
    
    def register_collector(self, name: str, collector_func: Callable[[], List[PerformanceMetric]]):
        """注册指标收集器"""
        self.collectors[name] = collector_func
    
    def collect_all_metrics(self) -> Dict[str, List[PerformanceMetric]]:
        """收集所有指标"""
        all_metrics = {}
        
        for name, collector in self.collectors.items():
            try:
                metrics = collector()
                all_metrics[name] = metrics
                
                # 保存到历史记录
                with self.lock:
                    for metric in metrics:
                        self.metrics_history[f"{name}.{metric.name}"].append(metric)
                        
            except Exception as e:
                print(f"收集指标失败 {name}: {e}")
                all_metrics[name] = []
        
        return all_metrics
    
    def _collect_system_metrics(self) -> List[PerformanceMetric]:
        """收集系统指标"""
        metrics = []
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            metrics.append(PerformanceMetric(
                name="cpu_usage",
                value=cpu_percent,
                unit="%",
                category="system",
                threshold_warning=70.0,
                threshold_critical=90.0
            ))
            
            # 内存使用
            memory = psutil.virtual_memory()
            metrics.append(PerformanceMetric(
                name="memory_usage",
                value=memory.percent,
                unit="%",
                category="system",
                threshold_warning=80.0,
                threshold_critical=95.0
            ))
            
            # 磁盘使用
            disk = psutil.disk_usage('/')
            metrics.append(PerformanceMetric(
                name="disk_usage",
                value=disk.percent,
                unit="%",
                category="system",
                threshold_warning=85.0,
                threshold_critical=95.0
            ))
            
            # 网络IO
            net_io = psutil.net_io_counters()
            metrics.append(PerformanceMetric(
                name="network_bytes_sent",
                value=net_io.bytes_sent,
                unit="bytes",
                category="system"
            ))
            
            metrics.append(PerformanceMetric(
                name="network_bytes_recv",
                value=net_io.bytes_recv,
                unit="bytes",
                category="system"
            ))
            
        except Exception as e:
            print(f"收集系统指标失败: {e}")
        
        return metrics
    
    def _collect_memory_metrics(self) -> List[PerformanceMetric]:
        """收集内存指标"""
        metrics = []
        
        try:
            # Python进程内存
            process = psutil.Process()
            memory_info = process.memory_info()
            
            metrics.append(PerformanceMetric(
                name="process_memory_rss",
                value=memory_info.rss / 1024 / 1024,  # MB
                unit="MB",
                category="memory",
                threshold_warning=500.0,
                threshold_critical=1000.0
            ))
            
            metrics.append(PerformanceMetric(
                name="process_memory_vms",
                value=memory_info.vms / 1024 / 1024,  # MB
                unit="MB",
                category="memory"
            ))
            
            # 垃圾回收统计
            gc_stats = gc.get_stats()
            if gc_stats:
                for i, stat in enumerate(gc_stats):
                    metrics.append(PerformanceMetric(
                        name=f"gc_generation_{i}_collections",
                        value=stat['collections'],
                        unit="count",
                        category="memory"
                    ))
            
            # 对象计数
            metrics.append(PerformanceMetric(
                name="gc_object_count",
                value=len(gc.get_objects()),
                unit="count",
                category="memory",
                threshold_warning=100000,
                threshold_critical=200000
            ))
            
        except Exception as e:
            print(f"收集内存指标失败: {e}")
        
        return metrics
    
    def _collect_performance_metrics(self) -> List[PerformanceMetric]:
        """收集性能指标"""
        metrics = []
        
        try:
            # 线程数
            metrics.append(PerformanceMetric(
                name="thread_count",
                value=threading.active_count(),
                unit="count",
                category="performance",
                threshold_warning=50,
                threshold_critical=100
            ))
            
            # 文件描述符数量（Unix系统）
            try:
                process = psutil.Process()
                metrics.append(PerformanceMetric(
                    name="open_files",
                    value=process.num_fds() if hasattr(process, 'num_fds') else 0,
                    unit="count",
                    category="performance",
                    threshold_warning=500,
                    threshold_critical=1000
                ))
            except:
                pass
            
        except Exception as e:
            print(f"收集性能指标失败: {e}")
        
        return metrics
    
    def get_metric_history(self, metric_name: str, limit: int = 100) -> List[PerformanceMetric]:
        """获取指标历史"""
        with self.lock:
            history = self.metrics_history.get(metric_name, deque())
            return list(history)[-limit:]
    
    def get_latest_metric(self, metric_name: str) -> Optional[PerformanceMetric]:
        """获取最新指标值"""
        with self.lock:
            history = self.metrics_history.get(metric_name, deque())
            return history[-1] if history else None

class PerformanceAlertManager:
    """性能告警管理器"""
    
    def __init__(self):
        self.alert_handlers = []
        self.alert_history = deque(maxlen=1000)
        self.suppressed_alerts = set()
        
    def add_alert_handler(self, handler: Callable[[PerformanceMetric], None]):
        """添加告警处理器"""
        self.alert_handlers.append(handler)
    
    def check_metrics(self, metrics: Dict[str, List[PerformanceMetric]]):
        """检查指标并触发告警"""
        for category, metric_list in metrics.items():
            for metric in metric_list:
                if metric.status in ["warning", "critical"]:
                    alert_key = f"{category}.{metric.name}.{metric.status}"
                    
                    # 避免重复告警
                    if alert_key not in self.suppressed_alerts:
                        self._trigger_alert(metric)
                        self.suppressed_alerts.add(alert_key)
                        
                        # 5分钟后允许再次告警
                        threading.Timer(300, lambda: self.suppressed_alerts.discard(alert_key)).start()
    
    def _trigger_alert(self, metric: PerformanceMetric):
        """触发告警"""
        alert_info = {
            'timestamp': time.time(),
            'metric': metric,
            'message': f"{metric.name} {metric.status}: {metric.value}{metric.unit}"
        }
        
        self.alert_history.append(alert_info)
        
        # 调用所有告警处理器
        for handler in self.alert_handlers:
            try:
                handler(metric)
            except Exception as e:
                print(f"告警处理器执行失败: {e}")

class PerformanceMonitor(QObject):
    """性能监控器主类"""
    
    # 信号定义
    metrics_updated = Signal(dict)  # 指标更新
    alert_triggered = Signal(object)  # 告警触发
    
    def __init__(self):
        super().__init__()
        
        self.collector = MetricsCollector()
        self.alert_manager = PerformanceAlertManager()
        
        # 监控配置
        self.monitoring_enabled = True
        self.collection_interval = 5.0  # 5秒
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._collect_and_process_metrics)
        
        # 设置告警处理器
        self.alert_manager.add_alert_handler(self._on_alert_triggered)
        
        # 性能报告
        self.performance_reports = deque(maxlen=100)
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring_enabled:
            self.monitor_timer.start(int(self.collection_interval * 1000))
            print("性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitor_timer.stop()
        print("性能监控已停止")
    
    def _collect_and_process_metrics(self):
        """收集和处理指标"""
        try:
            # 收集指标
            metrics = self.collector.collect_all_metrics()
            
            # 检查告警
            self.alert_manager.check_metrics(metrics)
            
            # 生成性能报告
            report = self._generate_performance_report(metrics)
            self.performance_reports.append(report)
            
            # 发送信号
            self.metrics_updated.emit(metrics)
            
        except Exception as e:
            print(f"性能监控处理失败: {e}")
    
    def _generate_performance_report(self, metrics: Dict[str, List[PerformanceMetric]]) -> Dict[str, Any]:
        """生成性能报告"""
        report = {
            'timestamp': time.time(),
            'summary': {},
            'alerts': [],
            'recommendations': []
        }
        
        # 汇总关键指标
        for category, metric_list in metrics.items():
            category_summary = {}
            alerts = []
            
            for metric in metric_list:
                category_summary[metric.name] = {
                    'value': metric.value,
                    'unit': metric.unit,
                    'status': metric.status
                }
                
                if metric.status in ["warning", "critical"]:
                    alerts.append(metric)
            
            report['summary'][category] = category_summary
            report['alerts'].extend(alerts)
        
        # 生成建议
        report['recommendations'] = self._generate_recommendations(metrics)
        
        return report
    
    def _generate_recommendations(self, metrics: Dict[str, List[PerformanceMetric]]) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        # 检查CPU使用率
        cpu_metric = None
        for metric in metrics.get('system', []):
            if metric.name == 'cpu_usage':
                cpu_metric = metric
                break
        
        if cpu_metric and cpu_metric.value > 80:
            recommendations.append("CPU使用率过高，建议关闭不必要的程序或优化算法")
        
        # 检查内存使用
        memory_metric = None
        for metric in metrics.get('system', []):
            if metric.name == 'memory_usage':
                memory_metric = metric
                break
        
        if memory_metric and memory_metric.value > 85:
            recommendations.append("内存使用率过高，建议清理缓存或增加内存")
        
        # 检查垃圾回收
        gc_objects = None
        for metric in metrics.get('memory', []):
            if metric.name == 'gc_object_count':
                gc_objects = metric
                break
        
        if gc_objects and gc_objects.value > 150000:
            recommendations.append("对象数量过多，建议执行垃圾回收或优化对象管理")
        
        return recommendations
    
    def _on_alert_triggered(self, metric: PerformanceMetric):
        """告警触发处理"""
        self.alert_triggered.emit(metric)
    
    def get_latest_report(self) -> Optional[Dict[str, Any]]:
        """获取最新性能报告"""
        return self.performance_reports[-1] if self.performance_reports else None
    
    def get_metric_trend(self, metric_name: str, duration: int = 300) -> List[float]:
        """获取指标趋势（最近5分钟）"""
        history = self.collector.get_metric_history(metric_name, limit=duration//int(self.collection_interval))
        return [m.value for m in history]
    
    def export_performance_data(self, file_path: Path):
        """导出性能数据"""
        try:
            data = {
                'reports': list(self.performance_reports),
                'alert_history': list(self.alert_manager.alert_history),
                'export_time': time.time()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)
                
            print(f"性能数据已导出到: {file_path}")
            
        except Exception as e:
            print(f"导出性能数据失败: {e}")

class PerformanceMonitorWidget(QWidget):
    """性能监控界面控件"""
    
    def __init__(self, monitor: PerformanceMonitor, parent=None):
        super().__init__(parent)
        
        self.monitor = monitor
        self.setup_ui()
        
        # 连接信号
        self.monitor.metrics_updated.connect(self.update_display)
        self.monitor.alert_triggered.connect(self.show_alert)
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("🔍 实时性能监控")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 关键指标显示
        metrics_layout = QHBoxLayout()
        
        # CPU使用率
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setRange(0, 100)
        self.cpu_label = QLabel("CPU: 0%")
        cpu_layout = QVBoxLayout()
        cpu_layout.addWidget(self.cpu_label)
        cpu_layout.addWidget(self.cpu_progress)
        metrics_layout.addLayout(cpu_layout)
        
        # 内存使用率
        self.memory_progress = QProgressBar()
        self.memory_progress.setRange(0, 100)
        self.memory_label = QLabel("内存: 0%")
        memory_layout = QVBoxLayout()
        memory_layout.addWidget(self.memory_label)
        memory_layout.addWidget(self.memory_progress)
        metrics_layout.addLayout(memory_layout)
        
        layout.addLayout(metrics_layout)
        
        # 详细信息
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(200)
        self.details_text.setReadOnly(True)
        layout.addWidget(self.details_text)
        
        # 控制按钮
        buttons_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("▶️ 开始监控")
        self.start_btn.clicked.connect(self.monitor.start_monitoring)
        buttons_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止监控")
        self.stop_btn.clicked.connect(self.monitor.stop_monitoring)
        buttons_layout.addWidget(self.stop_btn)
        
        export_btn = QPushButton("📊 导出数据")
        export_btn.clicked.connect(self.export_data)
        buttons_layout.addWidget(export_btn)
        
        layout.addLayout(buttons_layout)
    
    def update_display(self, metrics: Dict[str, List[PerformanceMetric]]):
        """更新显示"""
        # 更新CPU使用率
        for metric in metrics.get('system', []):
            if metric.name == 'cpu_usage':
                self.cpu_progress.setValue(int(metric.value))
                self.cpu_label.setText(f"CPU: {metric.value:.1f}%")
                
                # 设置颜色
                if metric.status == "critical":
                    self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: red; }")
                elif metric.status == "warning":
                    self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: orange; }")
                else:
                    self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: green; }")
                break
        
        # 更新内存使用率
        for metric in metrics.get('system', []):
            if metric.name == 'memory_usage':
                self.memory_progress.setValue(int(metric.value))
                self.memory_label.setText(f"内存: {metric.value:.1f}%")
                
                # 设置颜色
                if metric.status == "critical":
                    self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: red; }")
                elif metric.status == "warning":
                    self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: orange; }")
                else:
                    self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: green; }")
                break
        
        # 更新详细信息
        self._update_details(metrics)
    
    def _update_details(self, metrics: Dict[str, List[PerformanceMetric]]):
        """更新详细信息"""
        details = "📊 详细性能指标:\n\n"
        
        for category, metric_list in metrics.items():
            details += f"【{category.upper()}】\n"
            for metric in metric_list:
                status_icon = {"normal": "✅", "warning": "⚠️", "critical": "❌"}.get(metric.status, "ℹ️")
                details += f"  {status_icon} {metric.name}: {metric.value:.2f} {metric.unit}\n"
            details += "\n"
        
        # 添加建议
        report = self.monitor.get_latest_report()
        if report and report.get('recommendations'):
            details += "💡 优化建议:\n"
            for rec in report['recommendations']:
                details += f"  • {rec}\n"
        
        self.details_text.setPlainText(details)
    
    def show_alert(self, metric: PerformanceMetric):
        """显示告警"""
        alert_text = f"⚠️ 性能告警: {metric.name} = {metric.value}{metric.unit} ({metric.status})"
        self.details_text.append(f"\n{alert_text}")
    
    def export_data(self):
        """导出数据"""
        from PySide6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出性能数据", "performance_data.json", "JSON Files (*.json)"
        )
        
        if file_path:
            self.monitor.export_performance_data(Path(file_path))

# 全局性能监控器实例
_performance_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

def cleanup_performance_monitor():
    """清理全局性能监控器"""
    global _performance_monitor
    if _performance_monitor:
        _performance_monitor.stop_monitoring()
        _performance_monitor = None
