#include "performance_engine.hpp"
#include <algorithm>
#include <sstream>
#include <cctype>
#include <chrono>
#include <iostream>

// SearchIndexEngine 实现
void SearchIndexEngine::setDataSource(const std::vector<FileItem>& data) {
    std::lock_guard<std::mutex> lock(index_mutex_);
    data_source_ = data;
    buildIndex();
}

void SearchIndexEngine::buildIndex() {
    word_index_.clear();
    type_index_.clear();

    for (size_t i = 0; i < data_source_.size(); ++i) {
        const auto& item = data_source_[i];

        // 建立文件名索引
        auto words = tokenize(toLowerCase(item.name));
        for (const auto& word : words) {
            word_index_[word].insert(static_cast<int>(i));
        }

        // 建立文件类型索引
        std::string type = toLowerCase(item.file_type);
        type_index_[type].insert(static_cast<int>(i));
    }
}

std::vector<int> SearchIndexEngine::search(const std::string& query, const std::string& file_type_filter) const {
    std::lock_guard<std::mutex> lock(index_mutex_);

    std::unordered_set<int> result_set;
    bool first_word = true;

    // 如果有查询词，进行文本搜索
    if (!query.empty()) {
        auto query_words = tokenize(toLowerCase(query));

        for (const auto& word : query_words) {
            std::unordered_set<int> word_results;

            // 查找包含该词的所有项目
            for (const auto& [indexed_word, indices] : word_index_) {
                if (indexed_word.find(word) != std::string::npos) {
                    word_results.insert(indices.begin(), indices.end());
                }
            }

            if (first_word) {
                result_set = word_results;
                first_word = false;
            } else {
                // 取交集
                std::unordered_set<int> intersection;
                for (int idx : result_set) {
                    if (word_results.count(idx)) {
                        intersection.insert(idx);
                    }
                }
                result_set = intersection;
            }
        }
    } else {
        // 如果没有查询词，返回所有项目
        for (size_t i = 0; i < data_source_.size(); ++i) {
            result_set.insert(static_cast<int>(i));
        }
    }

    // 应用文件类型过滤
    if (!file_type_filter.empty()) {
        std::string filter = toLowerCase(file_type_filter);
        auto type_it = type_index_.find(filter);

        if (type_it != type_index_.end()) {
            std::unordered_set<int> filtered_result;
            for (int idx : result_set) {
                if (type_it->second.count(idx)) {
                    filtered_result.insert(idx);
                }
            }
            result_set = filtered_result;
        } else {
            result_set.clear(); // 没有匹配的类型
        }
    }

    // 转换为vector并排序
    std::vector<int> result(result_set.begin(), result_set.end());
    std::sort(result.begin(), result.end());

    return result;
}

const FileItem& SearchIndexEngine::getItem(int index) const {
    std::lock_guard<std::mutex> lock(index_mutex_);
    return data_source_[index];
}

std::vector<std::string> SearchIndexEngine::tokenize(const std::string& text) const {
    std::vector<std::string> tokens;
    std::istringstream iss(text);
    std::string token;

    while (iss >> token) {
        if (!token.empty()) {
            tokens.push_back(token);
        }
    }

    return tokens;
}

std::string SearchIndexEngine::toLowerCase(const std::string& str) const {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

// SortEngine 实现
std::vector<int> SortEngine::sort(const std::vector<FileItem>& data,
                                 const std::vector<int>& indices,
                                 SortKey key, bool reverse) const {
    std::string cache_key = generateCacheKey(indices, key, reverse);

    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        auto it = sort_cache_.find(cache_key);
        if (it != sort_cache_.end()) {
            return it->second; // 返回缓存结果
        }
    }

    // 执行排序
    std::vector<int> result = indices;

    switch (key) {
        case NAME:
            std::sort(result.begin(), result.end(), [&data](int a, int b) {
                return data[a].name < data[b].name;
            });
            break;
        case SIZE:
            std::sort(result.begin(), result.end(), [&data](int a, int b) {
                return data[a].size < data[b].size;
            });
            break;
        case DATE:
            std::sort(result.begin(), result.end(), [&data](int a, int b) {
                return data[a].created_time < data[b].created_time;
            });
            break;
        case TYPE:
            std::sort(result.begin(), result.end(), [&data](int a, int b) {
                return data[a].file_type < data[b].file_type;
            });
            break;
        case RATING:
            std::sort(result.begin(), result.end(), [&data](int a, int b) {
                return data[a].rating < data[b].rating;
            });
            break;
    }

    if (reverse) {
        std::reverse(result.begin(), result.end());
    }

    // 缓存结果
    {
        std::lock_guard<std::mutex> lock(cache_mutex_);
        if (sort_cache_.size() < 20) { // 限制缓存大小
            sort_cache_[cache_key] = result;
        }
    }

    return result;
}

void SortEngine::clearCache() {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    sort_cache_.clear();
}

std::string SortEngine::generateCacheKey(const std::vector<int>& indices, SortKey key, bool reverse) const {
    std::ostringstream oss;
    oss << static_cast<int>(key) << "_" << reverse << "_" << indices.size();

    // 为了简化，只使用大小作为缓存键的一部分
    // 在实际应用中，可能需要更复杂的键生成策略
    if (!indices.empty()) {
        oss << "_" << indices.front() << "_" << indices.back();
    }

    return oss.str();
}

// VirtualScrollManager 实现
VirtualScrollManager::VirtualScrollManager(int cache_size, int preload_distance)
    : cache_size_(cache_size), preload_distance_(preload_distance), current_position_(0) {
}

void VirtualScrollManager::updateVisibleRange(int start_index, int visible_count, int total_count) {
    std::lock_guard<std::mutex> lock(cache_mutex_);

    current_position_ = start_index;
    visible_indices_.clear();

    // 计算可见范围（包括预加载）
    int preload_start = std::max(0, start_index - preload_distance_);
    int preload_end = std::min(total_count, start_index + visible_count + preload_distance_);

    for (int i = preload_start; i < preload_end; ++i) {
        visible_indices_.push_back(i);
    }
}

std::vector<int> VirtualScrollManager::getItemsToLoad() const {
    std::lock_guard<std::mutex> lock(cache_mutex_);

    std::vector<int> to_load;
    for (int index : visible_indices_) {
        if (cached_indices_.find(index) == cached_indices_.end()) {
            to_load.push_back(index);
        }
    }

    return to_load;
}

std::vector<int> VirtualScrollManager::getItemsToUnload() const {
    std::lock_guard<std::mutex> lock(cache_mutex_);

    std::vector<int> to_unload;
    std::unordered_set<int> visible_set(visible_indices_.begin(), visible_indices_.end());

    for (int cached_index : cached_indices_) {
        if (visible_set.find(cached_index) == visible_set.end()) {
            to_unload.push_back(cached_index);
        }
    }

    return to_unload;
}

void VirtualScrollManager::markItemLoaded(int index) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    cached_indices_.insert(index);

    // 如果缓存超过限制，移除最远的项目
    if (static_cast<int>(cached_indices_.size()) > cache_size_) {
        // 简单策略：移除距离当前位置最远的项目
        int farthest_index = -1;
        int max_distance = -1;

        for (int cached_index : cached_indices_) {
            int distance = std::abs(cached_index - current_position_);
            if (distance > max_distance) {
                max_distance = distance;
                farthest_index = cached_index;
            }
        }

        if (farthest_index != -1) {
            cached_indices_.erase(farthest_index);
        }
    }
}

void VirtualScrollManager::markItemUnloaded(int index) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    cached_indices_.erase(index);
}

bool VirtualScrollManager::isItemCached(int index) const {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    return cached_indices_.find(index) != cached_indices_.end();
}

void VirtualScrollManager::clearCache() {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    cached_indices_.clear();
    visible_indices_.clear();
}

// ThumbnailLoadManager 实现
ThumbnailLoadManager::ThumbnailLoadManager(int thread_count)
    : running_(false), thread_count_(thread_count) {
}

ThumbnailLoadManager::~ThumbnailLoadManager() {
    stop();
}

void ThumbnailLoadManager::requestThumbnail(int id, const std::string& file_path, int priority) {
    std::lock_guard<std::mutex> lock(queue_mutex_);

    // 检查是否已在处理或已完成
    if (processing_ids_.count(id) || completed_ids_.count(id)) {
        return;
    }

    request_queue_.emplace(id, file_path, priority);
    queue_condition_.notify_one();
}

void ThumbnailLoadManager::start() {
    running_ = true;

    for (int i = 0; i < thread_count_; ++i) {
        worker_threads_.emplace_back(&ThumbnailLoadManager::workerThread, this);
    }
}

void ThumbnailLoadManager::stop() {
    running_ = false;
    queue_condition_.notify_all();

    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }

    worker_threads_.clear();
}

bool ThumbnailLoadManager::isCompleted(int id) const {
    std::lock_guard<std::mutex> lock(processing_mutex_);
    return completed_ids_.count(id) > 0;
}

void ThumbnailLoadManager::clearQueue() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    std::priority_queue<ThumbnailRequest> empty_queue;
    request_queue_.swap(empty_queue);

    std::lock_guard<std::mutex> proc_lock(processing_mutex_);
    processing_ids_.clear();
    completed_ids_.clear();
}

void ThumbnailLoadManager::workerThread() {
    while (running_) {
        ThumbnailRequest request(0, "", 0);

        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_condition_.wait(lock, [this] { return !request_queue_.empty() || !running_; });

            if (!running_) break;

            if (!request_queue_.empty()) {
                request = request_queue_.top();
                request_queue_.pop();

                std::lock_guard<std::mutex> proc_lock(processing_mutex_);
                processing_ids_.insert(request.id);
            } else {
                continue;
            }
        }

        // 处理缩略图请求
        bool success = processThumbnailRequest(request);

        {
            std::lock_guard<std::mutex> lock(processing_mutex_);
            processing_ids_.erase(request.id);
            if (success) {
                completed_ids_.insert(request.id);
            }
        }
    }
}

bool ThumbnailLoadManager::processThumbnailRequest(const ThumbnailRequest& request) {
    // 模拟缩略图处理
    // 在实际实现中，这里会调用图像处理库
    std::this_thread::sleep_for(std::chrono::milliseconds(10)); // 模拟处理时间
    return true;
}

// PerformanceEngine 实现
PerformanceEngine::PerformanceEngine()
    : search_engine_(std::make_unique<SearchIndexEngine>()),
      sort_engine_(std::make_unique<SortEngine>()),
      scroll_manager_(std::make_unique<VirtualScrollManager>()),
      thumbnail_manager_(std::make_unique<ThumbnailLoadManager>()) {

    thumbnail_manager_->start();
}

PerformanceEngine::~PerformanceEngine() {
    cleanup();
}

void PerformanceEngine::initializeData(const std::vector<FileItem>& data) {
    std::lock_guard<std::mutex> lock(engine_mutex_);
    search_engine_->setDataSource(data);
}

std::vector<int> PerformanceEngine::searchAndFilter(const std::string& query,
                                                   const std::string& file_type_filter) const {
    return search_engine_->search(query, file_type_filter);
}

std::vector<int> PerformanceEngine::sortResults(const std::vector<int>& indices,
                                               SortEngine::SortKey key, bool reverse) const {
    std::vector<FileItem> data;
    for (size_t i = 0; i < search_engine_->size(); ++i) {
        data.push_back(search_engine_->getItem(static_cast<int>(i)));
    }

    return sort_engine_->sort(data, indices, key, reverse);
}

void PerformanceEngine::updateVisibleRange(int start_index, int visible_count, int total_count) {
    scroll_manager_->updateVisibleRange(start_index, visible_count, total_count);
}

std::vector<int> PerformanceEngine::getItemsToLoad() const {
    return scroll_manager_->getItemsToLoad();
}

std::vector<int> PerformanceEngine::getItemsToUnload() const {
    return scroll_manager_->getItemsToUnload();
}

void PerformanceEngine::markItemLoaded(int index) {
    scroll_manager_->markItemLoaded(index);
}

void PerformanceEngine::requestThumbnail(int id, const std::string& file_path, int priority) {
    thumbnail_manager_->requestThumbnail(id, file_path, priority);
}

bool PerformanceEngine::isThumbnailCompleted(int id) const {
    return thumbnail_manager_->isCompleted(id);
}

const FileItem& PerformanceEngine::getItem(int index) const {
    return search_engine_->getItem(index);
}

PerformanceEngine::PerformanceStats PerformanceEngine::getPerformanceStats() const {
    PerformanceStats stats;
    stats.total_items = search_engine_->size();
    stats.cached_items = 0; // TODO: 实现缓存统计
    stats.search_index_size = 0; // TODO: 实现索引大小统计
    stats.cache_hit_ratio = 0.95; // TODO: 实现缓存命中率统计
    stats.active_threads = 4; // TODO: 实现活跃线程统计

    return stats;
}

void PerformanceEngine::cleanup() {
    if (thumbnail_manager_) {
        thumbnail_manager_->stop();
    }

    if (sort_engine_) {
        sort_engine_->clearCache();
    }

    if (scroll_manager_) {
        scroll_manager_->clearCache();
    }
}

// C接口实现
extern "C" {
    PerformanceEngine* create_engine() {
        return new PerformanceEngine();
    }

    void destroy_engine(PerformanceEngine* engine) {
        delete engine;
    }

    void engine_initialize_data(PerformanceEngine* engine,
                               const FileItem* items, int count) {
        if (!engine || !items) return;

        std::vector<FileItem> data(items, items + count);
        engine->initializeData(data);
    }

    int* engine_search_and_filter(PerformanceEngine* engine,
                                 const char* query, const char* file_type_filter,
                                 int* result_count) {
        if (!engine || !result_count) return nullptr;

        std::string query_str = query ? query : "";
        std::string filter_str = file_type_filter ? file_type_filter : "";

        auto results = engine->searchAndFilter(query_str, filter_str);

        *result_count = static_cast<int>(results.size());
        if (results.empty()) return nullptr;

        int* result_array = new int[results.size()];
        std::copy(results.begin(), results.end(), result_array);

        return result_array;
    }

    int* engine_sort_results(PerformanceEngine* engine,
                           const int* indices, int count,
                           int sort_key, bool reverse,
                           int* result_count) {
        if (!engine || !indices || !result_count) return nullptr;

        std::vector<int> input_indices(indices, indices + count);
        auto results = engine->sortResults(input_indices,
                                         static_cast<SortEngine::SortKey>(sort_key),
                                         reverse);

        *result_count = static_cast<int>(results.size());
        if (results.empty()) return nullptr;

        int* result_array = new int[results.size()];
        std::copy(results.begin(), results.end(), result_array);

        return result_array;
    }

    void engine_update_visible_range(PerformanceEngine* engine,
                                   int start_index, int visible_count, int total_count) {
        if (!engine) return;
        engine->updateVisibleRange(start_index, visible_count, total_count);
    }

    int* engine_get_items_to_load(PerformanceEngine* engine, int* count) {
        if (!engine || !count) return nullptr;

        auto items = engine->getItemsToLoad();
        *count = static_cast<int>(items.size());

        if (items.empty()) return nullptr;

        int* result_array = new int[items.size()];
        std::copy(items.begin(), items.end(), result_array);

        return result_array;
    }

    int* engine_get_items_to_unload(PerformanceEngine* engine, int* count) {
        if (!engine || !count) return nullptr;

        auto items = engine->getItemsToUnload();
        *count = static_cast<int>(items.size());

        if (items.empty()) return nullptr;

        int* result_array = new int[items.size()];
        std::copy(items.begin(), items.end(), result_array);

        return result_array;
    }

    void engine_mark_item_loaded(PerformanceEngine* engine, int index) {
        if (!engine) return;
        engine->markItemLoaded(index);
    }

    void engine_request_thumbnail(PerformanceEngine* engine,
                                int id, const char* file_path, int priority) {
        if (!engine || !file_path) return;
        engine->requestThumbnail(id, std::string(file_path), priority);
    }

    bool engine_is_thumbnail_completed(PerformanceEngine* engine, int id) {
        if (!engine) return false;
        return engine->isThumbnailCompleted(id);
    }

    FileItem* engine_get_item(PerformanceEngine* engine, int index) {
        if (!engine) return nullptr;

        try {
            const FileItem& item = engine->getItem(index);
            FileItem* result = new FileItem(item);
            return result;
        } catch (...) {
            return nullptr;
        }
    }

    void free_int_array(int* array) {
        delete[] array;
    }

    void free_file_item(FileItem* item) {
        delete item;
    }
}
