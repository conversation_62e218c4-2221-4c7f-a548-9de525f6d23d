# 智能素材管理器

基于AI技术的现代化素材管理软件，为设计师、摄影师和内容创作者提供智能化的数字素材管理解决方案。

## 🌟 主要特性

### 📁 智能文件管理
- **非侵入式管理**：采用索引模式，不移动或修改原始文件
- **批量导入**：支持拖拽导入文件和文件夹
- **实时监控**：自动检测文件变化并更新索引
- **多格式支持**：支持图片、视频、音频、设计文件等多种格式

### 🤖 AI智能分析
- **自动标签生成**：基于图像内容自动生成描述性标签
- **颜色分析**：提取主色调和调色板
- **相似度检测**：智能识别相似和重复文件
- **内容识别**：物体检测和场景分析

### 🔍 强大搜索功能
- **多维度搜索**：支持文件名、标签、颜色、尺寸等多种搜索条件
- **以图搜图**：上传图片查找相似素材
- **智能筛选**：按类型、时间、评分等快速筛选
- **搜索建议**：智能搜索提示和历史记录

### 🎨 现代化界面
- **扁平化设计**：简洁美观的现代化界面
- **主题切换**：支持浅色和深色主题
- **响应式布局**：自适应不同屏幕尺寸
- **流畅动画**：优雅的界面过渡效果

### ⚡ 高性能优化
- **虚拟滚动**：处理大量文件时保持流畅
- **多线程处理**：后台异步处理任务
- **智能缓存**：优化加载速度和内存使用
- **增量更新**：只处理变化的文件

## 🚀 快速开始

### 系统要求
- Windows 10/11
- Python 3.12+
- 4GB+ 内存
- 1GB+ 可用磁盘空间

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/smartasset/smart-asset-manager.git
cd smart-asset-manager
```

2. **创建虚拟环境**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **运行程序**
```bash
python main.py
```

### 首次使用

1. 启动程序后，点击"导入文件"或"导入文件夹"
2. 选择要管理的素材文件或文件夹
3. 程序会自动分析文件并生成缩略图和标签
4. 使用搜索功能快速找到需要的素材

## 📖 使用指南

### 文件导入
- **拖拽导入**：直接将文件或文件夹拖拽到程序窗口
- **菜单导入**：使用"文件"菜单中的导入选项
- **批量导入**：支持一次性导入整个文件夹

### 搜索和筛选
- **快速搜索**：在搜索框中输入关键词
- **高级筛选**：使用工具栏中的筛选选项
- **标签搜索**：点击侧边栏中的标签进行筛选
- **颜色搜索**：使用颜色筛选器按颜色查找

### 标签管理
- **自动标签**：AI自动生成的内容标签
- **手动标签**：用户自定义添加的标签
- **标签编辑**：在预览面板中管理标签
- **标签云**：查看最常用的标签

### 视图模式
- **网格视图**：大缩略图展示，适合浏览图片
- **列表视图**：紧凑列表，显示基本信息
- **详细视图**：表格形式，显示完整文件信息

## 🛠️ 技术架构

### 开发技术栈
- **语言**：Python 3.12
- **GUI框架**：PySide6 (Qt6)
- **数据库**：SQLite
- **图像处理**：Pillow, OpenCV
- **机器学习**：scikit-learn
- **架构模式**：MVC

### 项目结构
```
smart-asset-manager/
├── main.py                 # 主程序入口
├── theme/                  # 主题管理
│   └── theme_manager.py
├── ui/                     # 用户界面
│   ├── main_window.py
│   ├── components/         # UI组件
│   └── dialogs/           # 对话框
├── database/              # 数据库管理
│   └── db_manager.py
├── core/                  # 核心功能
│   ├── file_manager.py
│   └── search_engine.py
├── ai/                    # AI分析
│   └── ai_analyzer.py
├── utils/                 # 工具模块
│   └── config_manager.py
└── requirements.txt       # 依赖列表
```

## 🔧 配置说明

### 配置文件位置
- Windows: `%APPDATA%/SmartAssetManager/config.json`
- 配置文件采用JSON格式，支持手动编辑

### 主要配置项
- **主题设置**：界面主题和颜色方案
- **性能设置**：缓存大小、并发任务数
- **AI设置**：自动分析选项和阈值
- **搜索设置**：搜索行为和历史记录

## 🤝 贡献指南

欢迎贡献代码和建议！

### 开发环境设置
1. Fork 项目到你的GitHub账户
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

### 代码规范
- 遵循PEP 8代码风格
- 添加适当的注释和文档
- 编写单元测试
- 确保代码通过所有测试

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 🎉 首次发布
- ✨ 基础文件管理功能
- 🤖 AI自动标签生成
- 🔍 多维度搜索功能
- 🎨 现代化界面设计
- ⚡ 性能优化

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 📞 联系我们

- **项目主页**：https://github.com/smartasset/smart-asset-manager
- **问题反馈**：https://github.com/smartasset/smart-asset-manager/issues
- **邮箱**：<EMAIL>

## 🙏 致谢

感谢以下开源项目的支持：
- [PySide6](https://www.qt.io/qt-for-python) - 强大的GUI框架
- [Pillow](https://pillow.readthedocs.io/) - 图像处理库
- [OpenCV](https://opencv.org/) - 计算机视觉库
- [scikit-learn](https://scikit-learn.org/) - 机器学习库

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
