#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持久化删除测试工具
测试删除和清空操作的持久化效果
"""

import sys
import time
import os
import sqlite3
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QListWidget, QListWidgetItem, QCheckBox,
                               QSpinBox, QProgressBar, QMessageBox, QFileDialog,
                               QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class PersistentDeleteTestWindow(QMainWindow):
    """持久化删除测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("💾 持久化删除测试工具")
        self.setGeometry(100, 100, 1200, 800)
        
        self.db_path = "data/smart_asset_manager.db"
        self.setup_ui()
        self.refresh_database_view()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("💾 持久化删除测试工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 功能说明
        desc_group = QGroupBox("📋 测试说明")
        desc_layout = QVBoxLayout(desc_group)
        
        desc_text = QLabel("""
🎯 持久化删除测试目标：
• 验证删除操作是否真正从数据库中删除记录
• 验证清空操作是否真正清空数据库
• 验证重启软件后删除的素材不再显示

🔧 测试步骤：
1. 查看当前数据库中的素材记录
2. 在主程序中执行删除/清空操作
3. 刷新数据库视图，验证记录是否被删除
4. 重启主程序，验证素材是否还会显示
        """)
        desc_text.setWordWrap(True)
        desc_text.setStyleSheet("color: #333; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        desc_layout.addWidget(desc_text)
        
        layout.addWidget(desc_group)
        
        # 控制面板
        control_group = QGroupBox("🎛️ 测试控制")
        control_layout = QVBoxLayout(control_group)
        
        # 数据库操作按钮
        db_layout = QHBoxLayout()
        
        refresh_db_btn = QPushButton("🔄 刷新数据库视图")
        refresh_db_btn.clicked.connect(self.refresh_database_view)
        refresh_db_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        db_layout.addWidget(refresh_db_btn)
        
        add_test_data_btn = QPushButton("➕ 添加测试数据")
        add_test_data_btn.clicked.connect(self.add_test_data)
        add_test_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        db_layout.addWidget(add_test_data_btn)
        
        clear_db_btn = QPushButton("🗑️ 清空数据库")
        clear_db_btn.clicked.connect(self.clear_database)
        clear_db_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        db_layout.addWidget(clear_db_btn)
        
        db_layout.addStretch()
        control_layout.addLayout(db_layout)
        
        # 主程序测试按钮
        main_layout = QHBoxLayout()
        
        open_main_btn = QPushButton("🚀 打开主程序")
        open_main_btn.clicked.connect(self.open_main_app)
        open_main_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        main_layout.addWidget(open_main_btn)
        
        restart_main_btn = QPushButton("🔄 重启主程序")
        restart_main_btn.clicked.connect(self.restart_main_app)
        restart_main_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        main_layout.addWidget(restart_main_btn)
        
        main_layout.addStretch()
        control_layout.addLayout(main_layout)
        
        layout.addWidget(control_group)
        
        # 数据库状态显示
        status_group = QGroupBox("📊 数据库状态")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("准备就绪")
        self.status_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        # 数据库内容表格
        table_group = QGroupBox("🗄️ 数据库素材记录")
        table_layout = QVBoxLayout(table_group)
        
        self.db_table = QTableWidget()
        self.db_table.setAlternatingRowColors(True)
        self.db_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.db_table.horizontalHeader().setStretchLastSection(True)
        table_layout.addWidget(self.db_table)
        
        layout.addWidget(table_group)
        
        # 测试日志
        log_group = QGroupBox("📝 测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumHeight(120)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def refresh_database_view(self):
        """刷新数据库视图"""
        try:
            self.log("🔄 刷新数据库视图...")
            
            if not os.path.exists(self.db_path):
                self.log("⚠️ 数据库文件不存在")
                self.status_label.setText("数据库文件不存在")
                self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                self.db_table.setRowCount(0)
                return
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查询素材记录
            cursor.execute("""
                SELECT id, name, file_path, file_type, size, created_time, category_id
                FROM materials 
                ORDER BY id DESC
            """)
            
            rows = cursor.fetchall()
            
            # 设置表格
            self.db_table.setRowCount(len(rows))
            self.db_table.setColumnCount(7)
            self.db_table.setHorizontalHeaderLabels([
                "ID", "名称", "文件路径", "类型", "大小", "创建时间", "分类ID"
            ])
            
            # 填充数据
            for row_idx, row_data in enumerate(rows):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data) if cell_data else "")
                    self.db_table.setItem(row_idx, col_idx, item)
            
            # 调整列宽
            self.db_table.resizeColumnsToContents()
            
            # 更新状态
            self.status_label.setText(f"数据库中有 {len(rows)} 条素材记录")
            if len(rows) == 0:
                self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            else:
                self.status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            
            self.log(f"✅ 数据库视图已刷新，共 {len(rows)} 条记录")
            
            conn.close()
            
        except Exception as e:
            self.log(f"❌ 刷新数据库视图失败: {e}")
            self.status_label.setText("数据库访问失败")
            self.status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
    
    def add_test_data(self):
        """添加测试数据"""
        try:
            self.log("➕ 添加测试数据...")
            
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建表（如果不存在）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS materials (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    file_path TEXT UNIQUE NOT NULL,
                    file_type TEXT,
                    size INTEGER,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    category_id TEXT,
                    tags TEXT,
                    rating INTEGER DEFAULT 0,
                    description TEXT,
                    thumbnail_path TEXT,
                    metadata TEXT
                )
            """)
            
            # 添加测试数据
            test_data = [
                ("测试图片1.jpg", "test_images/test1.jpg", "image", 1024000, "images"),
                ("测试图片2.png", "test_images/test2.png", "image", 2048000, "images"),
                ("测试音频1.mp3", "test_audio/test1.mp3", "audio", 5120000, "audio"),
                ("测试视频1.mp4", "test_video/test1.mp4", "video", 10240000, "video"),
                ("测试文档1.pdf", "test_docs/test1.pdf", "document", 512000, "documents"),
            ]
            
            added_count = 0
            for name, file_path, file_type, size, category_id in test_data:
                try:
                    cursor.execute("""
                        INSERT INTO materials (name, file_path, file_type, size, category_id)
                        VALUES (?, ?, ?, ?, ?)
                    """, (name, file_path, file_type, size, category_id))
                    added_count += 1
                except sqlite3.IntegrityError:
                    # 文件路径已存在，跳过
                    pass
            
            conn.commit()
            conn.close()
            
            self.log(f"✅ 成功添加 {added_count} 条测试数据")
            
            # 刷新视图
            self.refresh_database_view()
            
        except Exception as e:
            self.log(f"❌ 添加测试数据失败: {e}")
    
    def clear_database(self):
        """清空数据库"""
        try:
            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认清空数据库",
                "确定要清空数据库中的所有素材记录吗？\n\n此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                self.log("🚫 用户取消了清空数据库操作")
                return
            
            self.log("🗑️ 清空数据库...")
            
            if not os.path.exists(self.db_path):
                self.log("⚠️ 数据库文件不存在")
                return
            
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取清空前的记录数
            cursor.execute("SELECT COUNT(*) FROM materials")
            before_count = cursor.fetchone()[0]
            
            # 清空表
            cursor.execute("DELETE FROM materials")
            conn.commit()
            conn.close()
            
            self.log(f"✅ 已清空数据库，删除了 {before_count} 条记录")
            
            # 刷新视图
            self.refresh_database_view()
            
        except Exception as e:
            self.log(f"❌ 清空数据库失败: {e}")
    
    def open_main_app(self):
        """打开主程序"""
        try:
            self.log("🚀 启动主程序...")
            
            import subprocess
            import sys
            
            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"], 
                           cwd=str(project_root))
            
            self.log("✅ 主程序已启动")
            self.log("💡 请在主程序中进行删除/清空操作，然后回到此工具刷新数据库视图")
            
        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")
    
    def restart_main_app(self):
        """重启主程序"""
        try:
            self.log("🔄 重启主程序进行持久化测试...")
            
            # 先杀死可能存在的主程序进程
            import subprocess
            import sys
            
            try:
                # Windows下杀死Python进程
                subprocess.run(["taskkill", "/f", "/im", "python.exe"], 
                             capture_output=True, timeout=5)
                time.sleep(1)
            except:
                pass
            
            # 重新启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"], 
                           cwd=str(project_root))
            
            self.log("✅ 主程序已重启")
            self.log("💡 检查重启后的主程序是否还显示之前删除的素材")
            
        except Exception as e:
            self.log(f"❌ 重启主程序失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = PersistentDeleteTestWindow()
    window.show()
    
    print("持久化删除测试工具启动成功！")
    print("测试流程：")
    print("1. 💾 查看数据库中的素材记录")
    print("2. 🚀 打开主程序进行删除/清空操作")
    print("3. 🔄 刷新数据库视图验证记录是否删除")
    print("4. 🔄 重启主程序验证素材是否还显示")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
