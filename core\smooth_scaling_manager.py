#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流畅缩放管理器
实现图片查看器级别的流畅缩放体验
"""

import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Callable
from collections import OrderedDict
import weakref

from PySide6.QtCore import QObject, Signal, QTimer, QSize, Qt
from PySide6.QtGui import QPixmap, QImage
from PySide6.QtWidgets import QApplication

class ScalingCache:
    """多级缩放缓存"""

    def __init__(self, max_original_cache: int = 20, max_scaled_cache: int = 100):
        self.max_original_cache = max_original_cache
        self.max_scaled_cache = max_scaled_cache

        # 原图缓存 (文件路径 -> QPixmap)
        self.original_cache = OrderedDict()

        # 缩放图缓存 (文件路径_尺寸 -> QPixmap)
        self.scaled_cache = OrderedDict()

        # 访问时间记录
        self.access_times = {}

        self.lock = threading.RLock()

    def get_original(self, file_path: str) -> Optional[QPixmap]:
        """获取原图"""
        with self.lock:
            if file_path in self.original_cache:
                # 移动到末尾（最近使用）
                pixmap = self.original_cache.pop(file_path)
                self.original_cache[file_path] = pixmap
                self.access_times[file_path] = time.time()
                return pixmap
            return None

    def put_original(self, file_path: str, pixmap: QPixmap):
        """缓存原图"""
        with self.lock:
            # 检查缓存大小
            if len(self.original_cache) >= self.max_original_cache:
                # 移除最旧的项
                oldest_key = next(iter(self.original_cache))
                del self.original_cache[oldest_key]
                if oldest_key in self.access_times:
                    del self.access_times[oldest_key]

            self.original_cache[file_path] = pixmap
            self.access_times[file_path] = time.time()

    def get_scaled(self, file_path: str, size: int) -> Optional[QPixmap]:
        """获取缩放图"""
        cache_key = f"{file_path}_{size}"
        with self.lock:
            if cache_key in self.scaled_cache:
                # 移动到末尾（最近使用）
                pixmap = self.scaled_cache.pop(cache_key)
                self.scaled_cache[cache_key] = pixmap
                return pixmap
            return None

    def put_scaled(self, file_path: str, size: int, pixmap: QPixmap):
        """缓存缩放图"""
        cache_key = f"{file_path}_{size}"
        with self.lock:
            # 检查缓存大小
            if len(self.scaled_cache) >= self.max_scaled_cache:
                # 移除最旧的项
                oldest_key = next(iter(self.scaled_cache))
                del self.scaled_cache[oldest_key]

            self.scaled_cache[cache_key] = pixmap

    def clear(self):
        """清空缓存"""
        with self.lock:
            self.original_cache.clear()
            self.scaled_cache.clear()
            self.access_times.clear()

    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计"""
        with self.lock:
            return {
                'original_count': len(self.original_cache),
                'scaled_count': len(self.scaled_cache),
                'total_memory_mb': self._estimate_memory_usage()
            }

    def _estimate_memory_usage(self) -> int:
        """估算内存使用量（MB）"""
        total_bytes = 0

        # 估算原图内存
        for pixmap in self.original_cache.values():
            total_bytes += pixmap.width() * pixmap.height() * 4  # RGBA

        # 估算缩放图内存
        for pixmap in self.scaled_cache.values():
            total_bytes += pixmap.width() * pixmap.height() * 4  # RGBA

        return total_bytes // (1024 * 1024)

class SmoothScalingManager(QObject):
    """流畅缩放管理器"""

    # 信号定义
    scaling_completed = Signal(str, int, QPixmap)  # 文件路径, 目标尺寸, 缩放后图片
    batch_scaling_completed = Signal(dict)  # 批量缩放完成
    scaling_progress = Signal(int, int)  # 当前进度, 总数

    def __init__(self):
        super().__init__()

        # 缓存系统
        self.cache = ScalingCache()

        # 缩放配置
        self.config = {
            'fast_preview_enabled': True,  # 启用快速预览
            'progressive_quality': True,   # 渐进式质量提升
            'batch_size': 10,             # 批量处理大小
            'max_concurrent': 4,          # 最大并发数
            'preload_distance': 5,        # 预加载距离
        }

        # 缩放队列和状态
        self.scaling_queue = []
        self.active_scalings = set()
        self.pending_requests = {}  # 文件路径 -> 目标尺寸列表

        # 性能统计
        self.stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'fast_previews': 0,
            'high_quality_renders': 0,
            'average_scaling_time': 0.0
        }

        # 定时器
        self.batch_timer = QTimer()
        self.batch_timer.timeout.connect(self._process_batch_queue)
        self.batch_timer.setSingleShot(True)

        # 预加载队列和定时器
        self.preload_queue = []
        self.preload_timer = QTimer()
        self.preload_timer.timeout.connect(self._process_preload_queue)
        self.preload_timer.setSingleShot(True)

        print("流畅缩放管理器初始化完成")

    def request_scaling(self, file_path: str, target_size: int, priority: str = 'normal') -> bool:
        """请求缩放（单个）"""
        try:
            self.stats['total_requests'] += 1

            # 检查缓存
            cached_pixmap = self.cache.get_scaled(file_path, target_size)
            if cached_pixmap:
                self.stats['cache_hits'] += 1
                self.scaling_completed.emit(file_path, target_size, cached_pixmap)
                return True

            # 添加到队列
            request_key = f"{file_path}_{target_size}"
            if request_key not in self.active_scalings:
                self.scaling_queue.append({
                    'file_path': file_path,
                    'target_size': target_size,
                    'priority': priority,
                    'timestamp': time.time()
                })
                self.active_scalings.add(request_key)

                # 启动批量处理
                if not self.batch_timer.isActive():
                    self.batch_timer.start(50)  # 50ms延迟批量处理

            return True

        except Exception as e:
            print(f"缩放请求失败 {file_path}: {e}")
            return False

    def request_batch_scaling(self, file_paths: List[str], target_size: int, priority: str = 'normal'):
        """批量缩放请求"""
        for file_path in file_paths:
            self.request_scaling(file_path, target_size, priority)

    def _process_batch_queue(self):
        """处理批量队列"""
        if not self.scaling_queue:
            return

        # 按优先级排序
        priority_order = {'urgent': 0, 'high': 1, 'normal': 2, 'low': 3}
        self.scaling_queue.sort(key=lambda x: (
            priority_order.get(x['priority'], 2),
            x['timestamp']
        ))

        # 处理批量
        batch = self.scaling_queue[:self.config['batch_size']]
        self.scaling_queue = self.scaling_queue[self.config['batch_size']:]

        if batch:
            self._execute_batch_scaling(batch)

        # 如果还有队列，继续处理
        if self.scaling_queue:
            self.batch_timer.start(10)  # 更短的间隔

    def _execute_batch_scaling(self, batch: List[Dict]):
        """执行批量缩放"""
        results = {}

        for item in batch:
            file_path = item['file_path']
            target_size = item['target_size']

            try:
                # 生成缩放图片
                scaled_pixmap = self._generate_scaled_pixmap(file_path, target_size)

                if scaled_pixmap and not scaled_pixmap.isNull():
                    # 缓存结果
                    self.cache.put_scaled(file_path, target_size, scaled_pixmap)
                    results[file_path] = scaled_pixmap

                    # 发送单个完成信号
                    self.scaling_completed.emit(file_path, target_size, scaled_pixmap)

                # 从活跃集合中移除
                request_key = f"{file_path}_{target_size}"
                self.active_scalings.discard(request_key)

            except Exception as e:
                print(f"批量缩放失败 {file_path}: {e}")
                request_key = f"{file_path}_{target_size}"
                self.active_scalings.discard(request_key)

        # 发送批量完成信号
        if results:
            self.batch_scaling_completed.emit(results)

    def _generate_scaled_pixmap(self, file_path: str, target_size: int) -> Optional[QPixmap]:
        """生成缩放图片"""
        start_time = time.time()

        try:
            # 尝试从原图缓存获取
            original_pixmap = self.cache.get_original(file_path)

            if not original_pixmap:
                # 加载原图
                original_pixmap = QPixmap(file_path)
                if original_pixmap.isNull():
                    return None

                # 缓存原图（如果不是太大）
                if original_pixmap.width() <= 2048 and original_pixmap.height() <= 2048:
                    self.cache.put_original(file_path, original_pixmap)

            # 执行缩放
            if self.config['fast_preview_enabled']:
                # 快速预览模式
                scaled_pixmap = self._fast_scale(original_pixmap, target_size)
                self.stats['fast_previews'] += 1

                # 如果启用渐进式质量，稍后生成高质量版本
                if self.config['progressive_quality']:
                    QTimer.singleShot(100, lambda: self._generate_high_quality_version(
                        file_path, target_size, original_pixmap
                    ))
            else:
                # 直接生成高质量版本
                scaled_pixmap = self._high_quality_scale(original_pixmap, target_size)
                self.stats['high_quality_renders'] += 1

            # 更新统计
            scaling_time = time.time() - start_time
            total_time = self.stats['average_scaling_time'] * (self.stats['total_requests'] - 1)
            self.stats['average_scaling_time'] = (total_time + scaling_time) / self.stats['total_requests']

            return scaled_pixmap

        except Exception as e:
            print(f"生成缩放图片失败 {file_path}: {e}")
            return None

    def _fast_scale(self, pixmap: QPixmap, target_size: int) -> QPixmap:
        """快速缩放（低质量但快速）- 充满显示"""
        # 计算缩放比例以充满整个区域
        scale_w = target_size / pixmap.width()
        scale_h = target_size / pixmap.height()
        scale = max(scale_w, scale_h)  # 使用max确保充满

        # 计算缩放后尺寸
        new_width = int(pixmap.width() * scale)
        new_height = int(pixmap.height() * scale)

        # 执行缩放
        scaled_pixmap = pixmap.scaled(
            new_width, new_height,
            Qt.KeepAspectRatio,
            Qt.FastTransformation
        )

        # 如果超出目标尺寸，进行居中裁剪
        if new_width > target_size or new_height > target_size:
            crop_x = max(0, (new_width - target_size) // 2)
            crop_y = max(0, (new_height - target_size) // 2)

            scaled_pixmap = scaled_pixmap.copy(
                crop_x, crop_y,
                min(target_size, new_width),
                min(target_size, new_height)
            )

        return scaled_pixmap

    def _high_quality_scale(self, pixmap: QPixmap, target_size: int) -> QPixmap:
        """高质量缩放 - 充满显示"""
        # 计算缩放比例以充满整个区域
        scale_w = target_size / pixmap.width()
        scale_h = target_size / pixmap.height()
        scale = max(scale_w, scale_h)  # 使用max确保充满

        # 计算缩放后尺寸
        new_width = int(pixmap.width() * scale)
        new_height = int(pixmap.height() * scale)

        # 执行高质量缩放
        scaled_pixmap = pixmap.scaled(
            new_width, new_height,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )

        # 如果超出目标尺寸，进行居中裁剪
        if new_width > target_size or new_height > target_size:
            crop_x = max(0, (new_width - target_size) // 2)
            crop_y = max(0, (new_height - target_size) // 2)

            scaled_pixmap = scaled_pixmap.copy(
                crop_x, crop_y,
                min(target_size, new_width),
                min(target_size, new_height)
            )

        return scaled_pixmap

    def _generate_high_quality_version(self, file_path: str, target_size: int, original_pixmap: QPixmap):
        """生成高质量版本（延迟）"""
        try:
            # 生成高质量版本
            hq_pixmap = self._high_quality_scale(original_pixmap, target_size)

            # 更新缓存
            self.cache.put_scaled(file_path, target_size, hq_pixmap)

            # 发送更新信号
            self.scaling_completed.emit(file_path, target_size, hq_pixmap)
            self.stats['high_quality_renders'] += 1

        except Exception as e:
            print(f"生成高质量版本失败 {file_path}: {e}")

    def preload_for_viewport(self, visible_files: List[str], target_size: int):
        """为视口预加载"""
        # 预加载可见文件周围的文件
        for file_path in visible_files:
            self.request_scaling(file_path, target_size, 'high')

    def _process_preload_queue(self):
        """处理预加载队列"""
        if not self.preload_queue:
            return

        # 处理预加载任务
        batch = self.preload_queue[:self.config['batch_size']]
        self.preload_queue = self.preload_queue[self.config['batch_size']:]

        for item in batch:
            file_path = item['file_path']
            target_size = item['target_size']
            self.request_scaling(file_path, target_size, 'low')

        # 如果还有队列，继续处理
        if self.preload_queue:
            self.preload_timer.start(100)  # 100ms延迟

    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.scaling_queue.clear()
        self.active_scalings.clear()
        print("缩放缓存已清空")

    def get_scaling_stats(self) -> Dict[str, any]:
        """获取缩放统计"""
        cache_stats = self.cache.get_cache_stats()

        return {
            'requests': {
                'total': self.stats['total_requests'],
                'cache_hits': self.stats['cache_hits'],
                'cache_hit_rate': (self.stats['cache_hits'] / max(1, self.stats['total_requests'])) * 100,
                'fast_previews': self.stats['fast_previews'],
                'high_quality_renders': self.stats['high_quality_renders'],
                'average_time': self.stats['average_scaling_time']
            },
            'cache': cache_stats,
            'queue': {
                'pending': len(self.scaling_queue),
                'active': len(self.active_scalings)
            }
        }

    def optimize_for_size_change(self, old_size: int, new_size: int, visible_files: List[str]):
        """为尺寸变化优化"""
        # 清理旧尺寸的缓存（可选）
        if abs(new_size - old_size) > 50:  # 尺寸变化较大时清理
            self._cleanup_size_cache(old_size)

        # 优先处理可见文件
        for file_path in visible_files:
            self.request_scaling(file_path, new_size, 'urgent')

    def _cleanup_size_cache(self, size: int):
        """清理指定尺寸的缓存"""
        with self.cache.lock:
            keys_to_remove = [
                key for key in self.cache.scaled_cache.keys()
                if key.endswith(f"_{size}")
            ]

            for key in keys_to_remove:
                del self.cache.scaled_cache[key]

    def set_config(self, config: Dict[str, any]):
        """设置配置"""
        self.config.update(config)
        print(f"缩放配置已更新: {config}")

# 全局流畅缩放管理器实例
_smooth_scaling_manager = None

def get_smooth_scaling_manager() -> SmoothScalingManager:
    """获取全局流畅缩放管理器实例"""
    global _smooth_scaling_manager
    if _smooth_scaling_manager is None:
        _smooth_scaling_manager = SmoothScalingManager()
    return _smooth_scaling_manager

def cleanup_smooth_scaling_manager():
    """清理全局流畅缩放管理器"""
    global _smooth_scaling_manager
    if _smooth_scaling_manager:
        _smooth_scaling_manager.clear_cache()
        _smooth_scaling_manager = None
