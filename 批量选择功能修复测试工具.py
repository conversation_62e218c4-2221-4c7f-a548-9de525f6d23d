#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量选择功能修复测试工具
测试修复后的全选、反选、清空选择、删除选中功能
"""

import sys
import time
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QListWidget, QListWidgetItem, QCheckBox,
                               QSpinBox, QProgressBar, QMessageBox, QFileDialog)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class BatchSelectionFixTestWindow(QMainWindow):
    """批量选择功能修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 批量选择功能修复测试工具")
        self.setGeometry(100, 100, 1200, 800)
        
        self.test_items = []
        self.selected_items = []
        self.setup_ui()
        self.create_test_data()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔧 批量选择功能修复测试工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 修复验证面板
        fix_group = QGroupBox("🔧 修复验证")
        fix_layout = QVBoxLayout(fix_group)
        
        # 问题1：高DPI警告修复
        dpi_layout = QHBoxLayout()
        dpi_layout.addWidget(QLabel("问题1: 高DPI弃用警告修复"))
        test_dpi_btn = QPushButton("🔍 测试高DPI设置")
        test_dpi_btn.clicked.connect(self.test_high_dpi_fix)
        dpi_layout.addWidget(test_dpi_btn)
        dpi_layout.addStretch()
        fix_layout.addLayout(dpi_layout)
        
        # 问题2：删除后列表更新修复
        update_layout = QHBoxLayout()
        update_layout.addWidget(QLabel("问题2: 删除后列表更新修复"))
        test_update_btn = QPushButton("🔍 测试删除更新")
        test_update_btn.clicked.connect(self.test_delete_update_fix)
        update_layout.addWidget(test_update_btn)
        update_layout.addStretch()
        fix_layout.addLayout(update_layout)
        
        # 问题3：清空功能修复
        clear_layout = QHBoxLayout()
        clear_layout.addWidget(QLabel("问题3: 清空功能分离修复"))
        test_clear_btn = QPushButton("🔍 测试清空分离")
        test_clear_btn.clicked.connect(self.test_clear_separation_fix)
        clear_layout.addWidget(test_clear_btn)
        clear_layout.addStretch()
        fix_layout.addLayout(clear_layout)
        
        layout.addWidget(fix_group)
        
        # 功能测试面板
        test_group = QGroupBox("🧪 功能测试")
        test_layout = QVBoxLayout(test_group)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("✅ 全选")
        select_all_btn.clicked.connect(self.test_select_all)
        batch_layout.addWidget(select_all_btn)
        
        invert_selection_btn = QPushButton("🔄 反选")
        invert_selection_btn.clicked.connect(self.test_invert_selection)
        batch_layout.addWidget(invert_selection_btn)
        
        clear_selection_btn = QPushButton("❌ 清空选择")
        clear_selection_btn.clicked.connect(self.test_clear_selection)
        batch_layout.addWidget(clear_selection_btn)
        
        delete_selected_btn = QPushButton("🗑️ 删除选中")
        delete_selected_btn.clicked.connect(self.test_delete_selected)
        batch_layout.addWidget(delete_selected_btn)
        
        batch_layout.addStretch()
        test_layout.addLayout(batch_layout)
        
        # 创建测试文件按钮
        file_layout = QHBoxLayout()
        
        create_files_btn = QPushButton("📁 创建测试文件")
        create_files_btn.clicked.connect(self.create_test_files)
        file_layout.addWidget(create_files_btn)
        
        open_main_app_btn = QPushButton("🚀 打开主程序")
        open_main_app_btn.clicked.connect(self.open_main_app)
        file_layout.addWidget(open_main_app_btn)
        
        file_layout.addStretch()
        test_layout.addLayout(file_layout)
        
        layout.addWidget(test_group)
        
        # 状态显示
        status_group = QGroupBox("📊 测试状态")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("准备就绪")
        self.status_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        # 测试项目列表
        list_group = QGroupBox("📄 测试项目列表")
        list_layout = QVBoxLayout(list_group)
        
        self.item_list = QListWidget()
        self.item_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.item_list.itemSelectionChanged.connect(self.on_selection_changed)
        list_layout.addWidget(self.item_list)
        
        layout.addWidget(list_group)
        
        # 测试日志
        log_group = QGroupBox("📝 测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def create_test_data(self):
        """创建测试数据"""
        try:
            self.log("🔧 创建测试数据...")
            
            # 清空现有数据
            self.item_list.clear()
            self.test_items = []
            self.selected_items = []
            
            # 生成测试项目
            file_types = ["图片", "音频", "视频", "文档", "设计"]
            for i in range(20):
                file_type = file_types[i % len(file_types)]
                item_name = f"{file_type}文件_{i+1:03d}.ext"
                
                # 创建列表项
                list_item = QListWidgetItem(item_name)
                list_item.setData(Qt.UserRole, i)
                self.item_list.addItem(list_item)
                
                # 保存到测试数据
                self.test_items.append({
                    'id': i,
                    'name': item_name,
                    'type': file_type
                })
            
            self.update_status_display()
            self.log(f"✅ 成功创建 {len(self.test_items)} 个测试项目")
            
        except Exception as e:
            self.log(f"❌ 创建测试数据失败: {e}")
    
    def test_high_dpi_fix(self):
        """测试高DPI修复"""
        try:
            self.log("🔍 测试高DPI弃用警告修复...")
            
            # 检查Qt版本
            from PySide6 import __version__ as pyside_version
            self.log(f"📋 PySide6版本: {pyside_version}")
            
            # 检查应用程序属性
            app = QApplication.instance()
            if app:
                # 在Qt6中，这些属性已经默认启用
                self.log("✅ Qt6中高DPI缩放默认启用")
                self.log("✅ 不再需要手动设置AA_EnableHighDpiScaling")
                self.log("✅ 不再需要手动设置AA_UseHighDpiPixmaps")
                
                # 检查当前设置
                try:
                    # 检查有效的属性
                    attrs = [
                        (Qt.AA_DontCreateNativeWidgetSiblings, "AA_DontCreateNativeWidgetSiblings"),
                        (Qt.AA_ShareOpenGLContexts, "AA_ShareOpenGLContexts")
                    ]
                    
                    for attr, name in attrs:
                        is_set = app.testAttribute(attr)
                        self.log(f"📋 {name}: {'启用' if is_set else '禁用'}")
                        
                except Exception as e:
                    self.log(f"⚠️ 检查属性时出错: {e}")
                
                self.log("✅ 高DPI修复验证完成")
            else:
                self.log("❌ 无法获取应用程序实例")
                
        except Exception as e:
            self.log(f"❌ 高DPI修复测试失败: {e}")
    
    def test_delete_update_fix(self):
        """测试删除后列表更新修复"""
        try:
            self.log("🔍 测试删除后列表更新修复...")
            
            # 选择一些项目
            if self.item_list.count() > 5:
                for i in range(3):  # 选择前3个
                    self.item_list.item(i).setSelected(True)
                
                selected_count = len(self.item_list.selectedItems())
                self.log(f"📋 选择了 {selected_count} 个项目")
                
                # 模拟删除操作
                selected_items = self.item_list.selectedItems()
                for item in selected_items:
                    row = self.item_list.row(item)
                    self.item_list.takeItem(row)
                
                remaining_count = self.item_list.count()
                self.log(f"✅ 删除完成，剩余 {remaining_count} 个项目")
                self.log("✅ 列表已正确更新")
                
                # 重新创建测试数据
                QTimer.singleShot(2000, self.create_test_data)
                
            else:
                self.log("⚠️ 测试项目不足，请先创建测试数据")
                
        except Exception as e:
            self.log(f"❌ 删除更新测试失败: {e}")
    
    def test_clear_separation_fix(self):
        """测试清空功能分离修复"""
        try:
            self.log("🔍 测试清空功能分离修复...")
            
            # 选择一些项目
            if self.item_list.count() > 0:
                for i in range(min(5, self.item_list.count())):
                    self.item_list.item(i).setSelected(True)
                
                selected_count = len(self.item_list.selectedItems())
                self.log(f"📋 选择了 {selected_count} 个项目")
                
                # 测试清空选择（不删除项目）
                self.item_list.clearSelection()
                
                remaining_selected = len(self.item_list.selectedItems())
                total_items = self.item_list.count()
                
                self.log(f"✅ 清空选择完成")
                self.log(f"📋 选中项目: {remaining_selected}")
                self.log(f"📋 总项目数: {total_items}")
                
                if remaining_selected == 0 and total_items > 0:
                    self.log("✅ 清空选择功能正常 - 只清空选择，不删除项目")
                else:
                    self.log("❌ 清空选择功能异常")
                
            else:
                self.log("⚠️ 没有测试项目")
                
        except Exception as e:
            self.log(f"❌ 清空分离测试失败: {e}")
    
    def test_select_all(self):
        """测试全选功能"""
        try:
            self.log("✅ 测试全选功能...")
            self.item_list.selectAll()
            
            selected_count = len(self.item_list.selectedItems())
            total_count = self.item_list.count()
            
            self.log(f"✅ 全选完成: {selected_count}/{total_count}")
            
            if selected_count == total_count:
                self.log("✅ 全选功能正常")
            else:
                self.log("❌ 全选功能异常")
                
        except Exception as e:
            self.log(f"❌ 全选测试失败: {e}")
    
    def test_invert_selection(self):
        """测试反选功能"""
        try:
            self.log("🔄 测试反选功能...")
            
            # 记录当前选择
            before_selection = set(self.item_list.row(item) for item in self.item_list.selectedItems())
            all_items = set(range(self.item_list.count()))
            
            # 执行反选
            self.invert_list_selection()
            
            # 检查结果
            after_selection = set(self.item_list.row(item) for item in self.item_list.selectedItems())
            expected_selection = all_items - before_selection
            
            self.log(f"🔄 反选完成: {len(after_selection)} 项目")
            
            if after_selection == expected_selection:
                self.log("✅ 反选功能正常")
            else:
                self.log("❌ 反选功能异常")
                
        except Exception as e:
            self.log(f"❌ 反选测试失败: {e}")
    
    def test_clear_selection(self):
        """测试清空选择功能"""
        try:
            self.log("❌ 测试清空选择功能...")
            
            # 先选择一些项目
            for i in range(min(3, self.item_list.count())):
                self.item_list.item(i).setSelected(True)
            
            before_count = len(self.item_list.selectedItems())
            total_items = self.item_list.count()
            
            # 执行清空选择
            self.item_list.clearSelection()
            
            after_count = len(self.item_list.selectedItems())
            remaining_items = self.item_list.count()
            
            self.log(f"❌ 清空选择完成")
            self.log(f"📋 选择前: {before_count}, 选择后: {after_count}")
            self.log(f"📋 项目数: {total_items} -> {remaining_items}")
            
            if after_count == 0 and remaining_items == total_items:
                self.log("✅ 清空选择功能正常 - 只清空选择，保留所有项目")
            else:
                self.log("❌ 清空选择功能异常")
                
        except Exception as e:
            self.log(f"❌ 清空选择测试失败: {e}")
    
    def test_delete_selected(self):
        """测试删除选中功能"""
        try:
            self.log("🗑️ 测试删除选中功能...")
            
            # 选择一些项目
            if self.item_list.count() > 2:
                self.item_list.item(0).setSelected(True)
                self.item_list.item(1).setSelected(True)
                
                before_selected = len(self.item_list.selectedItems())
                before_total = self.item_list.count()
                
                # 执行删除
                selected_items = self.item_list.selectedItems()
                for item in selected_items:
                    row = self.item_list.row(item)
                    self.item_list.takeItem(row)
                
                after_selected = len(self.item_list.selectedItems())
                after_total = self.item_list.count()
                
                self.log(f"🗑️ 删除完成")
                self.log(f"📋 删除前: 选中{before_selected}, 总数{before_total}")
                self.log(f"📋 删除后: 选中{after_selected}, 总数{after_total}")
                
                if after_total == before_total - before_selected:
                    self.log("✅ 删除选中功能正常 - 删除了选中的项目")
                else:
                    self.log("❌ 删除选中功能异常")
                    
            else:
                self.log("⚠️ 测试项目不足")
                
        except Exception as e:
            self.log(f"❌ 删除选中测试失败: {e}")
    
    def create_test_files(self):
        """创建测试文件"""
        try:
            self.log("📁 创建测试文件...")
            
            test_dir = Path("test_batch_files")
            test_dir.mkdir(exist_ok=True)
            
            # 创建不同类型的测试文件
            test_files = [
                ("test_image1.jpg", "测试图片1"),
                ("test_image2.png", "测试图片2"),
                ("test_audio1.mp3", "测试音频1"),
                ("test_document1.pdf", "测试文档1"),
                ("test_video1.mp4", "测试视频1")
            ]
            
            created_count = 0
            for filename, description in test_files:
                file_path = test_dir / filename
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"{description}\n创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                created_count += 1
            
            self.log(f"✅ 成功创建 {created_count} 个测试文件到 {test_dir}")
            
        except Exception as e:
            self.log(f"❌ 创建测试文件失败: {e}")
    
    def open_main_app(self):
        """打开主程序"""
        try:
            self.log("🚀 启动主程序...")
            
            import subprocess
            import sys
            
            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"], 
                           cwd=str(project_root))
            
            self.log("✅ 主程序已启动")
            
        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")
    
    def invert_list_selection(self):
        """反选列表项目"""
        selected_items = self.item_list.selectedItems()
        selected_indices = set(self.item_list.row(item) for item in selected_items)
        
        self.item_list.clearSelection()
        
        for i in range(self.item_list.count()):
            if i not in selected_indices:
                self.item_list.item(i).setSelected(True)
    
    def on_selection_changed(self):
        """选择变更处理"""
        self.update_status_display()
    
    def update_status_display(self):
        """更新状态显示"""
        try:
            selected_count = len(self.item_list.selectedItems())
            total_count = self.item_list.count()
            
            if selected_count == 0:
                self.status_label.setText("未选择")
                self.status_label.setStyleSheet("color: #666;")
            elif selected_count == total_count and total_count > 0:
                self.status_label.setText(f"已全选 ({selected_count})")
                self.status_label.setStyleSheet("color: #27AE60; font-weight: bold;")
            else:
                self.status_label.setText(f"已选择 {selected_count}/{total_count}")
                self.status_label.setStyleSheet("color: #3498DB; font-weight: bold;")
                
        except Exception as e:
            self.log(f"❌ 更新状态显示失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = BatchSelectionFixTestWindow()
    window.show()
    
    print("批量选择功能修复测试工具启动成功！")
    print("修复验证：")
    print("1. 🔧 高DPI弃用警告修复")
    print("2. 🔧 删除后列表更新修复")
    print("3. 🔧 清空功能分离修复")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
