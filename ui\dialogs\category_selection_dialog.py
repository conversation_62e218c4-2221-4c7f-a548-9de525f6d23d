#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类选择对话框
用于选择素材要移动到的目标分类
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QTreeWidget, QTreeWidgetItem, 
                               QMessageBox, QFrame, QButtonBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor

class CategorySelectionDialog(QDialog):
    """分类选择对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择目标分类")
        self.setModal(True)
        self.resize(400, 500)
        
        self.selected_category = None
        self.setup_ui()
        self.load_categories()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("选择目标分类")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明文本
        desc = QLabel("请选择要将素材移动到的目标分类：")
        desc.setWordWrap(True)
        desc.setStyleSheet("color: #666; margin: 10px 0;")
        layout.addWidget(desc)
        
        # 分类树
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderHidden(True)
        self.category_tree.setRootIsDecorated(True)
        self.category_tree.itemClicked.connect(self.on_category_clicked)
        self.category_tree.itemDoubleClicked.connect(self.on_category_double_clicked)
        layout.addWidget(self.category_tree)
        
        # 选中分类显示
        self.selected_label = QLabel("未选择分类")
        self.selected_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(self.selected_label)
        
        # 按钮组
        button_box = QButtonBox(QButtonBox.Ok | QButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # 设置按钮状态
        self.ok_button = button_box.button(QButtonBox.Ok)
        self.ok_button.setText("移动到此分类")
        self.ok_button.setEnabled(False)
        
        cancel_button = button_box.button(QButtonBox.Cancel)
        cancel_button.setText("取消")
        
        layout.addWidget(button_box)
    
    def load_categories(self):
        """加载分类数据"""
        try:
            # 获取分类管理器
            from core.category_manager import get_category_manager
            category_manager = get_category_manager()
            
            # 获取所有分类
            all_categories = category_manager.get_all_categories()
            
            if not all_categories:
                self._load_default_categories()
                return
            
            # 按类型分组
            system_categories = []
            custom_categories = []
            
            for cat in all_categories:
                cat_type = cat.get('type', 'unknown')
                if cat_type == 'system' or (hasattr(cat_type, 'value') and cat_type.value == 'system'):
                    system_categories.append(cat)
                elif cat_type == 'custom' or (hasattr(cat_type, 'value') and cat_type.value == 'custom'):
                    custom_categories.append(cat)
                else:
                    system_categories.append(cat)
            
            # 添加系统分类
            for category in system_categories:
                try:
                    display_name = f"{category['icon']} {category['name']}"
                    item = QTreeWidgetItem(self.category_tree, [display_name])
                    item.setData(0, Qt.UserRole, category)
                    
                    # 设置颜色
                    try:
                        color = QColor(category['color'])
                        item.setForeground(0, color)
                    except:
                        pass
                        
                except Exception as e:
                    print(f"❌ 添加系统分类失败: {e}")
            
            # 添加自定义分类组
            if custom_categories:
                try:
                    custom_group = QTreeWidgetItem(self.category_tree, ["📁 自定义分类"])
                    custom_group.setData(0, Qt.UserRole, None)  # 组节点不可选择
                    
                    for category in custom_categories:
                        try:
                            display_name = f"{category['icon']} {category['name']}"
                            item = QTreeWidgetItem(custom_group, [display_name])
                            item.setData(0, Qt.UserRole, category)
                            
                            # 设置颜色
                            try:
                                color = QColor(category['color'])
                                item.setForeground(0, color)
                            except:
                                pass
                                
                        except Exception as e:
                            print(f"❌ 添加自定义分类失败: {e}")
                            
                except Exception as e:
                    print(f"❌ 创建自定义分类组失败: {e}")
            
            # 展开所有项目
            self.category_tree.expandAll()
            
        except Exception as e:
            print(f"❌ 加载分类失败: {e}")
            self._load_default_categories()
    
    def _load_default_categories(self):
        """加载默认分类"""
        default_categories = [
            {"id": "temp", "name": "临时分组", "icon": "📦", "color": "#6c757d"},
            {"id": "images", "name": "图片文件", "icon": "🖼️", "color": "#28a745"},
            {"id": "audio", "name": "音频文件", "icon": "🎵", "color": "#17a2b8"},
            {"id": "video", "name": "视频文件", "icon": "🎬", "color": "#dc3545"},
            {"id": "documents", "name": "文档文件", "icon": "📄", "color": "#ffc107"},
            {"id": "design", "name": "设计文件", "icon": "🎨", "color": "#6f42c1"}
        ]
        
        for category in default_categories:
            display_name = f"{category['icon']} {category['name']}"
            item = QTreeWidgetItem(self.category_tree, [display_name])
            item.setData(0, Qt.UserRole, category)
            
            try:
                color = QColor(category['color'])
                item.setForeground(0, color)
            except:
                pass
    
    def on_category_clicked(self, item, column):
        """分类点击处理"""
        category_data = item.data(0, Qt.UserRole)
        
        if category_data is None:
            # 点击的是组节点，不可选择
            self.selected_category = None
            self.selected_label.setText("请选择具体的分类")
            self.ok_button.setEnabled(False)
            return
        
        self.selected_category = category_data
        self.selected_label.setText(f"已选择: {category_data['icon']} {category_data['name']}")
        self.ok_button.setEnabled(True)
    
    def on_category_double_clicked(self, item, column):
        """分类双击处理"""
        category_data = item.data(0, Qt.UserRole)
        
        if category_data is not None:
            self.selected_category = category_data
            self.accept()
    
    def accept(self):
        """确认选择"""
        if self.selected_category is None:
            QMessageBox.warning(self, "警告", "请先选择一个分类！")
            return
        
        super().accept()
    
    def get_selected_category(self):
        """获取选中的分类"""
        return self.selected_category

def show_category_selection_dialog(parent=None):
    """显示分类选择对话框"""
    dialog = CategorySelectionDialog(parent)
    
    if dialog.exec() == QDialog.Accepted:
        return dialog.get_selected_category()
    
    return None
