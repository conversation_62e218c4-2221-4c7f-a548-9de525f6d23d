#ifndef PERFORMANCE_ENGINE_HPP
#define PERFORMANCE_ENGINE_HPP

#include <vector>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <algorithm>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>

// 文件项目结构
struct FileItem {
    int id;
    std::string name;
    std::string file_type;
    std::string file_path;
    long long size;
    std::string created_time;
    int width;
    int height;
    int rating;
    
    FileItem() = default;
    FileItem(int id, const std::string& name, const std::string& file_type,
             const std::string& file_path, long long size, const std::string& created_time,
             int width, int height, int rating)
        : id(id), name(name), file_type(file_type), file_path(file_path),
          size(size), created_time(created_time), width(width), height(height), rating(rating) {}
};

// 搜索索引引擎
class SearchIndexEngine {
private:
    std::vector<FileItem> data_source_;
    std::unordered_map<std::string, std::unordered_set<int>> word_index_;
    std::unordered_map<std::string, std::unordered_set<int>> type_index_;
    mutable std::mutex index_mutex_;
    
public:
    SearchIndexEngine() = default;
    ~SearchIndexEngine() = default;
    
    // 设置数据源并建立索引
    void setDataSource(const std::vector<FileItem>& data);
    
    // 快速搜索
    std::vector<int> search(const std::string& query, const std::string& file_type_filter = "") const;
    
    // 获取数据项
    const FileItem& getItem(int index) const;
    
    // 获取数据大小
    size_t size() const { return data_source_.size(); }
    
private:
    void buildIndex();
    std::vector<std::string> tokenize(const std::string& text) const;
    std::string toLowerCase(const std::string& str) const;
};

// 高性能排序引擎
class SortEngine {
public:
    enum SortKey {
        NAME,
        SIZE,
        DATE,
        TYPE,
        RATING
    };
    
private:
    mutable std::unordered_map<std::string, std::vector<int>> sort_cache_;
    mutable std::mutex cache_mutex_;
    
public:
    SortEngine() = default;
    ~SortEngine() = default;
    
    // 快速排序
    std::vector<int> sort(const std::vector<FileItem>& data, 
                         const std::vector<int>& indices,
                         SortKey key, bool reverse = false) const;
    
    // 清空缓存
    void clearCache();
    
private:
    std::string generateCacheKey(const std::vector<int>& indices, SortKey key, bool reverse) const;
};

// 虚拟滚动管理器
class VirtualScrollManager {
private:
    std::vector<int> visible_indices_;
    std::unordered_set<int> cached_indices_;
    mutable std::mutex cache_mutex_;
    
    int cache_size_;
    int preload_distance_;
    int current_position_;
    
public:
    VirtualScrollManager(int cache_size = 200, int preload_distance = 50);
    ~VirtualScrollManager() = default;
    
    // 更新可见范围
    void updateVisibleRange(int start_index, int visible_count, int total_count);
    
    // 获取需要加载的项目
    std::vector<int> getItemsToLoad() const;
    
    // 获取需要卸载的项目
    std::vector<int> getItemsToUnload() const;
    
    // 标记项目已加载
    void markItemLoaded(int index);
    
    // 标记项目已卸载
    void markItemUnloaded(int index);
    
    // 获取缓存状态
    bool isItemCached(int index) const;
    
    // 清空缓存
    void clearCache();
};

// 缩略图加载队列
struct ThumbnailRequest {
    int id;
    std::string file_path;
    int priority;
    
    ThumbnailRequest(int id, const std::string& path, int priority = 1)
        : id(id), file_path(path), priority(priority) {}
    
    bool operator<(const ThumbnailRequest& other) const {
        return priority < other.priority; // 优先级队列，数字越大优先级越高
    }
};

class ThumbnailLoadManager {
private:
    std::priority_queue<ThumbnailRequest> request_queue_;
    std::unordered_set<int> processing_ids_;
    std::unordered_set<int> completed_ids_;
    
    mutable std::mutex queue_mutex_;
    mutable std::mutex processing_mutex_;
    std::condition_variable queue_condition_;
    
    std::atomic<bool> running_;
    std::vector<std::thread> worker_threads_;
    int thread_count_;
    
public:
    ThumbnailLoadManager(int thread_count = 4);
    ~ThumbnailLoadManager();
    
    // 请求加载缩略图
    void requestThumbnail(int id, const std::string& file_path, int priority = 1);
    
    // 启动工作线程
    void start();
    
    // 停止工作线程
    void stop();
    
    // 检查是否已完成
    bool isCompleted(int id) const;
    
    // 清空队列
    void clearQueue();
    
private:
    void workerThread();
    bool processThumbnailRequest(const ThumbnailRequest& request);
};

// 主性能引擎
class PerformanceEngine {
private:
    std::unique_ptr<SearchIndexEngine> search_engine_;
    std::unique_ptr<SortEngine> sort_engine_;
    std::unique_ptr<VirtualScrollManager> scroll_manager_;
    std::unique_ptr<ThumbnailLoadManager> thumbnail_manager_;
    
    mutable std::mutex engine_mutex_;
    
public:
    PerformanceEngine();
    ~PerformanceEngine();
    
    // 初始化数据
    void initializeData(const std::vector<FileItem>& data);
    
    // 搜索和过滤
    std::vector<int> searchAndFilter(const std::string& query, 
                                   const std::string& file_type_filter = "") const;
    
    // 排序
    std::vector<int> sortResults(const std::vector<int>& indices, 
                               SortEngine::SortKey key, bool reverse = false) const;
    
    // 虚拟滚动管理
    void updateVisibleRange(int start_index, int visible_count, int total_count);
    std::vector<int> getItemsToLoad() const;
    std::vector<int> getItemsToUnload() const;
    void markItemLoaded(int index);
    
    // 缩略图管理
    void requestThumbnail(int id, const std::string& file_path, int priority = 1);
    bool isThumbnailCompleted(int id) const;
    
    // 获取数据项
    const FileItem& getItem(int index) const;
    
    // 性能统计
    struct PerformanceStats {
        size_t total_items;
        size_t cached_items;
        size_t search_index_size;
        double cache_hit_ratio;
        int active_threads;
    };
    
    PerformanceStats getPerformanceStats() const;
    
    // 清理资源
    void cleanup();
};

// C接口，用于Python调用
extern "C" {
    // 引擎管理
    PerformanceEngine* create_engine();
    void destroy_engine(PerformanceEngine* engine);
    
    // 数据管理
    void engine_initialize_data(PerformanceEngine* engine, 
                               const FileItem* items, int count);
    
    // 搜索和排序
    int* engine_search_and_filter(PerformanceEngine* engine, 
                                 const char* query, const char* file_type_filter,
                                 int* result_count);
    
    int* engine_sort_results(PerformanceEngine* engine,
                           const int* indices, int count,
                           int sort_key, bool reverse,
                           int* result_count);
    
    // 虚拟滚动
    void engine_update_visible_range(PerformanceEngine* engine,
                                   int start_index, int visible_count, int total_count);
    
    int* engine_get_items_to_load(PerformanceEngine* engine, int* count);
    int* engine_get_items_to_unload(PerformanceEngine* engine, int* count);
    void engine_mark_item_loaded(PerformanceEngine* engine, int index);
    
    // 缩略图管理
    void engine_request_thumbnail(PerformanceEngine* engine,
                                int id, const char* file_path, int priority);
    bool engine_is_thumbnail_completed(PerformanceEngine* engine, int id);
    
    // 数据获取
    FileItem* engine_get_item(PerformanceEngine* engine, int index);
    
    // 内存管理
    void free_int_array(int* array);
    void free_file_item(FileItem* item);
}

#endif // PERFORMANCE_ENGINE_HPP
