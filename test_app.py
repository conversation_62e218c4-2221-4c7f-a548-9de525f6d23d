# 测试应用程序启动
# 功能：简单测试程序是否能正常启动，检查基本功能

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")

    try:
        # 测试PySide6导入
        from PySide6.QtWidgets import QApplication, QMainWindow
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QIcon
        print("✓ PySide6导入成功")

        # 测试图像处理库
        from PIL import Image
        print("✓ Pillow导入成功")

        # 测试科学计算库
        import numpy as np
        print("✓ NumPy导入成功")

        # 测试机器学习库
        from sklearn.cluster import KMeans
        print("✓ scikit-learn导入成功")

        # 测试项目模块
        from theme.theme_manager import ThemeManager
        print("✓ 主题管理器导入成功")

        from utils.config_manager import ConfigManager
        print("✓ 配置管理器导入成功")

        from database.db_manager import DatabaseManager
        print("✓ 数据库管理器导入成功")

        from core.file_manager import FileManager
        print("✓ 文件管理器导入成功")

        from core.search_engine import SearchEngine
        print("✓ 搜索引擎导入成功")

        from ai.ai_analyzer import AIAnalyzer
        print("✓ AI分析器导入成功")

        return True

    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")

    try:
        # 测试配置管理器
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        print("✓ 配置管理器创建成功")

        # 测试数据库管理器
        from database.db_manager import DatabaseManager
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✓ 数据库初始化成功")

        # 测试主题管理器
        from theme.theme_manager import ThemeManager
        theme_manager = ThemeManager()
        theme_manager.load_theme("light")
        print("✓ 主题管理器创建成功")

        # 测试文件管理器
        from core.file_manager import FileManager
        file_manager = FileManager(db_manager, config_manager)
        print("✓ 文件管理器创建成功")

        # 测试搜索引擎
        from core.search_engine import SearchEngine
        search_engine = SearchEngine(db_manager, config_manager)
        print("✓ 搜索引擎创建成功")

        # 测试AI分析器
        from ai.ai_analyzer import AIAnalyzer
        ai_analyzer = AIAnalyzer(config_manager)
        print("✓ AI分析器创建成功")

        return True

    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        return False

def test_ui_creation():
    """测试UI创建"""
    print("\n测试UI创建...")

    try:
        from PySide6.QtWidgets import QApplication

        # 创建应用程序实例
        app = QApplication(sys.argv)
        print("✓ QApplication创建成功")

        # 测试主题管理器
        from theme.theme_manager import ThemeManager
        theme_manager = ThemeManager()

        # 测试配置管理器
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()

        # 测试数据库管理器
        from database.db_manager import DatabaseManager
        db_manager = DatabaseManager()
        db_manager.initialize_database()  # 确保数据库已初始化
        print("✓ 数据库初始化成功")

        # 测试主窗口创建
        from ui.main_window import MainWindow
        main_window = MainWindow(theme_manager, db_manager, config_manager)
        print("✓ 主窗口创建成功")

        # 应用主题
        theme_manager.apply_theme_to_widget(main_window)
        print("✓ 主题应用成功")

        # 不显示窗口，只测试创建
        print("✓ UI创建测试完成")

        return True

    except Exception as e:
        print(f"✗ UI创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("智能素材管理器 - 功能测试")
    print("=" * 50)

    # 测试导入
    import_success = test_imports()

    # 测试基本功能
    if import_success:
        func_success = test_basic_functionality()
    else:
        func_success = False

    # 测试UI创建
    if func_success:
        ui_success = test_ui_creation()
    else:
        ui_success = False

    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"模块导入: {'✓ 成功' if import_success else '✗ 失败'}")
    print(f"基本功能: {'✓ 成功' if func_success else '✗ 失败'}")
    print(f"UI创建: {'✓ 成功' if ui_success else '✗ 失败'}")

    if import_success and func_success and ui_success:
        print("\n🎉 所有测试通过！程序可以正常运行。")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
