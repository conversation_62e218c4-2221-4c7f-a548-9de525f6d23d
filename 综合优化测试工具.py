#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合优化测试工具
测试所有优化功能：启动优化、智能缓存、性能监控、智能预加载等
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                               QTabWidget, QProgressBar, QSplitter, QFrame,
                               QGroupBox, QGridLayout, QCheckBox, QSpinBox)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QColor, QPalette

class OptimizationTestSuite(QObject):
    """优化测试套件"""
    
    test_completed = Signal(str, dict)
    progress_updated = Signal(int, str)
    
    def __init__(self):
        super().__init__()
        self.test_results = {}
    
    def run_all_tests(self):
        """运行所有测试"""
        tests = [
            ("启动优化测试", self.test_startup_optimization),
            ("智能缓存测试", self.test_intelligent_cache),
            ("性能监控测试", self.test_performance_monitor),
            ("智能预加载测试", self.test_smart_preloader),
            ("C++引擎测试", self.test_cpp_engine),
            ("综合性能测试", self.test_overall_performance)
        ]
        
        total_tests = len(tests)
        
        for i, (test_name, test_func) in enumerate(tests):
            self.progress_updated.emit(int((i / total_tests) * 100), f"正在执行: {test_name}")
            
            try:
                result = test_func()
                self.test_results[test_name] = result
                self.test_completed.emit(test_name, result)
            except Exception as e:
                error_result = {"success": False, "error": str(e)}
                self.test_results[test_name] = error_result
                self.test_completed.emit(test_name, error_result)
        
        self.progress_updated.emit(100, "所有测试完成")
    
    def test_startup_optimization(self):
        """测试启动优化"""
        try:
            from core.startup_optimizer import get_startup_optimizer
            
            optimizer = get_startup_optimizer()
            
            # 测试启动画面创建
            splash = optimizer.create_splash_screen()
            
            # 测试预加载管理器
            preload_manager = optimizer.preload_manager
            preload_manager.preload_common_data()
            
            completed, failed = preload_manager.wait_for_completion(timeout=2.0)
            
            return {
                "success": True,
                "splash_created": splash is not None,
                "preload_completed": len(completed),
                "preload_failed": len(failed),
                "cache_items": len(preload_manager.cache)
            }
            
        except ImportError:
            return {"success": False, "error": "启动优化模块不可用"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_intelligent_cache(self):
        """测试智能缓存"""
        try:
            from core.intelligent_cache import get_cache_manager
            
            cache_manager = get_cache_manager()
            
            # 测试缓存操作
            test_data = {"test_key": "test_value", "timestamp": time.time()}
            
            # 写入缓存
            cache_manager.put("test_item", test_data, "test_category")
            
            # 读取缓存
            cached_data = cache_manager.get("test_item", "test_category")
            
            # 获取统计信息
            stats = cache_manager.get_stats()
            
            return {
                "success": True,
                "cache_write_success": True,
                "cache_read_success": cached_data is not None,
                "data_matches": cached_data == test_data if cached_data else False,
                "stats": stats
            }
            
        except ImportError:
            return {"success": False, "error": "智能缓存模块不可用"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_performance_monitor(self):
        """测试性能监控"""
        try:
            from core.performance_monitor import get_performance_monitor
            
            monitor = get_performance_monitor()
            
            # 收集指标
            metrics = monitor.collector.collect_all_metrics()
            
            # 生成报告
            report = monitor._generate_performance_report(metrics)
            
            # 获取趋势数据
            cpu_trend = monitor.get_metric_trend("system.cpu_usage", duration=60)
            
            return {
                "success": True,
                "metrics_collected": len(metrics),
                "report_generated": report is not None,
                "trend_data_available": len(cpu_trend) > 0,
                "categories": list(metrics.keys()),
                "alert_count": len(report.get('alerts', []) if report else [])
            }
            
        except ImportError:
            return {"success": False, "error": "性能监控模块不可用"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_smart_preloader(self):
        """测试智能预加载"""
        try:
            from core.smart_preloader import get_smart_preloader
            
            preloader = get_smart_preloader()
            
            # 注册测试预加载器
            def test_preloader(target):
                time.sleep(0.1)  # 模拟加载时间
                return f"preloaded_{target}"
            
            preloader.register_preloader('test_resource', test_preloader)
            
            # 记录用户行为
            preloader.record_user_action('view', 'test_file.jpg', {'file_type': 'image'})
            preloader.record_user_action('search', 'test_query', {'query': 'test'})
            
            # 获取预测
            predictions = preloader.behavior_analyzer.predict_next_actions('view', {'file_type': 'image'})
            
            # 获取统计信息
            stats = preloader.get_preload_stats()
            
            return {
                "success": True,
                "preloader_registered": True,
                "behavior_recorded": True,
                "predictions_count": len(predictions),
                "stats": stats
            }
            
        except ImportError:
            return {"success": False, "error": "智能预加载模块不可用"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_cpp_engine(self):
        """测试C++引擎"""
        try:
            from core_engine.python_bindings import get_performance_engine
            
            engine = get_performance_engine()
            
            if not engine.is_available():
                return {"success": False, "error": "C++引擎不可用"}
            
            # 测试数据初始化
            test_data = [
                {'id': i, 'name': f'test_file_{i}.jpg', 'file_type': 'image', 'size': 1024 * i}
                for i in range(1000)
            ]
            
            init_success = engine.initialize_data(test_data)
            
            # 测试搜索
            search_results = engine.search_and_filter("test", "image")
            
            # 测试排序
            sort_results = engine.sort_results(list(range(100)), engine.SORT_NAME, False)
            
            return {
                "success": True,
                "engine_available": True,
                "init_success": init_success,
                "search_results_count": len(search_results),
                "sort_results_count": len(sort_results)
            }
            
        except ImportError:
            return {"success": False, "error": "C++引擎模块不可用"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_overall_performance(self):
        """测试整体性能"""
        try:
            # 内存使用测试
            import psutil
            process = psutil.Process()
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            
            # 执行一些操作
            test_data = list(range(10000))
            processed_data = [x * 2 for x in test_data]
            
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_usage = memory_after - memory_before
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 线程数
            thread_count = threading.active_count()
            
            return {
                "success": True,
                "memory_usage_mb": memory_usage,
                "cpu_percent": cpu_percent,
                "thread_count": thread_count,
                "data_processing_success": len(processed_data) == len(test_data)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

class OptimizationTestWindow(QMainWindow):
    """优化测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("智能素材管理器 - 综合优化测试工具")
        self.setGeometry(100, 100, 1200, 800)
        
        self.test_suite = OptimizationTestSuite()
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title = QLabel("🧪 智能素材管理器 - 综合优化测试")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧控制面板
        self.create_control_panel(splitter)
        
        # 右侧结果面板
        self.create_results_panel(splitter)
        
        splitter.setSizes([300, 900])
        layout.addWidget(splitter)
        
        # 状态栏
        self.create_status_bar(layout)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.StyledPanel)
        control_frame.setMaximumWidth(350)
        
        layout = QVBoxLayout(control_frame)
        
        # 测试控制
        test_group = QGroupBox("🎯 测试控制")
        test_layout = QVBoxLayout(test_group)
        
        self.run_all_btn = QPushButton("🚀 运行所有测试")
        self.run_all_btn.clicked.connect(self.run_all_tests)
        test_layout.addWidget(self.run_all_btn)
        
        self.run_startup_btn = QPushButton("⚡ 启动优化测试")
        self.run_startup_btn.clicked.connect(lambda: self.run_single_test("启动优化测试"))
        test_layout.addWidget(self.run_startup_btn)
        
        self.run_cache_btn = QPushButton("🧠 智能缓存测试")
        self.run_cache_btn.clicked.connect(lambda: self.run_single_test("智能缓存测试"))
        test_layout.addWidget(self.run_cache_btn)
        
        self.run_monitor_btn = QPushButton("📊 性能监控测试")
        self.run_monitor_btn.clicked.connect(lambda: self.run_single_test("性能监控测试"))
        test_layout.addWidget(self.run_monitor_btn)
        
        self.run_preload_btn = QPushButton("🎯 智能预加载测试")
        self.run_preload_btn.clicked.connect(lambda: self.run_single_test("智能预加载测试"))
        test_layout.addWidget(self.run_preload_btn)
        
        self.run_cpp_btn = QPushButton("⚡ C++引擎测试")
        self.run_cpp_btn.clicked.connect(lambda: self.run_single_test("C++引擎测试"))
        test_layout.addWidget(self.run_cpp_btn)
        
        layout.addWidget(test_group)
        
        # 测试选项
        options_group = QGroupBox("⚙️ 测试选项")
        options_layout = QVBoxLayout(options_group)
        
        self.detailed_output = QCheckBox("详细输出")
        self.detailed_output.setChecked(True)
        options_layout.addWidget(self.detailed_output)
        
        self.auto_cleanup = QCheckBox("自动清理")
        self.auto_cleanup.setChecked(True)
        options_layout.addWidget(self.auto_cleanup)
        
        layout.addWidget(options_group)
        
        # 进度显示
        progress_group = QGroupBox("📈 测试进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("等待测试...")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        layout.addStretch()
        parent.addWidget(control_frame)
    
    def create_results_panel(self, parent):
        """创建结果面板"""
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 测试结果标签页
        self.results_tab = QWidget()
        results_layout = QVBoxLayout(self.results_tab)
        
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setFont(QFont("Consolas", 9))
        results_layout.addWidget(self.results_text)
        
        self.tab_widget.addTab(self.results_tab, "📋 测试结果")
        
        # 性能报告标签页
        self.performance_tab = QWidget()
        performance_layout = QVBoxLayout(self.performance_tab)
        
        self.performance_text = QTextEdit()
        self.performance_text.setReadOnly(True)
        self.performance_text.setFont(QFont("Consolas", 9))
        performance_layout.addWidget(self.performance_text)
        
        self.tab_widget.addTab(self.performance_tab, "📊 性能报告")
        
        # 系统信息标签页
        self.system_tab = QWidget()
        system_layout = QVBoxLayout(self.system_tab)
        
        self.system_text = QTextEdit()
        self.system_text.setReadOnly(True)
        self.system_text.setFont(QFont("Consolas", 9))
        system_layout.addWidget(self.system_text)
        
        self.tab_widget.addTab(self.system_tab, "💻 系统信息")
        
        parent.addWidget(self.tab_widget)
        
        # 初始化系统信息
        self.update_system_info()
    
    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setMaximumHeight(40)
        
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 清理按钮
        clear_btn = QPushButton("🗑️ 清空结果")
        clear_btn.clicked.connect(self.clear_results)
        status_layout.addWidget(clear_btn)
        
        # 导出按钮
        export_btn = QPushButton("📤 导出报告")
        export_btn.clicked.connect(self.export_report)
        status_layout.addWidget(export_btn)
        
        parent_layout.addWidget(status_frame)
    
    def setup_connections(self):
        """设置信号连接"""
        self.test_suite.test_completed.connect(self.on_test_completed)
        self.test_suite.progress_updated.connect(self.on_progress_updated)
    
    def run_all_tests(self):
        """运行所有测试"""
        self.results_text.clear()
        self.performance_text.clear()
        
        self.results_text.append("🧪 开始综合优化测试...\n")
        
        # 在新线程中运行测试
        self.test_thread = QThread()
        self.test_suite.moveToThread(self.test_thread)
        
        self.test_thread.started.connect(self.test_suite.run_all_tests)
        self.test_thread.start()
        
        self.run_all_btn.setEnabled(False)
    
    def run_single_test(self, test_name):
        """运行单个测试"""
        self.results_text.append(f"\n🔍 运行单项测试: {test_name}")
        
        # 根据测试名称调用相应的测试方法
        test_methods = {
            "启动优化测试": self.test_suite.test_startup_optimization,
            "智能缓存测试": self.test_suite.test_intelligent_cache,
            "性能监控测试": self.test_suite.test_performance_monitor,
            "智能预加载测试": self.test_suite.test_smart_preloader,
            "C++引擎测试": self.test_suite.test_cpp_engine
        }
        
        if test_name in test_methods:
            try:
                result = test_methods[test_name]()
                self.on_test_completed(test_name, result)
            except Exception as e:
                self.on_test_completed(test_name, {"success": False, "error": str(e)})
    
    def on_test_completed(self, test_name, result):
        """测试完成回调"""
        success = result.get("success", False)
        status_icon = "✅" if success else "❌"
        
        self.results_text.append(f"\n{status_icon} {test_name}:")
        
        if success:
            self.results_text.append("  状态: 通过")
            
            # 显示详细结果
            if self.detailed_output.isChecked():
                for key, value in result.items():
                    if key != "success":
                        self.results_text.append(f"  • {key}: {value}")
        else:
            self.results_text.append(f"  状态: 失败")
            self.results_text.append(f"  错误: {result.get('error', 'Unknown error')}")
        
        # 更新性能报告
        self.update_performance_report(test_name, result)
    
    def on_progress_updated(self, progress, message):
        """进度更新回调"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(message)
        self.status_label.setText(message)
        
        if progress >= 100:
            self.run_all_btn.setEnabled(True)
            self.status_label.setText("测试完成")
    
    def update_performance_report(self, test_name, result):
        """更新性能报告"""
        if result.get("success", False):
            self.performance_text.append(f"\n📊 {test_name} 性能数据:")
            
            # 提取性能相关数据
            if "stats" in result:
                stats = result["stats"]
                for key, value in stats.items():
                    self.performance_text.append(f"  • {key}: {value}")
            
            # 其他性能指标
            performance_keys = ["memory_usage_mb", "cpu_percent", "thread_count", 
                              "search_results_count", "cache_items"]
            
            for key in performance_keys:
                if key in result:
                    self.performance_text.append(f"  • {key}: {result[key]}")
    
    def update_system_info(self):
        """更新系统信息"""
        try:
            import psutil
            import platform
            
            system_info = f"""
💻 系统信息:
  • 操作系统: {platform.system()} {platform.release()}
  • Python版本: {platform.python_version()}
  • CPU核心数: {psutil.cpu_count()}
  • 内存总量: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f} GB
  • 磁盘使用: {psutil.disk_usage('/').percent:.1f}%

🔧 优化模块状态:
            """
            
            # 检查优化模块
            modules = [
                ("启动优化器", "core.startup_optimizer"),
                ("智能缓存", "core.intelligent_cache"),
                ("性能监控", "core.performance_monitor"),
                ("智能预加载", "core.smart_preloader"),
                ("C++引擎", "core_engine.python_bindings")
            ]
            
            for name, module in modules:
                try:
                    __import__(module)
                    system_info += f"  • {name}: ✅ 可用\n"
                except ImportError:
                    system_info += f"  • {name}: ❌ 不可用\n"
            
            self.system_text.setPlainText(system_info)
            
        except Exception as e:
            self.system_text.setPlainText(f"获取系统信息失败: {e}")
    
    def clear_results(self):
        """清空结果"""
        self.results_text.clear()
        self.performance_text.clear()
        self.progress_bar.setValue(0)
        self.progress_label.setText("等待测试...")
        self.status_label.setText("就绪")
    
    def export_report(self):
        """导出报告"""
        from PySide6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出测试报告", "optimization_test_report.txt", "Text Files (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("智能素材管理器 - 综合优化测试报告\n")
                    f.write("=" * 50 + "\n\n")
                    
                    f.write("测试结果:\n")
                    f.write(self.results_text.toPlainText())
                    f.write("\n\n")
                    
                    f.write("性能报告:\n")
                    f.write(self.performance_text.toPlainText())
                    f.write("\n\n")
                    
                    f.write("系统信息:\n")
                    f.write(self.system_text.toPlainText())
                
                self.status_label.setText(f"报告已导出: {file_path}")
                
            except Exception as e:
                self.status_label.setText(f"导出失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = OptimizationTestWindow()
    window.show()
    
    print("综合优化测试工具启动成功！")
    print("功能特性：")
    print("1. 🧪 全面测试所有优化模块")
    print("2. 📊 详细的性能分析报告")
    print("3. 💻 系统信息和兼容性检查")
    print("4. 📤 测试报告导出功能")
    print("5. 🎯 单项测试和综合测试")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
