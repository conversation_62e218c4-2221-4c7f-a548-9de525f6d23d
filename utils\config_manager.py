# 配置管理模块
# 功能：管理应用程序配置，包括用户设置、主题选择、窗口状态等持久化配置

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional
from PySide6.QtCore import QSettings, QStandardPaths

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self):
        self.app_name = "SmartAssetManager"
        self.config_dir = Path(QStandardPaths.writableLocation(QStandardPaths.AppDataLocation))
        self.config_file = self.config_dir / "config.json"
        self.settings = QSettings(self.app_name, "Settings")
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "theme": "light",
            "language": "zh_CN",
            "window": {
                "width": 1200,
                "height": 800,
                "maximized": False,
                "position": {"x": 100, "y": 100}
            },
            "ui": {
                "sidebar_width": 250,
                "preview_panel_visible": True,
                "preview_panel_width": 300,
                "thumbnail_size": 150,
                "view_mode": "grid"
            },
            "file_management": {
                "auto_scan": True,
                "scan_interval": 300,  # 秒
                "generate_thumbnails": True,
                "thumbnail_quality": 85,
                "max_thumbnail_size": 300,
                "supported_formats": [
                    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp",
                    ".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv",
                    ".mp3", ".wav", ".flac", ".aac", ".ogg",
                    ".pdf", ".psd", ".ai", ".eps", ".svg"
                ]
            },
            "ai": {
                "auto_tag": True,
                "face_detection": True,
                "object_detection": True,
                "color_analysis": True,
                "duplicate_detection": True,
                "similarity_threshold": 0.8
            },
            "search": {
                "search_history_limit": 50,
                "auto_complete": True,
                "fuzzy_search": True,
                "search_in_tags": True,
                "search_in_filename": True,
                "search_in_metadata": True
            },
            "performance": {
                "cache_size_mb": 500,
                "max_concurrent_tasks": 4,
                "preload_thumbnails": True,
                "virtual_scrolling": True,
                "animation_enabled": True
            },
            "backup": {
                "auto_backup": True,
                "backup_interval_days": 7,
                "max_backup_files": 5,
                "backup_location": ""
            }
        }
        
        # 加载配置
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并默认配置和加载的配置
                    return self._merge_config(self.default_config, loaded_config)
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
            
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            
    def _merge_config(self, default: Dict, loaded: Dict) -> Dict:
        """合并配置，确保所有默认键都存在"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
        
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any):
        """设置配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # 设置值
        config[keys[-1]] = value
        
    def get_theme(self) -> str:
        """获取主题设置"""
        return self.get("theme", "light")
        
    def set_theme(self, theme: str):
        """设置主题"""
        self.set("theme", theme)
        
    def get_window_config(self) -> Dict[str, Any]:
        """获取窗口配置"""
        return self.get("window", self.default_config["window"])
        
    def set_window_config(self, config: Dict[str, Any]):
        """设置窗口配置"""
        self.set("window", config)
        
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return self.get("ui", self.default_config["ui"])
        
    def set_ui_config(self, config: Dict[str, Any]):
        """设置UI配置"""
        self.set("ui", config)
        
    def get_file_management_config(self) -> Dict[str, Any]:
        """获取文件管理配置"""
        return self.get("file_management", self.default_config["file_management"])
        
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return self.get("ai", self.default_config["ai"])
        
    def get_search_config(self) -> Dict[str, Any]:
        """获取搜索配置"""
        return self.get("search", self.default_config["search"])
        
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self.get("performance", self.default_config["performance"])
        
    def get_backup_config(self) -> Dict[str, Any]:
        """获取备份配置"""
        return self.get("backup", self.default_config["backup"])
        
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
        
    def export_config(self, file_path: str) -> bool:
        """导出配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
            
    def import_config(self, file_path: str) -> bool:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
                self.config = self._merge_config(self.default_config, imported_config)
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
            
    def get_cache_dir(self) -> Path:
        """获取缓存目录"""
        cache_dir = self.config_dir / "cache"
        cache_dir.mkdir(exist_ok=True)
        return cache_dir
        
    def get_thumbnails_dir(self) -> Path:
        """获取缩略图目录"""
        thumbnails_dir = self.get_cache_dir() / "thumbnails"
        thumbnails_dir.mkdir(exist_ok=True)
        return thumbnails_dir
        
    def get_database_path(self) -> Path:
        """获取数据库文件路径"""
        return self.config_dir / "database.db"
        
    def get_logs_dir(self) -> Path:
        """获取日志目录"""
        logs_dir = self.config_dir / "logs"
        logs_dir.mkdir(exist_ok=True)
        return logs_dir
