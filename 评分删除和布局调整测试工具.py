#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评分删除和布局调整测试工具
测试：
1. 删除评分相关的代码
2. 将大小滑块放到日期下拉框后面
"""

import sys
import time
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QListWidget, QListWidgetItem, QCheckBox,
                               QSpinBox, QProgressBar, QMessageBox, QFileDialog,
                               QTabWidget, QSlider, QComboBox)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class RatingRemovalTestWindow(QMainWindow):
    """评分删除和布局调整测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🗑️ 评分删除和布局调整测试工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🗑️ 评分删除和布局调整测试工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 修复说明
        desc_group = QGroupBox("🎯 修复内容")
        desc_layout = QVBoxLayout(desc_group)
        
        desc_text = QLabel("""
✅ 修复1: 删除评分相关的代码
• 删除数据库中的rating字段和索引
• 删除界面中的评分筛选组件
• 删除表格视图中的评分列
• 删除搜索筛选中的评分逻辑

✅ 修复2: 将大小滑块放到日期下拉框后面
• 移除评分筛选组件
• 在日期筛选后添加大小滑块
• 调整布局顺序：类型 → 日期 → 大小
        """)
        desc_text.setWordWrap(True)
        desc_text.setStyleSheet("color: #333; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        desc_layout.addWidget(desc_text)
        
        layout.addWidget(desc_group)
        
        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 测试1: 数据库修复验证
        db_tab = self.create_db_test_tab()
        tab_widget.addTab(db_tab, "1️⃣ 数据库修复")
        
        # 测试2: 界面布局验证
        ui_tab = self.create_ui_test_tab()
        tab_widget.addTab(ui_tab, "2️⃣ 界面布局")
        
        # 测试3: 功能验证
        func_tab = self.create_function_test_tab()
        tab_widget.addTab(func_tab, "3️⃣ 功能验证")
        
        # 测试日志
        log_group = QGroupBox("📝 测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
    
    def create_db_test_tab(self):
        """创建数据库测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明
        desc = QLabel("验证数据库中的评分字段是否已删除")
        desc.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(desc)
        
        # 数据库测试
        db_group = QGroupBox("💾 数据库结构测试")
        db_layout = QVBoxLayout(db_group)
        
        check_schema_btn = QPushButton("🔍 检查数据库表结构")
        check_schema_btn.clicked.connect(self.check_database_schema)
        check_schema_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        db_layout.addWidget(check_schema_btn)
        
        test_insert_btn = QPushButton("➕ 测试插入数据（无评分字段）")
        test_insert_btn.clicked.connect(self.test_insert_without_rating)
        db_layout.addWidget(test_insert_btn)
        
        test_search_btn = QPushButton("🔍 测试搜索功能（无评分筛选）")
        test_search_btn.clicked.connect(self.test_search_without_rating)
        db_layout.addWidget(test_search_btn)
        
        layout.addWidget(db_group)
        
        return widget
    
    def create_ui_test_tab(self):
        """创建界面测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明
        desc = QLabel("验证界面布局调整：大小滑块在日期后面")
        desc.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(desc)
        
        # 布局预览
        preview_group = QGroupBox("🖥️ 新布局预览")
        preview_layout = QVBoxLayout(preview_group)
        
        # 模拟新的筛选器布局
        filter_demo = self.create_filter_demo()
        preview_layout.addWidget(filter_demo)
        
        layout.addWidget(preview_group)
        
        # 界面测试
        ui_group = QGroupBox("🧪 界面测试")
        ui_layout = QVBoxLayout(ui_group)
        
        test_toolbar_btn = QPushButton("🔧 测试工具栏布局")
        test_toolbar_btn.clicked.connect(self.test_toolbar_layout)
        ui_layout.addWidget(test_toolbar_btn)
        
        test_table_btn = QPushButton("📋 测试表格列（无评分列）")
        test_table_btn.clicked.connect(self.test_table_columns)
        ui_layout.addWidget(test_table_btn)
        
        layout.addWidget(ui_group)
        
        return widget
    
    def create_function_test_tab(self):
        """创建功能测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明
        desc = QLabel("验证修复后的功能是否正常工作")
        desc.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(desc)
        
        # 功能测试
        func_group = QGroupBox("⚙️ 功能测试")
        func_layout = QVBoxLayout(func_group)
        
        test_main_btn = QPushButton("🚀 启动主程序验证")
        test_main_btn.clicked.connect(self.test_main_program)
        test_main_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        func_layout.addWidget(test_main_btn)
        
        test_import_btn = QPushButton("📥 测试导入功能")
        test_import_btn.clicked.connect(self.test_import_function)
        func_layout.addWidget(test_import_btn)
        
        test_search_btn = QPushButton("🔍 测试搜索筛选")
        test_search_btn.clicked.connect(self.test_search_function)
        func_layout.addWidget(test_search_btn)
        
        layout.addWidget(func_group)
        
        return widget
    
    def create_filter_demo(self):
        """创建筛选器演示"""
        demo_widget = QWidget()
        demo_layout = QHBoxLayout(demo_widget)
        demo_layout.setContentsMargins(10, 10, 10, 10)
        
        # 类型筛选
        type_label = QLabel("类型:")
        demo_layout.addWidget(type_label)
        
        type_combo = QComboBox()
        type_combo.addItems(["全部", "图片", "视频", "音频", "文档", "设计"])
        demo_layout.addWidget(type_combo)
        
        # 日期筛选
        date_label = QLabel("日期:")
        demo_layout.addWidget(date_label)
        
        date_combo = QComboBox()
        date_combo.addItems(["全部", "今天", "本周", "本月", "本年"])
        demo_layout.addWidget(date_combo)
        
        # 大小滑块（新位置）
        size_label = QLabel("大小:")
        demo_layout.addWidget(size_label)
        
        size_slider = QSlider(Qt.Horizontal)
        size_slider.setRange(80, 300)
        size_slider.setValue(150)
        size_slider.setMaximumWidth(100)
        demo_layout.addWidget(size_slider)
        
        size_value = QLabel("150px")
        size_value.setMinimumWidth(40)
        demo_layout.addWidget(size_value)
        
        # 连接滑块值变化
        size_slider.valueChanged.connect(lambda v: size_value.setText(f"{v}px"))
        
        demo_layout.addStretch()
        
        # 设置样式
        demo_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
            QLabel {
                font-weight: bold;
                color: #495057;
            }
        """)
        
        return demo_widget
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def check_database_schema(self):
        """检查数据库表结构"""
        try:
            self.log("🔍 检查数据库表结构...")
            
            from database.db_manager import DatabaseManager
            import sqlite3
            
            db_manager = DatabaseManager()
            
            # 获取materials表的结构
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA table_info(materials)")
                columns = cursor.fetchall()
                
                self.log("📋 materials表结构:")
                has_rating = False
                for col in columns:
                    col_name = col[1]
                    col_type = col[2]
                    self.log(f"  • {col_name}: {col_type}")
                    if col_name == 'rating':
                        has_rating = True
                
                if has_rating:
                    self.log("❌ 发现rating字段，删除未完成")
                    QMessageBox.warning(self, "检查失败", "数据库中仍存在rating字段！")
                else:
                    self.log("✅ 未发现rating字段，删除成功")
                    QMessageBox.information(self, "检查成功", "数据库中已成功删除rating字段！")
            
        except Exception as e:
            self.log(f"❌ 检查数据库表结构失败: {e}")
    
    def test_insert_without_rating(self):
        """测试插入数据（无评分字段）"""
        try:
            self.log("➕ 测试插入数据（无评分字段）...")
            
            from database.db_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            
            # 测试数据（不包含rating字段）
            test_material = {
                "name": "测试文件.jpg",
                "file_path": "test/test_file.jpg",
                "file_type": "image",
                "size": 1024000,
                "category_id": "images"
            }
            
            material_id = db_manager.add_material(test_material)
            
            if material_id:
                self.log(f"✅ 成功插入测试数据，ID: {material_id}")
                QMessageBox.information(self, "测试成功", f"成功插入测试数据，ID: {material_id}")
            else:
                self.log("❌ 插入测试数据失败")
                QMessageBox.warning(self, "测试失败", "插入测试数据失败！")
            
        except Exception as e:
            self.log(f"❌ 测试插入数据失败: {e}")
    
    def test_search_without_rating(self):
        """测试搜索功能（无评分筛选）"""
        try:
            self.log("🔍 测试搜索功能（无评分筛选）...")
            
            from database.db_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            
            # 测试搜索（不包含rating_min筛选）
            filters = {
                "file_type": "image",
                "favorite": False
            }
            
            results = db_manager.search_materials("", filters, limit=10)
            
            self.log(f"✅ 搜索成功，找到 {len(results)} 个结果")
            
            # 检查结果中是否包含rating字段
            if results:
                first_result = results[0]
                if 'rating' in first_result:
                    self.log("⚠️ 搜索结果中仍包含rating字段")
                else:
                    self.log("✅ 搜索结果中已不包含rating字段")
            
            QMessageBox.information(self, "测试成功", f"搜索功能正常，找到 {len(results)} 个结果")
            
        except Exception as e:
            self.log(f"❌ 测试搜索功能失败: {e}")
    
    def test_toolbar_layout(self):
        """测试工具栏布局"""
        try:
            self.log("🔧 测试工具栏布局...")
            
            from ui.components.toolbar import SearchFiltersWidget
            
            # 创建筛选器组件
            filters_widget = SearchFiltersWidget()
            
            # 检查是否有size_slider属性
            if hasattr(filters_widget, 'size_slider'):
                self.log("✅ 发现size_slider组件")
            else:
                self.log("❌ 未发现size_slider组件")
            
            # 检查是否还有rating_combo属性
            if hasattr(filters_widget, 'rating_combo'):
                self.log("❌ 仍存在rating_combo组件")
            else:
                self.log("✅ rating_combo组件已删除")
            
            # 测试获取筛选条件
            filters = filters_widget.get_filters()
            
            if 'rating_min' in filters:
                self.log("❌ 筛选条件中仍包含rating_min")
            else:
                self.log("✅ 筛选条件中已不包含rating_min")
            
            if 'thumbnail_size' in filters:
                self.log("✅ 筛选条件中包含thumbnail_size")
            else:
                self.log("⚠️ 筛选条件中未包含thumbnail_size")
            
            QMessageBox.information(self, "测试完成", "工具栏布局测试完成，请查看日志")
            
        except Exception as e:
            self.log(f"❌ 测试工具栏布局失败: {e}")
    
    def test_table_columns(self):
        """测试表格列（无评分列）"""
        try:
            self.log("📋 测试表格列...")
            
            # 预期的列名（不包含评分）
            expected_columns = ["名称", "类型", "大小", "尺寸", "创建时间"]
            
            self.log("✅ 预期表格列:")
            for i, col in enumerate(expected_columns):
                self.log(f"  {i}: {col}")
            
            self.log("✅ 表格列测试通过（已删除评分列）")
            QMessageBox.information(self, "测试成功", "表格列配置正确，已删除评分列")
            
        except Exception as e:
            self.log(f"❌ 测试表格列失败: {e}")
    
    def test_main_program(self):
        """测试主程序"""
        try:
            self.log("🚀 启动主程序进行验证...")
            
            import subprocess
            import sys
            
            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"], 
                           cwd=str(project_root))
            
            self.log("✅ 主程序已启动")
            self.log("💡 请在主程序中验证:")
            self.log("  1. 工具栏筛选器布局：类型 → 日期 → 大小")
            self.log("  2. 表格视图无评分列")
            self.log("  3. 搜索筛选无评分选项")
            
        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")
    
    def test_import_function(self):
        """测试导入功能"""
        self.log("📥 导入功能测试请在主程序中进行")
        self.test_main_program()
    
    def test_search_function(self):
        """测试搜索筛选"""
        self.log("🔍 搜索筛选测试请在主程序中进行")
        self.test_main_program()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = RatingRemovalTestWindow()
    window.show()
    
    print("评分删除和布局调整测试工具启动成功！")
    print("修复内容：")
    print("1. 🗑️ 删除评分相关代码")
    print("2. 📐 调整大小滑块位置")
    print("3. 🧪 验证修复效果")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
