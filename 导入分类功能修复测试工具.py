#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入分类功能修复测试工具
测试修复后的导入分类选择功能
"""

import sys
import time
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QListWidget, QListWidgetItem, QCheckBox,
                               QSpinBox, QProgressBar, QMessageBox, QFileDialog)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class ImportCategoryFixTestWindow(QMainWindow):
    """导入分类功能修复测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 导入分类功能修复测试工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.test_files = []
        self.setup_ui()
        self.create_test_files()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔧 导入分类功能修复测试工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 修复说明
        desc_group = QGroupBox("🔧 修复内容")
        desc_layout = QVBoxLayout(desc_group)
        
        desc_text = QLabel("""
🎯 修复的问题：
• ❌ 错误: 'FileManager' object has no attribute 'import_files_with_category'

🔧 修复方案：
• ✅ 为FileManager添加import_files_with_category方法
• ✅ 创建CategoryFileImportThread类处理带分类的导入
• ✅ 支持自动分类功能
• ✅ 支持重复文件检查

🧪 测试内容：
• 测试分类选择对话框
• 测试导入到指定分类
• 测试自动分类功能
• 测试重复文件处理
        """)
        desc_text.setWordWrap(True)
        desc_text.setStyleSheet("color: #333; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        desc_layout.addWidget(desc_text)
        
        layout.addWidget(desc_group)
        
        # 测试控制面板
        control_group = QGroupBox("🎛️ 测试控制")
        control_layout = QVBoxLayout(control_group)
        
        # 文件管理按钮
        file_layout = QHBoxLayout()
        
        create_files_btn = QPushButton("📁 创建测试文件")
        create_files_btn.clicked.connect(self.create_test_files)
        create_files_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        file_layout.addWidget(create_files_btn)
        
        select_files_btn = QPushButton("📂 选择测试文件")
        select_files_btn.clicked.connect(self.select_test_files)
        file_layout.addWidget(select_files_btn)
        
        file_layout.addStretch()
        control_layout.addLayout(file_layout)
        
        # 测试功能按钮
        test_layout = QHBoxLayout()
        
        test_dialog_btn = QPushButton("🗂️ 测试分类对话框")
        test_dialog_btn.clicked.connect(self.test_category_dialog)
        test_dialog_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        test_layout.addWidget(test_dialog_btn)
        
        test_import_btn = QPushButton("📥 测试完整导入流程")
        test_import_btn.clicked.connect(self.test_full_import)
        test_import_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        test_layout.addWidget(test_import_btn)
        
        open_main_btn = QPushButton("🚀 打开主程序")
        open_main_btn.clicked.connect(self.open_main_app)
        open_main_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        test_layout.addWidget(open_main_btn)
        
        test_layout.addStretch()
        control_layout.addLayout(test_layout)
        
        layout.addWidget(control_group)
        
        # 测试文件列表
        file_group = QGroupBox("📄 测试文件列表")
        file_layout = QVBoxLayout(file_group)
        
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(150)
        file_layout.addWidget(self.file_list)
        
        layout.addWidget(file_group)
        
        # 测试日志
        log_group = QGroupBox("📝 测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def create_test_files(self):
        """创建测试文件"""
        try:
            self.log("📁 创建测试文件...")
            
            test_dir = Path("test_import_files")
            test_dir.mkdir(exist_ok=True)
            
            # 创建不同类型的测试文件
            test_files_info = [
                ("test_image1.jpg", "图片文件1", "这是一个测试图片文件"),
                ("test_image2.png", "图片文件2", "这是另一个测试图片文件"),
                ("test_audio1.mp3", "音频文件1", "这是一个测试音频文件"),
                ("test_audio2.wav", "音频文件2", "这是另一个测试音频文件"),
                ("test_document1.pdf", "文档文件1", "这是一个测试PDF文档"),
                ("test_document2.docx", "文档文件2", "这是一个测试Word文档"),
                ("test_design1.psd", "设计文件1", "这是一个测试PSD设计文件"),
                ("test_video1.mp4", "视频文件1", "这是一个测试视频文件")
            ]
            
            created_files = []
            for filename, description, content in test_files_info:
                file_path = test_dir / filename
                
                # 创建测试文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"测试文件: {description}\n")
                    f.write(f"内容: {content}\n")
                    f.write(f"创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"文件大小: {len(content)} 字符\n")
                
                created_files.append(str(file_path))
            
            self.test_files = created_files
            self.update_file_list()
            
            self.log(f"✅ 已创建 {len(created_files)} 个测试文件")
            
        except Exception as e:
            self.log(f"❌ 创建测试文件失败: {e}")
    
    def select_test_files(self):
        """选择测试文件"""
        try:
            files, _ = QFileDialog.getOpenFileNames(
                self, "选择测试文件", "", 
                "所有文件 (*.*);;图片文件 (*.jpg *.png *.gif);;音频文件 (*.mp3 *.wav);;文档文件 (*.pdf *.doc *.docx)"
            )
            
            if files:
                self.test_files = files
                self.update_file_list()
                self.log(f"✅ 已选择 {len(files)} 个测试文件")
            
        except Exception as e:
            self.log(f"❌ 选择测试文件失败: {e}")
    
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_list.clear()
        
        for file_path in self.test_files:
            file_name = Path(file_path).name
            file_size = "未知大小"
            
            try:
                if Path(file_path).exists():
                    size_bytes = Path(file_path).stat().st_size
                    if size_bytes < 1024:
                        file_size = f"{size_bytes} B"
                    elif size_bytes < 1024 * 1024:
                        file_size = f"{size_bytes / 1024:.1f} KB"
                    else:
                        file_size = f"{size_bytes / (1024 * 1024):.1f} MB"
            except:
                pass
            
            item_text = f"{file_name} ({file_size})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, file_path)
            self.file_list.addItem(item)
    
    def test_category_dialog(self):
        """测试分类选择对话框"""
        if not self.test_files:
            QMessageBox.warning(self, "警告", "请先创建或选择测试文件！")
            return
        
        try:
            self.log("🗂️ 开始测试分类选择对话框...")
            
            from ui.dialogs.import_category_dialog import show_import_category_dialog
            
            result = show_import_category_dialog(self.test_files, self)
            
            if result:
                self.log("✅ 分类选择对话框测试成功:")
                self.log(f"  • 选择分类: {result['category_name']} (ID: {result['category_id']})")
                self.log(f"  • 自动分类: {result['auto_categorize']}")
                self.log(f"  • 检查重复: {result['check_duplicates']}")
                self.log(f"  • 记住选择: {result['remember_choice']}")
                
                QMessageBox.information(
                    self, "测试成功", 
                    f"分类选择对话框工作正常！\n\n选择的分类: {result['category_name']}"
                )
            else:
                self.log("⚠️ 用户取消了分类选择")
            
        except Exception as e:
            self.log(f"❌ 测试分类选择对话框失败: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            
            QMessageBox.critical(
                self, "测试失败", 
                f"分类选择对话框测试失败:\n\n{e}"
            )
    
    def test_full_import(self):
        """测试完整导入流程"""
        if not self.test_files:
            QMessageBox.warning(self, "警告", "请先创建或选择测试文件！")
            return
        
        try:
            self.log("📥 开始测试完整导入流程...")
            
            # 测试FileManager的import_files_with_category方法
            from core.file_manager import FileManager
            from database.db_manager import get_db_manager
            from core.config_manager import get_config_manager
            
            # 获取管理器实例
            db_manager = get_db_manager()
            config_manager = get_config_manager()
            file_manager = FileManager(db_manager, config_manager)
            
            # 检查方法是否存在
            if hasattr(file_manager, 'import_files_with_category'):
                self.log("✅ FileManager.import_files_with_category 方法存在")
                
                # 模拟导入选项
                import_options = {
                    "category_id": "temp",
                    "category_name": "临时分组",
                    "auto_categorize": True,
                    "check_duplicates": True,
                    "remember_choice": False
                }
                
                self.log("📁 开始模拟导入...")
                
                # 测试方法调用（不实际导入）
                try:
                    # 这里只测试方法调用，不等待完成
                    file_manager.import_files_with_category(self.test_files[:2], import_options)
                    self.log("✅ import_files_with_category 方法调用成功")
                    
                    QMessageBox.information(
                        self, "测试成功", 
                        "完整导入流程测试成功！\n\nFileManager.import_files_with_category 方法工作正常。"
                    )
                    
                except Exception as e:
                    self.log(f"❌ 方法调用失败: {e}")
                    QMessageBox.warning(
                        self, "测试警告", 
                        f"方法调用失败:\n\n{e}"
                    )
                
            else:
                self.log("❌ FileManager.import_files_with_category 方法不存在")
                QMessageBox.critical(
                    self, "测试失败", 
                    "FileManager.import_files_with_category 方法不存在！\n\n修复未完成。"
                )
            
        except Exception as e:
            self.log(f"❌ 测试完整导入流程失败: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            
            QMessageBox.critical(
                self, "测试失败", 
                f"完整导入流程测试失败:\n\n{e}"
            )
    
    def open_main_app(self):
        """打开主程序"""
        try:
            self.log("🚀 启动主程序进行实际测试...")
            
            import subprocess
            import sys
            
            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"], 
                           cwd=str(project_root))
            
            self.log("✅ 主程序已启动")
            self.log("💡 请在主程序中测试导入功能:")
            self.log("  1. 点击导入按钮")
            self.log("  2. 选择测试文件")
            self.log("  3. 验证分类选择对话框是否正常显示")
            self.log("  4. 完成导入流程")
            
        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = ImportCategoryFixTestWindow()
    window.show()
    
    print("导入分类功能修复测试工具启动成功！")
    print("修复内容：")
    print("1. 🔧 添加 FileManager.import_files_with_category 方法")
    print("2. 🔧 创建 CategoryFileImportThread 类")
    print("3. 🔧 支持自动分类和重复检查")
    print("4. 🧪 提供完整的测试验证")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
