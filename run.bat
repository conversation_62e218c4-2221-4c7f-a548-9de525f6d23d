@echo off
chcp 65001 >nul
title 智能素材管理器

echo.
echo ========================================
echo           智能素材管理器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Python，请先安装Python 3.12或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [信息] 检测到Python环境
python --version

REM 检查虚拟环境
if not exist "venv" (
    echo.
    echo [信息] 创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo [错误] 创建虚拟环境失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo [信息] 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查依赖是否安装
echo [信息] 检查依赖包...
pip show PySide6 >nul 2>&1
if errorlevel 1 (
    echo [信息] 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [错误] 安装依赖包失败
        pause
        exit /b 1
    )
)

REM 启动程序
echo.
echo [信息] 启动智能素材管理器...
echo.
python main.py

REM 程序结束
echo.
echo [信息] 程序已退出
pause
