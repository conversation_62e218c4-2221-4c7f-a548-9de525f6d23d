# 设置对话框
# 功能：应用程序设置界面，包含主题、性能、AI、搜索等各种配置选项

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QWidget, QLabel, QPushButton, QCheckBox, QSpinBox,
                               QSlider, QComboBox, QLineEdit, QTextEdit, QGroupBox,
                               QGridLayout, QFileDialog, QMessageBox, QProgressBar,
                               QListWidget, QListWidgetItem, QSplitter)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon

class SettingsDialog(QDialog):
    """设置对话框类"""
    
    # 信号定义
    settings_changed = Signal()
    theme_changed = Signal(str)
    
    def __init__(self, config_manager, theme_manager, parent=None):
        super().__init__(parent)
        
        self.config_manager = config_manager
        self.theme_manager = theme_manager
        
        # 临时配置（用于预览）
        self.temp_config = {}
        
        self.setup_ui()
        self.load_settings()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(800, 600)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个设置页面
        self.create_general_tab()
        self.create_appearance_tab()
        self.create_performance_tab()
        self.create_ai_tab()
        self.create_search_tab()
        self.create_backup_tab()
        
        # 创建按钮区域
        self.create_button_area(main_layout)
        
    def create_general_tab(self):
        """创建常规设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 语言设置组
        language_group = QGroupBox("语言设置")
        language_layout = QGridLayout(language_group)
        
        language_layout.addWidget(QLabel("界面语言:"), 0, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        language_layout.addWidget(self.language_combo, 0, 1)
        
        layout.addWidget(language_group)
        
        # 启动设置组
        startup_group = QGroupBox("启动设置")
        startup_layout = QVBoxLayout(startup_group)
        
        self.auto_start_checkbox = QCheckBox("开机自动启动")
        startup_layout.addWidget(self.auto_start_checkbox)
        
        self.restore_session_checkbox = QCheckBox("启动时恢复上次会话")
        startup_layout.addWidget(self.restore_session_checkbox)
        
        layout.addWidget(startup_group)
        
        # 文件关联设置组
        association_group = QGroupBox("文件关联")
        association_layout = QVBoxLayout(association_group)
        
        self.associate_images_checkbox = QCheckBox("关联图片文件")
        association_layout.addWidget(self.associate_images_checkbox)
        
        self.associate_videos_checkbox = QCheckBox("关联视频文件")
        association_layout.addWidget(self.associate_videos_checkbox)
        
        layout.addWidget(association_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(widget, "常规")
        
    def create_appearance_tab(self):
        """创建外观设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主题设置组
        theme_group = QGroupBox("主题设置")
        theme_layout = QGridLayout(theme_group)
        
        theme_layout.addWidget(QLabel("主题:"), 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色主题", "深色主题"])
        theme_layout.addWidget(self.theme_combo, 0, 1)
        
        self.auto_theme_checkbox = QCheckBox("跟随系统主题")
        theme_layout.addWidget(self.auto_theme_checkbox, 1, 0, 1, 2)
        
        layout.addWidget(theme_group)
        
        # 字体设置组
        font_group = QGroupBox("字体设置")
        font_layout = QGridLayout(font_group)
        
        font_layout.addWidget(QLabel("字体大小:"), 0, 0)
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setRange(8, 24)
        self.font_size_spinbox.setValue(9)
        font_layout.addWidget(self.font_size_spinbox, 0, 1)
        
        layout.addWidget(font_group)
        
        # 界面设置组
        ui_group = QGroupBox("界面设置")
        ui_layout = QVBoxLayout(ui_group)
        
        self.show_toolbar_checkbox = QCheckBox("显示工具栏")
        self.show_toolbar_checkbox.setChecked(True)
        ui_layout.addWidget(self.show_toolbar_checkbox)
        
        self.show_statusbar_checkbox = QCheckBox("显示状态栏")
        self.show_statusbar_checkbox.setChecked(True)
        ui_layout.addWidget(self.show_statusbar_checkbox)
        
        self.show_preview_checkbox = QCheckBox("默认显示预览面板")
        self.show_preview_checkbox.setChecked(True)
        ui_layout.addWidget(self.show_preview_checkbox)
        
        layout.addWidget(ui_group)
        
        # 缩略图设置组
        thumbnail_group = QGroupBox("缩略图设置")
        thumbnail_layout = QGridLayout(thumbnail_group)
        
        thumbnail_layout.addWidget(QLabel("默认大小:"), 0, 0)
        self.thumbnail_size_slider = QSlider(Qt.Horizontal)
        self.thumbnail_size_slider.setRange(100, 300)
        self.thumbnail_size_slider.setValue(150)
        thumbnail_layout.addWidget(self.thumbnail_size_slider, 0, 1)
        
        self.thumbnail_size_label = QLabel("150px")
        thumbnail_layout.addWidget(self.thumbnail_size_label, 0, 2)
        
        thumbnail_layout.addWidget(QLabel("质量:"), 1, 0)
        self.thumbnail_quality_slider = QSlider(Qt.Horizontal)
        self.thumbnail_quality_slider.setRange(50, 100)
        self.thumbnail_quality_slider.setValue(85)
        thumbnail_layout.addWidget(self.thumbnail_quality_slider, 1, 1)
        
        self.thumbnail_quality_label = QLabel("85%")
        thumbnail_layout.addWidget(self.thumbnail_quality_label, 1, 2)
        
        layout.addWidget(thumbnail_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(widget, "外观")
        
    def create_performance_tab(self):
        """创建性能设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 缓存设置组
        cache_group = QGroupBox("缓存设置")
        cache_layout = QGridLayout(cache_group)
        
        cache_layout.addWidget(QLabel("缓存大小:"), 0, 0)
        self.cache_size_spinbox = QSpinBox()
        self.cache_size_spinbox.setRange(100, 2000)
        self.cache_size_spinbox.setValue(500)
        self.cache_size_spinbox.setSuffix(" MB")
        cache_layout.addWidget(self.cache_size_spinbox, 0, 1)
        
        self.clear_cache_button = QPushButton("清理缓存")
        cache_layout.addWidget(self.clear_cache_button, 0, 2)
        
        layout.addWidget(cache_group)
        
        # 并发设置组
        concurrent_group = QGroupBox("并发设置")
        concurrent_layout = QGridLayout(concurrent_group)
        
        concurrent_layout.addWidget(QLabel("最大并发任务:"), 0, 0)
        self.max_tasks_spinbox = QSpinBox()
        self.max_tasks_spinbox.setRange(1, 16)
        self.max_tasks_spinbox.setValue(4)
        concurrent_layout.addWidget(self.max_tasks_spinbox, 0, 1)
        
        layout.addWidget(concurrent_group)
        
        # 预加载设置组
        preload_group = QGroupBox("预加载设置")
        preload_layout = QVBoxLayout(preload_group)
        
        self.preload_thumbnails_checkbox = QCheckBox("预加载缩略图")
        self.preload_thumbnails_checkbox.setChecked(True)
        preload_layout.addWidget(self.preload_thumbnails_checkbox)
        
        self.virtual_scrolling_checkbox = QCheckBox("启用虚拟滚动")
        self.virtual_scrolling_checkbox.setChecked(True)
        preload_layout.addWidget(self.virtual_scrolling_checkbox)
        
        layout.addWidget(preload_group)
        
        # 动画设置组
        animation_group = QGroupBox("动画设置")
        animation_layout = QVBoxLayout(animation_group)
        
        self.enable_animations_checkbox = QCheckBox("启用界面动画")
        self.enable_animations_checkbox.setChecked(True)
        animation_layout.addWidget(self.enable_animations_checkbox)
        
        layout.addWidget(animation_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(widget, "性能")
        
    def create_ai_tab(self):
        """创建AI设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 自动分析设置组
        auto_analysis_group = QGroupBox("自动分析")
        auto_analysis_layout = QVBoxLayout(auto_analysis_group)
        
        self.auto_tag_checkbox = QCheckBox("自动生成标签")
        self.auto_tag_checkbox.setChecked(True)
        auto_analysis_layout.addWidget(self.auto_tag_checkbox)
        
        self.face_detection_checkbox = QCheckBox("人脸检测")
        self.face_detection_checkbox.setChecked(True)
        auto_analysis_layout.addWidget(self.face_detection_checkbox)
        
        self.object_detection_checkbox = QCheckBox("物体识别")
        self.object_detection_checkbox.setChecked(True)
        auto_analysis_layout.addWidget(self.object_detection_checkbox)
        
        self.color_analysis_checkbox = QCheckBox("颜色分析")
        self.color_analysis_checkbox.setChecked(True)
        auto_analysis_layout.addWidget(self.color_analysis_checkbox)
        
        layout.addWidget(auto_analysis_group)
        
        # 重复检测设置组
        duplicate_group = QGroupBox("重复检测")
        duplicate_layout = QGridLayout(duplicate_group)
        
        self.duplicate_detection_checkbox = QCheckBox("启用重复检测")
        self.duplicate_detection_checkbox.setChecked(True)
        duplicate_layout.addWidget(self.duplicate_detection_checkbox, 0, 0, 1, 2)
        
        duplicate_layout.addWidget(QLabel("相似度阈值:"), 1, 0)
        self.similarity_threshold_slider = QSlider(Qt.Horizontal)
        self.similarity_threshold_slider.setRange(50, 100)
        self.similarity_threshold_slider.setValue(80)
        duplicate_layout.addWidget(self.similarity_threshold_slider, 1, 1)
        
        self.similarity_threshold_label = QLabel("80%")
        duplicate_layout.addWidget(self.similarity_threshold_label, 1, 2)
        
        layout.addWidget(duplicate_group)
        
        # AI模型设置组
        model_group = QGroupBox("AI模型")
        model_layout = QGridLayout(model_group)
        
        model_layout.addWidget(QLabel("模型路径:"), 0, 0)
        self.model_path_edit = QLineEdit()
        model_layout.addWidget(self.model_path_edit, 0, 1)
        
        self.browse_model_button = QPushButton("浏览")
        model_layout.addWidget(self.browse_model_button, 0, 2)
        
        layout.addWidget(model_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(widget, "AI")
        
    def create_search_tab(self):
        """创建搜索设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 搜索行为设置组
        behavior_group = QGroupBox("搜索行为")
        behavior_layout = QVBoxLayout(behavior_group)
        
        self.auto_complete_checkbox = QCheckBox("启用自动完成")
        self.auto_complete_checkbox.setChecked(True)
        behavior_layout.addWidget(self.auto_complete_checkbox)
        
        self.fuzzy_search_checkbox = QCheckBox("启用模糊搜索")
        self.fuzzy_search_checkbox.setChecked(True)
        behavior_layout.addWidget(self.fuzzy_search_checkbox)
        
        self.search_in_tags_checkbox = QCheckBox("在标签中搜索")
        self.search_in_tags_checkbox.setChecked(True)
        behavior_layout.addWidget(self.search_in_tags_checkbox)
        
        self.search_in_filename_checkbox = QCheckBox("在文件名中搜索")
        self.search_in_filename_checkbox.setChecked(True)
        behavior_layout.addWidget(self.search_in_filename_checkbox)
        
        self.search_in_metadata_checkbox = QCheckBox("在元数据中搜索")
        self.search_in_metadata_checkbox.setChecked(True)
        behavior_layout.addWidget(self.search_in_metadata_checkbox)
        
        layout.addWidget(behavior_group)
        
        # 搜索历史设置组
        history_group = QGroupBox("搜索历史")
        history_layout = QGridLayout(history_group)
        
        history_layout.addWidget(QLabel("历史记录数量:"), 0, 0)
        self.history_limit_spinbox = QSpinBox()
        self.history_limit_spinbox.setRange(10, 200)
        self.history_limit_spinbox.setValue(50)
        history_layout.addWidget(self.history_limit_spinbox, 0, 1)
        
        self.clear_history_button = QPushButton("清空历史")
        history_layout.addWidget(self.clear_history_button, 0, 2)
        
        layout.addWidget(history_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(widget, "搜索")
        
    def create_backup_tab(self):
        """创建备份设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 自动备份设置组
        auto_backup_group = QGroupBox("自动备份")
        auto_backup_layout = QGridLayout(auto_backup_group)
        
        self.auto_backup_checkbox = QCheckBox("启用自动备份")
        self.auto_backup_checkbox.setChecked(True)
        auto_backup_layout.addWidget(self.auto_backup_checkbox, 0, 0, 1, 2)
        
        auto_backup_layout.addWidget(QLabel("备份间隔:"), 1, 0)
        self.backup_interval_spinbox = QSpinBox()
        self.backup_interval_spinbox.setRange(1, 30)
        self.backup_interval_spinbox.setValue(7)
        self.backup_interval_spinbox.setSuffix(" 天")
        auto_backup_layout.addWidget(self.backup_interval_spinbox, 1, 1)
        
        auto_backup_layout.addWidget(QLabel("最大备份数:"), 2, 0)
        self.max_backups_spinbox = QSpinBox()
        self.max_backups_spinbox.setRange(1, 20)
        self.max_backups_spinbox.setValue(5)
        auto_backup_layout.addWidget(self.max_backups_spinbox, 2, 1)
        
        layout.addWidget(auto_backup_group)
        
        # 备份位置设置组
        location_group = QGroupBox("备份位置")
        location_layout = QGridLayout(location_group)
        
        location_layout.addWidget(QLabel("备份目录:"), 0, 0)
        self.backup_location_edit = QLineEdit()
        location_layout.addWidget(self.backup_location_edit, 0, 1)
        
        self.browse_backup_button = QPushButton("浏览")
        location_layout.addWidget(self.browse_backup_button, 0, 2)
        
        layout.addWidget(location_group)
        
        # 手动备份设置组
        manual_group = QGroupBox("手动备份")
        manual_layout = QHBoxLayout(manual_group)
        
        self.backup_now_button = QPushButton("立即备份")
        manual_layout.addWidget(self.backup_now_button)
        
        self.restore_backup_button = QPushButton("恢复备份")
        manual_layout.addWidget(self.restore_backup_button)
        
        manual_layout.addStretch()
        
        layout.addWidget(manual_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(widget, "备份")
        
    def create_button_area(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 重置按钮
        self.reset_button = QPushButton("重置默认")
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        button_layout.addWidget(self.cancel_button)
        
        # 应用按钮
        self.apply_button = QPushButton("应用")
        button_layout.addWidget(self.apply_button)
        
        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.setDefault(True)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
        
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.ok_button.clicked.connect(self.accept_settings)
        self.cancel_button.clicked.connect(self.reject)
        self.apply_button.clicked.connect(self.apply_settings)
        self.reset_button.clicked.connect(self.reset_settings)
        
        # 主题变更连接
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        
        # 滑块值变更连接
        self.thumbnail_size_slider.valueChanged.connect(
            lambda v: self.thumbnail_size_label.setText(f"{v}px")
        )
        self.thumbnail_quality_slider.valueChanged.connect(
            lambda v: self.thumbnail_quality_label.setText(f"{v}%")
        )
        self.similarity_threshold_slider.valueChanged.connect(
            lambda v: self.similarity_threshold_label.setText(f"{v}%")
        )
        
        # 浏览按钮连接
        self.browse_model_button.clicked.connect(self.browse_model_path)
        self.browse_backup_button.clicked.connect(self.browse_backup_location)
        
        # 功能按钮连接
        self.clear_cache_button.clicked.connect(self.clear_cache)
        self.clear_history_button.clicked.connect(self.clear_search_history)
        self.backup_now_button.clicked.connect(self.backup_now)
        self.restore_backup_button.clicked.connect(self.restore_backup)
        
    def load_settings(self):
        """加载设置"""
        try:
            # 加载主题设置
            current_theme = self.config_manager.get_theme()
            if current_theme == "dark":
                self.theme_combo.setCurrentText("深色主题")
            else:
                self.theme_combo.setCurrentText("浅色主题")
                
            # 加载UI设置
            ui_config = self.config_manager.get_ui_config()
            self.thumbnail_size_slider.setValue(ui_config.get("thumbnail_size", 150))
            self.show_preview_checkbox.setChecked(ui_config.get("preview_panel_visible", True))
            
            # 加载文件管理设置
            file_config = self.config_manager.get_file_management_config()
            self.thumbnail_quality_slider.setValue(file_config.get("thumbnail_quality", 85))
            
            # 加载AI设置
            ai_config = self.config_manager.get_ai_config()
            self.auto_tag_checkbox.setChecked(ai_config.get("auto_tag", True))
            self.face_detection_checkbox.setChecked(ai_config.get("face_detection", True))
            self.object_detection_checkbox.setChecked(ai_config.get("object_detection", True))
            self.color_analysis_checkbox.setChecked(ai_config.get("color_analysis", True))
            self.duplicate_detection_checkbox.setChecked(ai_config.get("duplicate_detection", True))
            
            similarity_threshold = int(ai_config.get("similarity_threshold", 0.8) * 100)
            self.similarity_threshold_slider.setValue(similarity_threshold)
            
            # 加载搜索设置
            search_config = self.config_manager.get_search_config()
            self.auto_complete_checkbox.setChecked(search_config.get("auto_complete", True))
            self.fuzzy_search_checkbox.setChecked(search_config.get("fuzzy_search", True))
            self.search_in_tags_checkbox.setChecked(search_config.get("search_in_tags", True))
            self.search_in_filename_checkbox.setChecked(search_config.get("search_in_filename", True))
            self.search_in_metadata_checkbox.setChecked(search_config.get("search_in_metadata", True))
            self.history_limit_spinbox.setValue(search_config.get("search_history_limit", 50))
            
            # 加载性能设置
            performance_config = self.config_manager.get_performance_config()
            self.cache_size_spinbox.setValue(performance_config.get("cache_size_mb", 500))
            self.max_tasks_spinbox.setValue(performance_config.get("max_concurrent_tasks", 4))
            self.preload_thumbnails_checkbox.setChecked(performance_config.get("preload_thumbnails", True))
            self.virtual_scrolling_checkbox.setChecked(performance_config.get("virtual_scrolling", True))
            self.enable_animations_checkbox.setChecked(performance_config.get("animation_enabled", True))
            
            # 加载备份设置
            backup_config = self.config_manager.get_backup_config()
            self.auto_backup_checkbox.setChecked(backup_config.get("auto_backup", True))
            self.backup_interval_spinbox.setValue(backup_config.get("backup_interval_days", 7))
            self.max_backups_spinbox.setValue(backup_config.get("max_backup_files", 5))
            self.backup_location_edit.setText(backup_config.get("backup_location", ""))
            
        except Exception as e:
            print(f"加载设置失败: {e}")
            
    def accept_settings(self):
        """接受设置"""
        self.apply_settings()
        self.accept()
        
    def apply_settings(self):
        """应用设置"""
        try:
            # 应用主题设置
            theme_text = self.theme_combo.currentText()
            theme_name = "dark" if theme_text == "深色主题" else "light"
            if theme_name != self.config_manager.get_theme():
                self.config_manager.set_theme(theme_name)
                self.theme_manager.load_theme(theme_name)
                self.theme_changed.emit(theme_name)
                
            # 应用其他设置
            # TODO: 实现其他设置的应用逻辑
            
            # 保存配置
            self.config_manager.save_config()
            
            # 发送设置变更信号
            self.settings_changed.emit()
            
            QMessageBox.information(self, "设置", "设置已保存并应用")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用设置失败: {e}")
            
    def reset_settings(self):
        """重置设置"""
        reply = QMessageBox.question(
            self, "重置设置", 
            "确定要重置所有设置为默认值吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.config_manager.reset_to_default()
            self.load_settings()
            
    def on_theme_changed(self, theme_text):
        """主题变更处理"""
        # 实时预览主题变更
        theme_name = "dark" if theme_text == "深色主题" else "light"
        self.theme_manager.load_theme(theme_name)
        
    def browse_model_path(self):
        """浏览模型路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择AI模型文件", "", "模型文件 (*.onnx *.pb *.pth)"
        )
        if file_path:
            self.model_path_edit.setText(file_path)
            
    def browse_backup_location(self):
        """浏览备份位置"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择备份目录")
        if dir_path:
            self.backup_location_edit.setText(dir_path)
            
    def clear_cache(self):
        """清理缓存"""
        # TODO: 实现缓存清理
        QMessageBox.information(self, "缓存", "缓存已清理")
        
    def clear_search_history(self):
        """清空搜索历史"""
        # TODO: 实现搜索历史清理
        QMessageBox.information(self, "搜索历史", "搜索历史已清空")
        
    def backup_now(self):
        """立即备份"""
        # TODO: 实现立即备份
        QMessageBox.information(self, "备份", "备份已完成")
        
    def restore_backup(self):
        """恢复备份"""
        # TODO: 实现备份恢复
        QMessageBox.information(self, "恢复", "备份已恢复")
