# 内容区域组件
# 功能：中间内容展示区域，支持网格、列表、详细三种视图模式，包含面包屑导航和素材展示

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QScrollArea, QGridLayout, QListWidget,
                               QListWidgetItem, QTableWidget, QTableWidgetItem,
                               QStackedWidget, QFrame, QSlider, QComboBox,
                               QProgressBar, QFileDialog, QMessageBox)
from PySide6.QtCore import Qt, Signal, QSize, QTimer, QThread
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont, QDragEnterEvent, QDropEvent

import os
from pathlib import Path
from typing import List, Dict, Any

class ContentAreaWidget(QWidget):
    """内容区域控件类"""

    # 信号定义
    item_selected = Signal(int)  # 项目选择信号
    items_selection_changed = Signal(int)  # 选择变更信号
    files_dropped = Signal(list)  # 文件拖拽信号

    def __init__(self, theme_manager, db_manager, config_manager):
        super().__init__()

        self.theme_manager = theme_manager
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 界面组件
        self.breadcrumb_widget = None
        self.view_controls_widget = None
        self.stacked_widget = None
        self.grid_view = None
        self.list_view = None
        self.detail_view = None
        self.drop_zone = None

        # 状态
        self.current_view_mode = "grid"
        self.current_items = []
        self.selected_items = []
        self.thumbnail_size = 150

        # 启用拖拽
        self.setAcceptDrops(True)

        self.setup_ui()
        self.load_items()

    def setup_ui(self):
        """设置用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建顶部控制区域
        top_widget = self.create_top_controls()
        main_layout.addWidget(top_widget)

        # 创建内容展示区域
        content_widget = self.create_content_area()
        main_layout.addWidget(content_widget, 1)

        # 创建拖拽提示区域
        self.drop_zone = self.create_drop_zone()
        main_layout.addWidget(self.drop_zone)

    def create_top_controls(self):
        """创建顶部控制区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 面包屑导航
        self.breadcrumb_widget = BreadcrumbWidget()
        layout.addWidget(self.breadcrumb_widget)

        # 视图控制
        self.view_controls_widget = self.create_view_controls()
        layout.addWidget(self.view_controls_widget)

        return widget

    def create_view_controls(self):
        """创建视图控制区域"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 视图模式按钮
        grid_btn = QPushButton("网格")
        grid_btn.setCheckable(True)
        grid_btn.setChecked(True)
        grid_btn.clicked.connect(lambda: self.set_view_mode("grid"))
        layout.addWidget(grid_btn)

        list_btn = QPushButton("列表")
        list_btn.setCheckable(True)
        list_btn.clicked.connect(lambda: self.set_view_mode("list"))
        layout.addWidget(list_btn)

        detail_btn = QPushButton("详细")
        detail_btn.setCheckable(True)
        detail_btn.clicked.connect(lambda: self.set_view_mode("detail"))
        layout.addWidget(detail_btn)

        layout.addStretch()

        # 缩略图大小滑块
        size_label = QLabel("大小:")
        layout.addWidget(size_label)

        self.size_slider = QSlider(Qt.Horizontal)
        self.size_slider.setRange(100, 300)
        self.size_slider.setValue(150)
        self.size_slider.setFixedWidth(100)
        self.size_slider.valueChanged.connect(self.on_thumbnail_size_changed)
        layout.addWidget(self.size_slider)

        # 排序选择
        sort_label = QLabel("排序:")
        layout.addWidget(sort_label)

        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["名称", "大小", "类型", "创建时间", "修改时间", "评分"])
        self.sort_combo.currentTextChanged.connect(self.on_sort_changed)
        layout.addWidget(self.sort_combo)

        return widget

    def create_content_area(self):
        """创建内容展示区域"""
        # 创建堆叠控件
        self.stacked_widget = QStackedWidget()

        # 网格视图
        self.grid_view = GridViewWidget(self.theme_manager)
        self.grid_view.item_selected.connect(self.on_item_selected)
        self.grid_view.selection_changed.connect(self.on_selection_changed)
        self.stacked_widget.addWidget(self.grid_view)

        # 列表视图
        self.list_view = ListViewWidget(self.theme_manager)
        self.list_view.item_selected.connect(self.on_item_selected)
        self.list_view.selection_changed.connect(self.on_selection_changed)
        self.stacked_widget.addWidget(self.list_view)

        # 详细视图
        self.detail_view = DetailViewWidget(self.theme_manager)
        self.detail_view.item_selected.connect(self.on_item_selected)
        self.detail_view.selection_changed.connect(self.on_selection_changed)
        self.stacked_widget.addWidget(self.detail_view)

        return self.stacked_widget

    def create_drop_zone(self):
        """创建拖拽提示区域"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Box)
        widget.setLineWidth(2)
        widget.setFixedHeight(60)
        widget.setStyleSheet("""
            QFrame {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

        layout = QHBoxLayout(widget)

        # 拖拽图标
        icon_label = QLabel("📁")
        icon_label.setFont(QFont("", 20))
        layout.addWidget(icon_label)

        # 提示文字
        text_label = QLabel("拖拽文件或文件夹到此处进行导入")
        text_label.setFont(QFont("Microsoft YaHei", 10))
        layout.addWidget(text_label)

        layout.addStretch()

        # 浏览按钮
        browse_btn = QPushButton("浏览文件")
        browse_btn.clicked.connect(self.browse_files)
        layout.addWidget(browse_btn)

        return widget

    def set_view_mode(self, mode: str):
        """设置视图模式"""
        self.current_view_mode = mode

        if mode == "grid":
            self.stacked_widget.setCurrentWidget(self.grid_view)
        elif mode == "list":
            self.stacked_widget.setCurrentWidget(self.list_view)
        elif mode == "detail":
            self.stacked_widget.setCurrentWidget(self.detail_view)

        # 更新当前视图的数据
        self.update_current_view()

    def update_current_view(self):
        """更新当前视图"""
        current_widget = self.stacked_widget.currentWidget()
        if current_widget:
            current_widget.set_items(self.current_items)
            current_widget.set_thumbnail_size(self.thumbnail_size)

    def load_items(self, query: str = "", filters: Dict[str, Any] = None):
        """加载项目数据"""
        try:
            # 从数据库获取素材
            self.current_items = self.db_manager.search_materials(query, filters, limit=1000)

            # 更新当前视图
            self.update_current_view()

            # 更新面包屑
            self.breadcrumb_widget.set_path("全部素材")

        except Exception as e:
            print(f"加载项目失败: {e}")
            self.current_items = []

    def on_item_selected(self, item_id: int):
        """处理项目选择"""
        self.item_selected.emit(item_id)

    def on_selection_changed(self, selected_items: List[int]):
        """处理选择变更"""
        self.selected_items = selected_items
        self.items_selection_changed.emit(len(selected_items))

    def on_thumbnail_size_changed(self, size: int):
        """处理缩略图大小变更"""
        self.thumbnail_size = size
        current_widget = self.stacked_widget.currentWidget()
        if current_widget:
            current_widget.set_thumbnail_size(size)

    def on_sort_changed(self, sort_type: str):
        """处理排序变更"""
        try:
            if not self.current_items:
                return

            # 根据排序类型排序
            if sort_type == "名称":
                self.current_items.sort(key=lambda x: x.get('name', '').lower())
            elif sort_type == "大小":
                self.current_items.sort(key=lambda x: x.get('size', 0), reverse=True)
            elif sort_type == "类型":
                self.current_items.sort(key=lambda x: x.get('file_type', ''))
            elif sort_type == "创建时间":
                self.current_items.sort(key=lambda x: x.get('created_time', ''), reverse=True)
            elif sort_type == "修改时间":
                self.current_items.sort(key=lambda x: x.get('modified_time', ''), reverse=True)
            elif sort_type == "评分":
                self.current_items.sort(key=lambda x: x.get('rating', 0), reverse=True)

            # 更新当前视图
            self.update_current_view()

        except Exception as e:
            print(f"排序失败: {e}")

    def select_all(self):
        """全选"""
        current_widget = self.stacked_widget.currentWidget()
        if current_widget:
            current_widget.select_all()

    def copy_selected(self):
        """复制选中项"""
        if self.selected_items:
            try:
                from PySide6.QtGui import QClipboard
                from PySide6.QtCore import QMimeData, QUrl

                # 获取选中项目的文件路径
                file_paths = []
                for item_id in self.selected_items:
                    for item in self.current_items:
                        if item['id'] == item_id:
                            file_paths.append(item['file_path'])
                            break

                if file_paths:
                    # 创建MIME数据
                    mime_data = QMimeData()
                    urls = [QUrl.fromLocalFile(path) for path in file_paths]
                    mime_data.setUrls(urls)

                    # 复制到剪贴板
                    clipboard = QClipboard()
                    clipboard.setMimeData(mime_data)

                    print(f"已复制 {len(file_paths)} 个文件到剪贴板")

            except Exception as e:
                QMessageBox.warning(self, "复制失败", f"复制文件失败: {e}")

    def delete_selected(self):
        """删除选中项"""
        if self.selected_items:
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要从索引中删除选中的 {len(self.selected_items)} 个项目吗？\n注意：这只会从索引中删除，不会删除原始文件。",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    # 从数据库中删除记录
                    deleted_count = 0
                    for item_id in self.selected_items:
                        # TODO: 这里需要数据库管理器的引用
                        # self.db_manager.delete_material(item_id)
                        deleted_count += 1

                    # 从当前项目列表中移除
                    self.current_items = [
                        item for item in self.current_items
                        if item['id'] not in self.selected_items
                    ]

                    # 清空选择
                    self.selected_items.clear()

                    # 更新视图
                    self.update_current_view()

                    QMessageBox.information(self, "删除完成", f"已从索引中删除 {deleted_count} 个项目")

                except Exception as e:
                    QMessageBox.critical(self, "删除失败", f"删除项目失败: {e}")

    def browse_files(self):
        """浏览文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择文件", "",
            "所有支持的文件 (*.jpg *.jpeg *.png *.gif *.bmp *.tiff *.webp *.mp4 *.avi *.mov *.mkv *.wmv *.flv *.mp3 *.wav *.flac *.aac *.ogg *.pdf *.psd *.ai *.eps *.svg)"
        )

        if files:
            self.files_dropped.emit(files)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            # 高亮拖拽区域
            self.drop_zone.setStyleSheet("""
                QFrame {
                    border: 2px dashed #3498db;
                    border-radius: 8px;
                    background-color: #e8f4fd;
                }
            """)

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        # 恢复拖拽区域样式
        self.drop_zone.setStyleSheet("""
            QFrame {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if os.path.exists(file_path):
                files.append(file_path)

        if files:
            self.files_dropped.emit(files)

        # 恢复拖拽区域样式
        self.drop_zone.setStyleSheet("""
            QFrame {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

        event.acceptProposedAction()

class BreadcrumbWidget(QWidget):
    """面包屑导航控件"""

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)

    def set_path(self, path: str):
        """设置路径"""
        # 清空现有控件
        for i in reversed(range(self.layout.count())):
            child = self.layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 添加路径标签
        path_label = QLabel(path)
        path_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        self.layout.addWidget(path_label)

        self.layout.addStretch()

class GridViewWidget(QScrollArea):
    """网格视图控件"""

    item_selected = Signal(int)
    selection_changed = Signal(list)

    def __init__(self, theme_manager):
        super().__init__()
        self.theme_manager = theme_manager
        self.items = []
        self.selected_items = []
        self.thumbnail_size = 150

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setWidgetResizable(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # 创建内容控件
        self.content_widget = QWidget()
        self.grid_layout = QGridLayout(self.content_widget)
        self.grid_layout.setSpacing(10)
        self.setWidget(self.content_widget)

    def set_items(self, items: List[Dict[str, Any]]):
        """设置项目数据"""
        self.items = items
        self.refresh_view()

    def set_thumbnail_size(self, size: int):
        """设置缩略图大小"""
        self.thumbnail_size = size
        self.refresh_view()

    def refresh_view(self):
        """刷新视图"""
        # 清空现有控件
        for i in reversed(range(self.grid_layout.count())):
            child = self.grid_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 计算列数
        columns = max(1, self.width() // (self.thumbnail_size + 20))

        # 添加项目
        for i, item in enumerate(self.items):
            row = i // columns
            col = i % columns

            item_widget = GridItemWidget(item, self.thumbnail_size, self.theme_manager)
            item_widget.clicked.connect(lambda item_id=item['id']: self.item_selected.emit(item_id))
            self.grid_layout.addWidget(item_widget, row, col)

    def select_all(self):
        """全选"""
        self.selected_items = [item['id'] for item in self.items]
        self.selection_changed.emit(self.selected_items)

class ListViewWidget(QListWidget):
    """列表视图控件"""

    item_selected = Signal(int)
    selection_changed = Signal(list)

    def __init__(self, theme_manager):
        super().__init__()
        self.theme_manager = theme_manager
        self.items = []

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setSelectionMode(QListWidget.ExtendedSelection)
        self.itemClicked.connect(self.on_item_clicked)
        self.itemSelectionChanged.connect(self.on_selection_changed)

    def set_items(self, items: List[Dict[str, Any]]):
        """设置项目数据"""
        self.items = items
        self.refresh_view()

    def set_thumbnail_size(self, size: int):
        """设置缩略图大小"""
        # 列表视图不需要调整大小
        pass

    def refresh_view(self):
        """刷新视图"""
        self.clear()

        for item in self.items:
            list_item = QListWidgetItem(f"{item['name']} ({item['file_type']})")
            list_item.setData(Qt.UserRole, item['id'])
            self.addItem(list_item)

    def on_item_clicked(self, item):
        """项目点击处理"""
        item_id = item.data(Qt.UserRole)
        if item_id:
            self.item_selected.emit(item_id)

    def on_selection_changed(self):
        """选择变更处理"""
        selected_items = []
        for item in self.selectedItems():
            item_id = item.data(Qt.UserRole)
            if item_id:
                selected_items.append(item_id)
        self.selection_changed.emit(selected_items)

    def select_all(self):
        """全选"""
        self.selectAll()

class DetailViewWidget(QTableWidget):
    """详细视图控件"""

    item_selected = Signal(int)
    selection_changed = Signal(list)

    def __init__(self, theme_manager):
        super().__init__()
        self.theme_manager = theme_manager
        self.items = []

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 设置表头
        headers = ["名称", "类型", "大小", "尺寸", "创建时间", "评分"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        # 设置选择模式
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.ExtendedSelection)

        # 连接信号
        self.itemClicked.connect(self.on_item_clicked)
        self.itemSelectionChanged.connect(self.on_selection_changed)

    def set_items(self, items: List[Dict[str, Any]]):
        """设置项目数据"""
        self.items = items
        self.refresh_view()

    def set_thumbnail_size(self, size: int):
        """设置缩略图大小"""
        # 详细视图不需要调整大小
        pass

    def refresh_view(self):
        """刷新视图"""
        self.setRowCount(len(self.items))

        for row, item in enumerate(self.items):
            # 名称
            name_item = QTableWidgetItem(item['name'])
            name_item.setData(Qt.UserRole, item['id'])
            self.setItem(row, 0, name_item)

            # 类型
            self.setItem(row, 1, QTableWidgetItem(item['file_type']))

            # 大小
            size_mb = item.get('size', 0) / (1024 * 1024)
            self.setItem(row, 2, QTableWidgetItem(f"{size_mb:.2f} MB"))

            # 尺寸
            width = item.get('width', 0)
            height = item.get('height', 0)
            if width and height:
                dimensions = f"{width}x{height}"
            else:
                dimensions = "-"
            self.setItem(row, 3, QTableWidgetItem(dimensions))

            # 创建时间
            created_time = item.get('created_time', '')
            self.setItem(row, 4, QTableWidgetItem(created_time))

            # 评分
            rating = item.get('rating', 0)
            self.setItem(row, 5, QTableWidgetItem(str(rating)))

        # 调整列宽
        self.resizeColumnsToContents()

    def on_item_clicked(self, item):
        """项目点击处理"""
        item_id = item.data(Qt.UserRole)
        if item_id:
            self.item_selected.emit(item_id)

    def on_selection_changed(self):
        """选择变更处理"""
        selected_items = []
        for item in self.selectedItems():
            if item.column() == 0:  # 只处理第一列的选择
                item_id = item.data(Qt.UserRole)
                if item_id:
                    selected_items.append(item_id)
        self.selection_changed.emit(selected_items)

    def select_all(self):
        """全选"""
        self.selectAll()

class GridItemWidget(QWidget):
    """网格项目控件"""

    clicked = Signal()

    def __init__(self, item_data, thumbnail_size, theme_manager):
        super().__init__()
        self.item_data = item_data
        self.thumbnail_size = thumbnail_size
        self.theme_manager = theme_manager

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setFixedSize(self.thumbnail_size + 20, self.thumbnail_size + 60)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 缩略图
        thumbnail_label = QLabel()
        thumbnail_label.setFixedSize(self.thumbnail_size, self.thumbnail_size)
        thumbnail_label.setStyleSheet("""
            QLabel {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #f8f9fa;
            }
        """)
        thumbnail_label.setAlignment(Qt.AlignCenter)

        # TODO: 加载实际缩略图
        thumbnail_label.setText("📄")
        thumbnail_label.setFont(QFont("", self.thumbnail_size // 4))

        layout.addWidget(thumbnail_label)

        # 文件名
        name_label = QLabel(self.item_data['name'])
        name_label.setWordWrap(True)
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setMaximumHeight(40)
        layout.addWidget(name_label)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)
