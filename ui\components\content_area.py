# 内容区域组件
# 功能：中间内容展示区域，支持网格、列表、详细三种视图模式，包含面包屑导航和素材展示

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QScrollArea, QGridLayout, QListWidget,
                               QListWidgetItem, QTableWidget, QTableWidgetItem,
                               QStackedWidget, QFrame, QSlider, QComboBox,
                               QProgressBar, QFileDialog, QMessageBox, QApplication)
from PySide6.QtCore import Qt, Signal, QSize, QTimer, QThread, QRect
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont, QDragEnterEvent, QDropEvent

import os
import queue
import threading
from pathlib import Path
from typing import List, Dict, Any

class HighPerformanceThumbnailLoader(QThread):
    """高性能异步缩略图加载器"""

    thumbnail_loaded = Signal(str, QPixmap)
    batch_loaded = Signal(dict)  # 批量加载完成信号

    def __init__(self):
        super().__init__()

        # 资源管理标志
        self._is_destroyed = False
        self._cleanup_in_progress = False
        self._stop_requested = False

        self.load_queue = queue.PriorityQueue()  # 优先级队列
        self.running = True
        self.cache = {}  # 内存缓存
        self.cache_size_limit = 100  # 缓存大小限制
        self.batch_size = 5  # 批量处理大小

        # 预加载相关
        self.preload_enabled = True
        self.preload_distance = 10  # 预加载距离

    def load_thumbnail(self, file_path: str, priority: int = 1):
        """请求加载缩略图（支持优先级）"""
        if file_path in self.cache:
            # 直接从缓存返回
            self.thumbnail_loaded.emit(file_path, self.cache[file_path])
            return

        self.load_queue.put((priority, file_path))

    def preload_thumbnails(self, file_paths: List[str]):
        """预加载缩略图"""
        if not self.preload_enabled:
            return

        for file_path in file_paths[:self.preload_distance]:
            self.load_thumbnail(file_path, priority=2)  # 低优先级

    def stop(self):
        """停止加载器"""
        if self._is_destroyed or self._cleanup_in_progress:
            return

        self._cleanup_in_progress = True
        self._stop_requested = True
        self.running = False

        try:
            # 发送停止信号
            self.load_queue.put((0, None))
        except:
            pass  # 队列可能已经被销毁

    def safe_cleanup(self):
        """安全清理资源"""
        if self._is_destroyed:
            return

        try:
            self._is_destroyed = True
            self.stop()

            # 清理缓存
            if hasattr(self, 'cache'):
                self.cache.clear()

            # 等待线程结束
            if self.isRunning():
                self.wait(3000)  # 等待3秒
                if self.isRunning():
                    self.terminate()  # 强制终止
                    self.wait(1000)  # 再等待1秒

        except Exception as e:
            print(f"缩略图加载器清理失败: {e}")
        except RuntimeError:
            # 对象已被C++端删除，忽略错误
            pass

    def __del__(self):
        """析构函数"""
        try:
            self.safe_cleanup()
        except RuntimeError:
            # 对象已被C++端删除，忽略错误
            pass

    def run(self):
        """线程运行（批量处理优化）"""
        batch = []

        while self.running and not self._stop_requested and not self._is_destroyed:
            try:
                # 检查是否应该停止
                if self._cleanup_in_progress or self._is_destroyed:
                    break

                # 收集批量任务
                while len(batch) < self.batch_size and not self._stop_requested:
                    try:
                        priority, file_path = self.load_queue.get(timeout=0.1)
                        if file_path is None:  # 停止信号
                            self.running = False
                            break
                        batch.append(file_path)
                    except queue.Empty:
                        break

                if not batch or self._stop_requested:
                    continue

                # 批量处理
                results = self._batch_generate_thumbnails(batch)

                # 发送结果（检查对象是否还存在）
                for file_path, pixmap in results.items():
                    if pixmap and not pixmap.isNull() and not self._is_destroyed:
                        try:
                            # 添加到缓存
                            self._add_to_cache(file_path, pixmap)
                            self.thumbnail_loaded.emit(file_path, pixmap)
                        except RuntimeError:
                            # 对象已被删除，停止发送信号
                            self.running = False
                            break

                # 清空批次
                batch.clear()

            except Exception as e:
                if not self._is_destroyed:
                    print(f"批量缩略图加载失败: {e}")
                batch.clear()

        # 清理退出
        if hasattr(self, 'cache'):
            self.cache.clear()

    def _batch_generate_thumbnails(self, file_paths: List[str]) -> Dict[str, QPixmap]:
        """批量生成缩略图"""
        results = {}

        for file_path in file_paths:
            try:
                pixmap = self._generate_thumbnail_optimized(file_path)
                if pixmap and not pixmap.isNull():
                    results[file_path] = pixmap
            except Exception as e:
                print(f"生成缩略图失败 {file_path}: {e}")

        return results

    def _generate_thumbnail_optimized(self, file_path: str) -> QPixmap:
        """优化的缩略图生成（集成智能压缩）"""
        try:
            # 使用智能图片压缩器
            from core.image_compressor import get_image_compressor, CompressionConfig, CompressionLevel

            # 获取压缩器实例
            compressor = get_image_compressor()

            # 分析图片信息
            image_info = compressor.analyze_image(file_path)

            # 如果是大尺寸图片，使用智能压缩
            if image_info.needs_compression:
                print(f"🖼️ 检测到大尺寸图片: {image_info.original_size}, 正在智能压缩...")
                compressed_pixmap = compressor.compress_for_thumbnail(file_path)

                if not compressed_pixmap.isNull():
                    return compressed_pixmap

            # 对于小尺寸图片，使用原有的快速方法
            return self._generate_thumbnail_fast(file_path)

        except Exception as e:
            print(f"智能缩略图生成失败 {file_path}: {e}")
            # 回退到原有方法
            return self._generate_thumbnail_fast(file_path)

    def _generate_thumbnail_fast(self, file_path: str) -> QPixmap:
        """快速缩略图生成（原有方法）"""
        try:
            # 检查缓存
            thumbnail_dir = Path.home() / ".smart_asset_manager" / "thumbnails"
            thumbnail_dir.mkdir(parents=True, exist_ok=True)

            # 使用更好的哈希算法
            import hashlib
            file_hash = hashlib.md5(file_path.encode()).hexdigest()
            thumbnail_path = thumbnail_dir / f"{file_hash}.jpg"

            # 检查缓存文件
            if (thumbnail_path.exists() and
                thumbnail_path.stat().st_mtime > Path(file_path).stat().st_mtime):
                return QPixmap(str(thumbnail_path))

            # 生成新缩略图（优化版本）
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                # 使用更快的缩放算法
                scaled_pixmap = pixmap.scaled(
                    200, 200,
                    Qt.KeepAspectRatio,
                    Qt.FastTransformation  # 使用快速变换
                )

                # 异步保存缓存（不阻塞）
                threading.Thread(
                    target=self._save_thumbnail_async,
                    args=(scaled_pixmap, str(thumbnail_path)),
                    daemon=True
                ).start()

                return scaled_pixmap

        except Exception as e:
            print(f"快速缩略图生成失败 {file_path}: {e}")

        return QPixmap()

    def _save_thumbnail_async(self, pixmap: QPixmap, path: str):
        """异步保存缩略图"""
        try:
            pixmap.save(path, "JPEG", 85)
        except Exception as e:
            print(f"保存缩略图失败 {path}: {e}")

    def _add_to_cache(self, file_path: str, pixmap: QPixmap):
        """添加到内存缓存"""
        if len(self.cache) >= self.cache_size_limit:
            # 移除最旧的缓存项
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]

        self.cache[file_path] = pixmap

    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()

class ContentAreaWidget(QWidget):
    """内容区域控件类"""

    # 信号定义
    item_selected = Signal(int)  # 项目选择信号
    items_selection_changed = Signal(int)  # 选择变更信号
    files_dropped = Signal(list)  # 文件拖拽信号

    def __init__(self, theme_manager, db_manager, config_manager):
        super().__init__()

        self.theme_manager = theme_manager
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 界面组件
        self.breadcrumb_widget = None
        self.view_controls_widget = None
        self.stacked_widget = None
        self.grid_view = None
        self.list_view = None
        self.detail_view = None
        self.drop_zone = None

        # 状态
        self.current_view_mode = "grid"
        self.current_items = []
        self.selected_items = []
        self.thumbnail_size = 150

        # 启用拖拽
        self.setAcceptDrops(True)

        # 高性能异步缩略图加载器
        self.thumbnail_loader = HighPerformanceThumbnailLoader()
        self.thumbnail_loader.thumbnail_loaded.connect(self._on_thumbnail_loaded)
        self.thumbnail_loader.start()

        self.setup_ui()
        self.load_items()

    def cleanup_resources(self):
        """清理资源"""
        try:
            if hasattr(self, 'thumbnail_loader') and self.thumbnail_loader:
                # 使用安全清理方法
                self.thumbnail_loader.safe_cleanup()
        except Exception as e:
            print(f"清理缩略图加载器失败: {e}")

    def __del__(self):
        """析构函数"""
        self.cleanup_resources()

    def closeEvent(self, event):
        """关闭事件"""
        self.cleanup_resources()
        super().closeEvent(event)

    def setup_ui(self):
        """设置用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建顶部控制区域
        top_widget = self.create_top_controls()
        main_layout.addWidget(top_widget)

        # 创建内容展示区域
        content_widget = self.create_content_area()
        main_layout.addWidget(content_widget, 1)

        # 创建拖拽提示区域
        self.drop_zone = self.create_drop_zone()
        main_layout.addWidget(self.drop_zone)

    def create_top_controls(self):
        """创建顶部控制区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 面包屑导航
        self.breadcrumb_widget = BreadcrumbWidget()
        layout.addWidget(self.breadcrumb_widget)

        # 视图控制
        self.view_controls_widget = self.create_view_controls()
        layout.addWidget(self.view_controls_widget)

        return widget

    def create_view_controls(self):
        """创建视图控制区域"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # 视图模式按钮
        grid_btn = QPushButton("网格")
        grid_btn.setCheckable(True)
        grid_btn.setChecked(True)
        grid_btn.clicked.connect(lambda: self.set_view_mode("grid"))
        layout.addWidget(grid_btn)

        list_btn = QPushButton("列表")
        list_btn.setCheckable(True)
        list_btn.clicked.connect(lambda: self.set_view_mode("list"))
        layout.addWidget(list_btn)

        detail_btn = QPushButton("详细")
        detail_btn.setCheckable(True)
        detail_btn.clicked.connect(lambda: self.set_view_mode("detail"))
        layout.addWidget(detail_btn)

        # 分隔线
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.VLine)
        separator1.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator1)

        # 批量选择按钮组
        self.select_all_btn = QPushButton("✅ 全选")
        self.select_all_btn.clicked.connect(self.select_all_items)
        self.select_all_btn.setToolTip("选择所有可见的素材")
        layout.addWidget(self.select_all_btn)

        self.invert_selection_btn = QPushButton("🔄 反选")
        self.invert_selection_btn.clicked.connect(self.invert_selection)
        self.invert_selection_btn.setToolTip("反转当前选择状态")
        layout.addWidget(self.invert_selection_btn)

        self.clear_selection_btn = QPushButton("🗑️ 删除选中")
        self.clear_selection_btn.clicked.connect(self.delete_selected_items)
        self.clear_selection_btn.setToolTip("删除选中的素材")
        layout.addWidget(self.clear_selection_btn)

        # 选择状态显示
        self.selection_label = QLabel("未选择")
        self.selection_label.setStyleSheet("color: #666; font-size: 12px; margin-left: 10px;")
        layout.addWidget(self.selection_label)

        # 分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator2)

        layout.addStretch()

        # 缩略图大小滑块
        size_label = QLabel("大小:")
        layout.addWidget(size_label)

        self.size_slider = QSlider(Qt.Horizontal)
        self.size_slider.setRange(100, 300)
        self.size_slider.setValue(150)
        self.size_slider.setFixedWidth(100)

        # 使用防抖动定时器优化滑块性能
        self.size_change_timer = QTimer()
        self.size_change_timer.setSingleShot(True)
        self.size_change_timer.timeout.connect(self._apply_thumbnail_size_change)

        # 连接滑块事件到防抖动处理
        self.size_slider.valueChanged.connect(self._on_thumbnail_size_changing)
        self.size_slider.sliderReleased.connect(self._on_thumbnail_size_released)

        layout.addWidget(self.size_slider)

        # 排序选择
        sort_label = QLabel("排序:")
        layout.addWidget(sort_label)

        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["名称", "大小", "类型", "创建时间", "修改时间", "评分"])
        self.sort_combo.currentTextChanged.connect(self.on_sort_changed)
        layout.addWidget(self.sort_combo)

        return widget

    def create_content_area(self):
        """创建内容展示区域"""
        # 创建堆叠控件
        self.stacked_widget = QStackedWidget()

        # 网格视图
        self.grid_view = GridViewWidget(self.theme_manager)
        self.grid_view.item_selected.connect(self.on_item_selected)
        self.grid_view.selection_changed.connect(self.on_selection_changed)
        self.stacked_widget.addWidget(self.grid_view)

        # 列表视图
        self.list_view = ListViewWidget(self.theme_manager)
        self.list_view.item_selected.connect(self.on_item_selected)
        self.list_view.selection_changed.connect(self.on_selection_changed)
        self.stacked_widget.addWidget(self.list_view)

        # 详细视图
        self.detail_view = DetailViewWidget(self.theme_manager)
        self.detail_view.item_selected.connect(self.on_item_selected)
        self.detail_view.selection_changed.connect(self.on_selection_changed)
        self.stacked_widget.addWidget(self.detail_view)

        return self.stacked_widget

    def create_drop_zone(self):
        """创建拖拽提示区域"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Box)
        widget.setLineWidth(2)
        widget.setFixedHeight(60)
        widget.setStyleSheet("""
            QFrame {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

        layout = QHBoxLayout(widget)

        # 拖拽图标
        icon_label = QLabel("📁")
        icon_label.setFont(QFont("", 20))
        layout.addWidget(icon_label)

        # 提示文字
        text_label = QLabel("拖拽文件或文件夹到此处进行导入")
        text_label.setFont(QFont("Microsoft YaHei", 10))
        layout.addWidget(text_label)

        layout.addStretch()

        # 浏览按钮
        browse_btn = QPushButton("浏览文件")
        browse_btn.clicked.connect(self.browse_files)
        layout.addWidget(browse_btn)

        return widget

    def set_view_mode(self, mode: str):
        """设置视图模式"""
        self.current_view_mode = mode

        if mode == "grid":
            self.stacked_widget.setCurrentWidget(self.grid_view)
        elif mode == "list":
            self.stacked_widget.setCurrentWidget(self.list_view)
        elif mode == "detail":
            self.stacked_widget.setCurrentWidget(self.detail_view)

        # 更新当前视图的数据
        self.update_current_view()

    def update_current_view(self):
        """更新当前视图（优化性能）"""
        try:
            current_widget = self.stacked_widget.currentWidget()
            if current_widget:
                # 暂时禁用更新以提高性能
                current_widget.setUpdatesEnabled(False)

                # 更新数据
                current_widget.set_items(self.current_items)
                current_widget.set_thumbnail_size(self.thumbnail_size)

                # 重新启用更新
                current_widget.setUpdatesEnabled(True)

        except Exception as e:
            print(f"更新视图失败: {e}")

    def load_items(self, query: str = "", filters: Dict[str, Any] = None):
        """加载项目数据"""
        try:
            # 从数据库获取素材
            self.current_items = self.db_manager.search_materials(query, filters, limit=1000)

            # 更新当前视图
            self.update_current_view()

            # 更新面包屑
            self.breadcrumb_widget.set_path("全部素材")

        except Exception as e:
            print(f"加载项目失败: {e}")
            self.current_items = []

    def on_item_selected(self, item_id: int):
        """处理项目选择"""
        self.item_selected.emit(item_id)

    def on_selection_changed(self, selected_items: List[int]):
        """处理选择变更"""
        self.selected_items = selected_items
        self.items_selection_changed.emit(len(selected_items))

        # 更新选择状态显示
        self.update_selection_display()

    def _on_thumbnail_size_changing(self, size: int):
        """滑块拖动中（超级防抖动处理）"""
        self.pending_thumbnail_size = size

        # 立即更新滑块标签显示（无延迟反馈）
        if hasattr(self, 'size_label'):
            self.size_label.setText(f"{size}px")

        # 重启定时器，延迟应用实际更改
        self.size_change_timer.stop()
        self.size_change_timer.start(100)  # 减少到100ms延迟，更快响应

    def _on_thumbnail_size_released(self):
        """滑块释放时立即应用更改"""
        self.size_change_timer.stop()
        self._apply_thumbnail_size_change()

    def _apply_thumbnail_size_change(self):
        """实际应用缩略图大小更改"""
        if hasattr(self, 'pending_thumbnail_size'):
            size = self.pending_thumbnail_size
            self.thumbnail_size = size

            # 批量更新，提升性能
            current_widget = self.stacked_widget.currentWidget()
            if current_widget:
                # 暂时禁用更新
                current_widget.setUpdatesEnabled(False)
                try:
                    current_widget.set_thumbnail_size(size)
                finally:
                    # 重新启用更新
                    current_widget.setUpdatesEnabled(True)

    def on_thumbnail_size_changed(self, size: int):
        """处理缩略图大小变更（保持兼容性）"""
        self.thumbnail_size = size
        current_widget = self.stacked_widget.currentWidget()
        if current_widget:
            current_widget.set_thumbnail_size(size)

    def on_sort_changed(self, sort_type: str):
        """处理排序变更"""
        try:
            if not self.current_items:
                return

            # 根据排序类型排序
            if sort_type == "名称":
                self.current_items.sort(key=lambda x: x.get('name', '').lower())
            elif sort_type == "大小":
                self.current_items.sort(key=lambda x: x.get('size', 0), reverse=True)
            elif sort_type == "类型":
                self.current_items.sort(key=lambda x: x.get('file_type', ''))
            elif sort_type == "创建时间":
                self.current_items.sort(key=lambda x: x.get('created_time', ''), reverse=True)
            elif sort_type == "修改时间":
                self.current_items.sort(key=lambda x: x.get('modified_time', ''), reverse=True)
            elif sort_type == "评分":
                self.current_items.sort(key=lambda x: x.get('rating', 0), reverse=True)

            # 更新当前视图
            self.update_current_view()

        except Exception as e:
            print(f"排序失败: {e}")

    def _on_thumbnail_loaded(self, file_path: str, pixmap: QPixmap):
        """缩略图加载完成回调"""
        try:
            # 通知当前视图更新缩略图
            current_widget = self.stacked_widget.currentWidget()
            if hasattr(current_widget, 'update_thumbnail'):
                current_widget.update_thumbnail(file_path, pixmap)
        except Exception as e:
            print(f"更新缩略图失败: {e}")

    def select_all_items(self):
        """全选所有素材"""
        try:
            current_widget = self.stacked_widget.currentWidget()
            if current_widget and hasattr(current_widget, 'select_all'):
                # 调用视图控件的全选方法
                current_widget.select_all()

                # 更新内部选择状态
                all_item_ids = [item.get('id', i) for i, item in enumerate(self.current_items)]
                self.selected_items = all_item_ids

                # 确保视觉状态同步
                self._sync_visual_selection()

                print(f"✅ 已全选 {len(self.current_items)} 个素材")
            else:
                # 手动实现全选
                all_item_ids = [item.get('id', i) for i, item in enumerate(self.current_items)]
                self.selected_items = all_item_ids
                self.update_selection_display()
                self.items_selection_changed.emit(len(all_item_ids))
                print(f"✅ 已全选 {len(all_item_ids)} 个素材")
        except Exception as e:
            print(f"❌ 全选失败: {e}")

    def invert_selection(self):
        """反选素材"""
        try:
            current_widget = self.stacked_widget.currentWidget()
            if current_widget and hasattr(current_widget, 'invert_selection'):
                current_widget.invert_selection()
            else:
                # 手动实现反选
                all_item_ids = [item.get('id', i) for i, item in enumerate(self.current_items)]
                new_selection = [item_id for item_id in all_item_ids if item_id not in self.selected_items]
                self.selected_items = new_selection
                self.update_selection_display()
                self.items_selection_changed.emit(len(new_selection))
                print(f"🔄 反选完成，当前选择 {len(new_selection)} 个素材")
        except Exception as e:
            print(f"❌ 反选失败: {e}")

    def clear_selection(self):
        """清空所有选择"""
        try:
            current_widget = self.stacked_widget.currentWidget()
            if current_widget and hasattr(current_widget, 'clear_selection'):
                current_widget.clear_selection()
            else:
                # 手动实现清空选择
                self.selected_items = []
                self.update_selection_display()
                self.items_selection_changed.emit(0)
                print("❌ 已清空所有选择")
        except Exception as e:
            print(f"❌ 清空选择失败: {e}")

    def _sync_visual_selection(self):
        """同步视觉选择状态"""
        try:
            current_widget = self.stacked_widget.currentWidget()
            if current_widget:
                # 确保视图控件的选择状态与内部状态一致
                if hasattr(current_widget, 'sync_selection'):
                    current_widget.sync_selection(self.selected_items)

                # 更新显示
                self.update_selection_display()
                self.items_selection_changed.emit(len(self.selected_items))

        except Exception as e:
            print(f"❌ 同步视觉选择状态失败: {e}")

    def update_selection_display(self):
        """更新选择状态显示"""
        try:
            selected_count = len(self.selected_items)
            total_count = len(self.current_items)

            if selected_count == 0:
                self.selection_label.setText("未选择")
                self.selection_label.setStyleSheet("color: #666; font-size: 12px; margin-left: 10px;")
            elif selected_count == total_count and total_count > 0:
                self.selection_label.setText(f"已全选 ({selected_count})")
                self.selection_label.setStyleSheet("color: #27AE60; font-size: 12px; margin-left: 10px; font-weight: bold;")
            else:
                self.selection_label.setText(f"已选择 {selected_count}/{total_count}")
                self.selection_label.setStyleSheet("color: #3498DB; font-size: 12px; margin-left: 10px; font-weight: bold;")

            # 更新按钮状态和文本
            self.select_all_btn.setEnabled(total_count > 0)
            self.invert_selection_btn.setEnabled(total_count > 0)

            # 修改清空按钮的文本和功能
            if selected_count > 0:
                self.clear_selection_btn.setText(f"🗑️ 删除选中({selected_count})")
                self.clear_selection_btn.setEnabled(True)
                self.clear_selection_btn.setToolTip(f"删除选中的 {selected_count} 个素材")
            else:
                self.clear_selection_btn.setText("🗑️ 删除选中")
                self.clear_selection_btn.setEnabled(False)
                self.clear_selection_btn.setToolTip("没有选中的素材")

        except Exception as e:
            print(f"❌ 更新选择显示失败: {e}")

    def select_all(self):
        """全选（保持兼容性）"""
        self.select_all_items()

    def copy_selected(self):
        """复制选中项"""
        if self.selected_items:
            try:
                from PySide6.QtGui import QClipboard
                from PySide6.QtCore import QMimeData, QUrl

                # 获取选中项目的文件路径
                file_paths = []
                for item_id in self.selected_items:
                    for item in self.current_items:
                        if item['id'] == item_id:
                            file_paths.append(item['file_path'])
                            break

                if file_paths:
                    # 创建MIME数据
                    mime_data = QMimeData()
                    urls = [QUrl.fromLocalFile(path) for path in file_paths]
                    mime_data.setUrls(urls)

                    # 复制到剪贴板
                    clipboard = QClipboard()
                    clipboard.setMimeData(mime_data)

                    print(f"已复制 {len(file_paths)} 个文件到剪贴板")

            except Exception as e:
                QMessageBox.warning(self, "复制失败", f"复制文件失败: {e}")

    def delete_selected(self):
        """删除选中项"""
        if self.selected_items:
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要从索引中删除选中的 {len(self.selected_items)} 个项目吗？\n注意：这只会从索引中删除，不会删除原始文件。",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    # 从数据库中删除记录
                    deleted_count = 0
                    for item_id in self.selected_items:
                        # TODO: 这里需要数据库管理器的引用
                        # self.db_manager.delete_material(item_id)
                        deleted_count += 1

                    # 从当前项目列表中移除
                    self.current_items = [
                        item for item in self.current_items
                        if item['id'] not in self.selected_items
                    ]

                    # 清空选择
                    self.selected_items.clear()

                    # 更新视图
                    self.update_current_view()

                    QMessageBox.information(self, "删除完成", f"已从索引中删除 {deleted_count} 个项目")

                except Exception as e:
                    QMessageBox.critical(self, "删除失败", f"删除项目失败: {e}")

    def browse_files(self):
        """浏览文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择文件", "",
            "所有支持的文件 (*.jpg *.jpeg *.png *.gif *.bmp *.tiff *.webp *.mp4 *.avi *.mov *.mkv *.wmv *.flv *.mp3 *.wav *.flac *.aac *.ogg *.pdf *.psd *.ai *.eps *.svg)"
        )

        if files:
            self.files_dropped.emit(files)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            # 高亮拖拽区域
            self.drop_zone.setStyleSheet("""
                QFrame {
                    border: 2px dashed #3498db;
                    border-radius: 8px;
                    background-color: #e8f4fd;
                }
            """)

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        # 恢复拖拽区域样式
        self.drop_zone.setStyleSheet("""
            QFrame {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        print("拖拽放下事件触发")
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            print(f"拖拽文件路径: {file_path}")
            if os.path.exists(file_path):
                files.append(file_path)

        if files:
            print(f"准备导入 {len(files)} 个文件")
            self.files_dropped.emit(files)
        else:
            print("没有有效的文件")

        # 恢复拖拽区域样式
        self.drop_zone.setStyleSheet("""
            QFrame {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

        event.acceptProposedAction()

class BreadcrumbWidget(QWidget):
    """面包屑导航控件"""

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)

    def set_path(self, path: str):
        """设置路径"""
        # 清空现有控件
        for i in reversed(range(self.layout.count())):
            child = self.layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 添加路径标签
        path_label = QLabel(path)
        path_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        self.layout.addWidget(path_label)

        self.layout.addStretch()

class VirtualGridViewWidget(QScrollArea):
    """虚拟滚动网格视图控件（性能优化）"""

    item_selected = Signal(int)
    selection_changed = Signal(list)

    def __init__(self, theme_manager):
        super().__init__()
        self.theme_manager = theme_manager
        self.items = []
        self.selected_items = []
        self.thumbnail_size = 150

        # 虚拟滚动相关
        self.visible_items = {}  # 当前可见的项目控件
        self.item_height = 200   # 每个项目的高度（包括间距）
        self.items_per_row = 1   # 每行项目数
        self.total_height = 0    # 总内容高度

        # 性能优化
        self.update_timer = QTimer()
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self._update_visible_items)

        self.setup_ui()

class GridViewWidget(QScrollArea):
    """网格视图控件（保持兼容性）"""

    item_selected = Signal(int)
    selection_changed = Signal(list)

    def __init__(self, theme_manager):
        super().__init__()
        self.theme_manager = theme_manager
        self.items = []
        self.selected_items = []
        self.thumbnail_size = 150

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setWidgetResizable(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # 创建内容控件
        self.content_widget = QWidget()
        self.grid_layout = QGridLayout(self.content_widget)
        self.grid_layout.setSpacing(10)
        self.setWidget(self.content_widget)

    def set_items(self, items: List[Dict[str, Any]]):
        """设置项目数据"""
        self.items = items
        self.refresh_view()

    def set_thumbnail_size(self, size: int):
        """设置缩略图大小（流畅缩放优化）"""
        if self.thumbnail_size == size:
            return  # 大小未变化，无需更新

        old_size = self.thumbnail_size
        self.thumbnail_size = size

        # 使用流畅缩放管理器进行批量优化
        try:
            from core.smooth_scaling_manager import get_smooth_scaling_manager

            scaling_manager = get_smooth_scaling_manager()

            # 获取可见文件列表
            visible_files = self._get_visible_files()

            # 通知缩放管理器尺寸变化
            scaling_manager.optimize_for_size_change(old_size, size, visible_files)

            # 连接批量完成信号
            if not hasattr(self, '_batch_scaling_connected'):
                scaling_manager.batch_scaling_completed.connect(self._on_batch_scaling_completed)
                self._batch_scaling_connected = True

            # 使用批量更新模式
            self.setUpdatesEnabled(False)
            try:
                # 只更新现有控件的大小，不重新创建
                self._update_existing_thumbnails_smooth(size)
            finally:
                self.setUpdatesEnabled(True)

        except Exception as e:
            print(f"流畅批量缩放失败，使用原方法: {e}")
            # 回退到原有方法
            self.setUpdatesEnabled(False)
            try:
                self._update_existing_thumbnails(size)
            finally:
                self.setUpdatesEnabled(True)

    def _get_visible_files(self) -> List[str]:
        """获取可见文件列表"""
        visible_files = []
        for i in range(self.grid_layout.count()):
            item = self.grid_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'file_path'):
                    visible_files.append(widget.file_path)
        return visible_files

    def _update_existing_thumbnails_smooth(self, size: int):
        """流畅更新现有缩略图大小"""
        for i in range(self.grid_layout.count()):
            item = self.grid_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'set_thumbnail_size'):
                    # 只更新控件大小，不立即重新生成图片
                    widget.setFixedSize(size + 20, size + 60)
                    if hasattr(widget, 'thumbnail_label'):
                        widget.thumbnail_label.setFixedSize(size, size)
                    widget.thumbnail_size = size

    def _on_batch_scaling_completed(self, results: Dict[str, any]):
        """批量缩放完成回调"""
        # 更新对应的缩略图控件
        for file_path, scaled_pixmap in results.items():
            self._update_thumbnail_widget(file_path, scaled_pixmap)

    def _update_thumbnail_widget(self, file_path: str, scaled_pixmap):
        """更新指定文件的缩略图控件"""
        for i in range(self.grid_layout.count()):
            item = self.grid_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if (hasattr(widget, 'file_path') and
                    widget.file_path == file_path and
                    hasattr(widget, 'thumbnail_label')):
                    widget.thumbnail_label.setPixmap(scaled_pixmap)
                    widget.thumbnail_label.setAlignment(Qt.AlignCenter)
                    break

    def _update_existing_thumbnails(self, size: int):
        """更新现有缩略图大小（避免重新创建控件）"""
        for i in range(self.grid_layout.count()):
            item = self.grid_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'set_thumbnail_size'):
                    widget.set_thumbnail_size(size)

    def refresh_view(self):
        """刷新视图（优化性能）"""
        try:
            # 暂时禁用更新
            self.setUpdatesEnabled(False)

            # 清空现有控件
            for i in reversed(range(self.grid_layout.count())):
                child = self.grid_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)

            # 计算列数
            columns = max(1, self.width() // (self.thumbnail_size + 20))

            # 批量添加项目
            for i, item in enumerate(self.items):
                row = i // columns
                col = i % columns

                item_widget = GridItemWidget(item, self.thumbnail_size, self.theme_manager)
                item_widget.clicked.connect(lambda item_id=item['id']: self.item_selected.emit(item_id))
                self.grid_layout.addWidget(item_widget, row, col)

        finally:
            # 重新启用更新
            self.setUpdatesEnabled(True)

    def select_all(self):
        """全选"""
        self.selected_items = [item.get('id', i) for i, item in enumerate(self.items)]
        self.selection_changed.emit(self.selected_items)

    def invert_selection(self):
        """反选"""
        all_ids = [item.get('id', i) for i, item in enumerate(self.items)]
        self.selected_items = [item_id for item_id in all_ids if item_id not in self.selected_items]
        self.selection_changed.emit(self.selected_items)

    def clear_selection(self):
        """清空选择"""
        self.selected_items = []
        self.selection_changed.emit(self.selected_items)

class ListViewWidget(QListWidget):
    """列表视图控件"""

    item_selected = Signal(int)
    selection_changed = Signal(list)

    def __init__(self, theme_manager):
        super().__init__()
        self.theme_manager = theme_manager
        self.items = []

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setSelectionMode(QListWidget.ExtendedSelection)
        self.itemClicked.connect(self.on_item_clicked)
        self.itemSelectionChanged.connect(self.on_selection_changed)

    def set_items(self, items: List[Dict[str, Any]]):
        """设置项目数据"""
        self.items = items
        self.refresh_view()

    def set_thumbnail_size(self, size: int):
        """设置缩略图大小"""
        # 列表视图不需要调整大小
        pass

    def refresh_view(self):
        """刷新视图"""
        self.clear()

        for item in self.items:
            list_item = QListWidgetItem(f"{item['name']} ({item['file_type']})")
            list_item.setData(Qt.UserRole, item['id'])
            self.addItem(list_item)

    def on_item_clicked(self, item):
        """项目点击处理"""
        item_id = item.data(Qt.UserRole)
        if item_id:
            self.item_selected.emit(item_id)

    def on_selection_changed(self):
        """选择变更处理"""
        selected_items = []
        for item in self.selectedItems():
            item_id = item.data(Qt.UserRole)
            if item_id:
                selected_items.append(item_id)
        self.selection_changed.emit(selected_items)

    def select_all(self):
        """全选"""
        self.selectAll()

    def invert_selection(self):
        """反选"""
        # 获取所有项目
        all_items = [self.item(i) for i in range(self.count())]
        selected_items = self.selectedItems()

        # 清空当前选择
        self.clearSelection()

        # 选择未选中的项目
        for item in all_items:
            if item not in selected_items:
                item.setSelected(True)

    def clear_selection(self):
        """清空选择"""
        self.clearSelection()

class DetailViewWidget(QTableWidget):
    """详细视图控件"""

    item_selected = Signal(int)
    selection_changed = Signal(list)

    def __init__(self, theme_manager):
        super().__init__()
        self.theme_manager = theme_manager
        self.items = []

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 设置表头
        headers = ["名称", "类型", "大小", "尺寸", "创建时间", "评分"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        # 设置选择模式
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.ExtendedSelection)

        # 连接信号
        self.itemClicked.connect(self.on_item_clicked)
        self.itemSelectionChanged.connect(self.on_selection_changed)

    def set_items(self, items: List[Dict[str, Any]]):
        """设置项目数据"""
        self.items = items
        self.refresh_view()

    def set_thumbnail_size(self, size: int):
        """设置缩略图大小"""
        # 详细视图不需要调整大小
        pass

    def refresh_view(self):
        """刷新视图"""
        self.setRowCount(len(self.items))

        for row, item in enumerate(self.items):
            # 名称
            name_item = QTableWidgetItem(item['name'])
            name_item.setData(Qt.UserRole, item['id'])
            self.setItem(row, 0, name_item)

            # 类型
            self.setItem(row, 1, QTableWidgetItem(item['file_type']))

            # 大小
            size_mb = item.get('size', 0) / (1024 * 1024)
            self.setItem(row, 2, QTableWidgetItem(f"{size_mb:.2f} MB"))

            # 尺寸
            width = item.get('width', 0)
            height = item.get('height', 0)
            if width and height:
                dimensions = f"{width}x{height}"
            else:
                dimensions = "-"
            self.setItem(row, 3, QTableWidgetItem(dimensions))

            # 创建时间
            created_time = item.get('created_time', '')
            self.setItem(row, 4, QTableWidgetItem(created_time))

            # 评分
            rating = item.get('rating', 0)
            self.setItem(row, 5, QTableWidgetItem(str(rating)))

        # 调整列宽
        self.resizeColumnsToContents()

    def on_item_clicked(self, item):
        """项目点击处理"""
        item_id = item.data(Qt.UserRole)
        if item_id:
            self.item_selected.emit(item_id)

    def on_selection_changed(self):
        """选择变更处理"""
        selected_items = []
        for item in self.selectedItems():
            if item.column() == 0:  # 只处理第一列的选择
                item_id = item.data(Qt.UserRole)
                if item_id:
                    selected_items.append(item_id)
        self.selection_changed.emit(selected_items)

    def select_all(self):
        """全选"""
        self.selectAll()

    def invert_selection(self):
        """反选"""
        # 获取当前选中的行
        selected_rows = set()
        for item in self.selectedItems():
            selected_rows.add(item.row())

        # 清空选择
        self.clearSelection()

        # 选择未选中的行
        for row in range(self.rowCount()):
            if row not in selected_rows:
                self.selectRow(row)

    def clear_selection(self):
        """清空选择"""
        self.clearSelection()

class GridItemWidget(QWidget):
    """网格项目控件"""

    clicked = Signal()

    def __init__(self, item_data, thumbnail_size, theme_manager):
        super().__init__()
        self.item_data = item_data
        self.thumbnail_size = thumbnail_size
        self.theme_manager = theme_manager

        self.setup_ui()
        self.load_thumbnail()

    def setup_ui(self):
        """设置用户界面"""
        self.setFixedSize(self.thumbnail_size + 20, self.thumbnail_size + 60)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 缩略图
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(self.thumbnail_size, self.thumbnail_size)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #f8f9fa;
            }
        """)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        # 不使用setScaledContents，保持原始比例

        layout.addWidget(self.thumbnail_label)

        # 文件名
        name_label = QLabel(self.item_data['name'])
        name_label.setWordWrap(True)
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setMaximumHeight(40)
        layout.addWidget(name_label)

    def load_thumbnail(self):
        """优化的缩略图加载"""
        try:
            file_path = self.item_data.get('file_path', '')
            file_type = self.item_data.get('file_type', '')
            thumbnail_path = self.item_data.get('thumbnail_path', '')

            # 首先显示占位符，避免空白
            self.show_placeholder()

            # 首先尝试加载已生成的缩略图
            if thumbnail_path and os.path.exists(thumbnail_path):
                # 使用异步加载，避免阻塞UI
                self._load_thumbnail_async(thumbnail_path)
                return

            # 如果是图片文件，异步加载原图
            if file_type == 'image' and os.path.exists(file_path):
                self._load_thumbnail_async(file_path)
                return

            # 根据文件类型显示默认图标
            self.show_default_icon(file_type)

        except Exception as e:
            print(f"加载缩略图失败: {e}")
            self.show_default_icon(self.item_data.get('file_type', ''))

    def show_placeholder(self):
        """显示占位符"""
        self.thumbnail_label.setText("⏳")
        self.thumbnail_label.setFont(QFont("", self.thumbnail_size // 6))
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #ecf0f1;
                color: #7f8c8d;
            }
        """)

    def _load_thumbnail_async(self, file_path: str):
        """异步加载缩略图"""
        # 使用QTimer延迟加载，避免同时加载太多图片
        QTimer.singleShot(10, lambda: self._do_load_thumbnail(file_path))

    def _do_load_thumbnail(self, file_path: str):
        """实际执行缩略图加载"""
        try:
            # 使用更小的尺寸先快速加载
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                # 使用快速变换先显示
                quick_pixmap = pixmap.scaled(
                    self.thumbnail_size, self.thumbnail_size,
                    Qt.KeepAspectRatio,
                    Qt.FastTransformation  # 快速变换
                )
                self.thumbnail_label.setPixmap(quick_pixmap)

                # 保存原始图片
                self.original_pixmap = pixmap

                # 异步生成高质量版本
                QTimer.singleShot(100, lambda: self._generate_high_quality_thumbnail(pixmap))

        except Exception as e:
            print(f"异步加载缩略图失败: {e}")
            self.show_default_icon(self.item_data.get('file_type', ''))

    def _generate_high_quality_thumbnail(self, pixmap: QPixmap):
        """生成高质量缩略图（延迟执行）"""
        try:
            # 生成高质量版本
            hq_pixmap = pixmap.scaled(
                self.thumbnail_size, self.thumbnail_size,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation  # 高质量变换
            )

            # 更新显示
            self.thumbnail_label.setPixmap(hq_pixmap)

        except Exception as e:
            print(f"生成高质量缩略图失败: {e}")

    def _set_thumbnail_pixmap(self, pixmap):
        """设置缩略图，充满显示区域"""
        # 计算合适的缩放尺寸，充满整个区域
        original_size = pixmap.size()
        target_size = self.thumbnail_size

        # 计算缩放比例 - 使用max确保充满整个区域
        scale_w = target_size / original_size.width()
        scale_h = target_size / original_size.height()
        scale = max(scale_w, scale_h)  # 改为max，确保充满

        # 计算实际显示尺寸
        new_width = int(original_size.width() * scale)
        new_height = int(original_size.height() * scale)

        # 使用高质量缩放
        scaled_pixmap = pixmap.scaled(
            new_width, new_height,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )

        # 如果缩放后超出目标尺寸，进行居中裁剪
        if new_width > target_size or new_height > target_size:
            # 计算裁剪位置（居中）
            crop_x = max(0, (new_width - target_size) // 2)
            crop_y = max(0, (new_height - target_size) // 2)

            # 裁剪到目标尺寸
            scaled_pixmap = scaled_pixmap.copy(
                crop_x, crop_y,
                min(target_size, new_width),
                min(target_size, new_height)
            )

        # 设置到标签
        self.thumbnail_label.setPixmap(scaled_pixmap)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)

        # 确保标签不会再次缩放内容
        self.thumbnail_label.setScaledContents(False)

        # 保存原始图片用于大小调整
        self.original_pixmap = pixmap

    def set_thumbnail_size(self, size: int):
        """更新缩略图大小（流畅缩放优化）"""
        if self.thumbnail_size == size:
            return

        old_size = self.thumbnail_size
        self.thumbnail_size = size

        # 更新控件大小
        self.setFixedSize(size + 20, size + 60)
        self.thumbnail_label.setFixedSize(size, size)

        # 使用流畅缩放管理器
        try:
            from core.smooth_scaling_manager import get_smooth_scaling_manager

            scaling_manager = get_smooth_scaling_manager()

            # 请求新尺寸的缩放图片
            success = scaling_manager.request_scaling(self.file_path, size, 'urgent')

            if success:
                # 连接缩放完成信号（如果还没连接）
                if not hasattr(self, '_scaling_connected'):
                    scaling_manager.scaling_completed.connect(self._on_scaling_completed)
                    self._scaling_connected = True
            else:
                # 回退到原有方法
                self._fallback_resize(old_size)

        except Exception as e:
            print(f"流畅缩放失败，使用回退方法: {e}")
            self._fallback_resize(old_size)

    def _fallback_resize(self, old_size: int):
        """回退的缩放方法"""
        # 重新缩放现有图片
        if hasattr(self, 'original_pixmap') and self.original_pixmap:
            self._set_thumbnail_pixmap(self.original_pixmap)
        else:
            # 如果没有原始图片，重新加载
            self.load_thumbnail()

    def _on_scaling_completed(self, file_path: str, target_size: int, scaled_pixmap):
        """缩放完成回调"""
        # 检查是否是当前文件和当前尺寸
        if (file_path == self.file_path and
            target_size == self.thumbnail_size and
            not scaled_pixmap.isNull()):

            # 直接设置缩放后的图片
            self.thumbnail_label.setPixmap(scaled_pixmap)
            self.thumbnail_label.setAlignment(Qt.AlignCenter)

    def show_default_icon(self, file_type):
        """显示默认图标"""
        icon_map = {
            'image': '🖼️',
            'video': '🎬',
            'audio': '🎵',
            'document': '📄',
            'design': '🎨',
            'other': '📁'
        }

        icon = icon_map.get(file_type, '📁')
        self.thumbnail_label.setText(icon)
        self.thumbnail_label.setFont(QFont("", self.thumbnail_size // 4))

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.LeftButton:
            # 如果是图片文件，打开图片查看器
            file_type = self.item_data.get('file_type', '')
            if file_type == 'image':
                file_path = self.item_data.get('file_path', '')
                if os.path.exists(file_path):
                    self.show_image_viewer(file_path)
        super().mouseDoubleClickEvent(event)

    def show_image_viewer(self, image_path):
        """显示图片查看器"""
        from ui.dialogs.image_viewer_dialog import ImageViewerDialog
        viewer = ImageViewerDialog(image_path, self)
        viewer.exec()
