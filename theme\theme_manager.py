# 主题管理模块
# 功能：全局主题设置和管理，支持浅色和深色主题切换，统一管理应用程序的颜色方案

from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QWidget
from PySide6.QtGui import QPalette, QColor
from typing import Dict, Any
import json
from pathlib import Path

class ThemeManager(QObject):
    """主题管理器类"""
    
    # 主题变更信号
    theme_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.themes = self._load_theme_definitions()
        
    def _load_theme_definitions(self) -> Dict[str, Dict[str, Any]]:
        """加载主题定义"""
        return {
            "light": {
                # 主色调
                "primary": "#3498db",
                "primary_light": "#5dade2", 
                "primary_dark": "#2980b9",
                
                # 背景色系
                "background": "#f8f9fa",
                "surface": "#ffffff",
                "surface_variant": "#ecf0f1",
                
                # 文字色系
                "text_primary": "#2c3e50",
                "text_secondary": "#7f8c8d",
                "text_disabled": "#95a5a6",
                
                # 状态色系
                "success": "#27ae60",
                "warning": "#f39c12",
                "error": "#e74c3c",
                "info": "#3498db",
                
                # 边框和分割线
                "border": "#bdc3c7",
                "divider": "#ecf0f1",
                
                # 标题栏
                "titlebar": "#2c3e50",
                "titlebar_text": "#ffffff",
                
                # 工具栏
                "toolbar": "#ecf0f1",
                "toolbar_text": "#2c3e50",
                
                # 侧边栏
                "sidebar": "#ffffff",
                "sidebar_text": "#2c3e50",
                "sidebar_selected": "#e8f4fd",
                
                # 按钮
                "button_normal": "#3498db",
                "button_hover": "#5dade2",
                "button_pressed": "#2980b9",
                "button_text": "#ffffff",
                
                # 输入框
                "input_background": "#ffffff",
                "input_border": "#bdc3c7",
                "input_focus": "#3498db",
                "input_text": "#2c3e50",
                
                # 卡片
                "card_background": "#ffffff",
                "card_border": "#e9ecef",
                "card_shadow": "rgba(0, 0, 0, 0.1)",
                
                # 滚动条
                "scrollbar_background": "#f8f9fa",
                "scrollbar_handle": "#bdc3c7",
                "scrollbar_handle_hover": "#95a5a6"
            },
            
            "dark": {
                # 主色调
                "primary": "#3498db",
                "primary_light": "#5dade2",
                "primary_dark": "#2980b9",
                
                # 背景色系
                "background": "#1e1e1e",
                "surface": "#2d2d2d",
                "surface_variant": "#404040",
                
                # 文字色系
                "text_primary": "#ffffff",
                "text_secondary": "#b0b0b0",
                "text_disabled": "#707070",
                
                # 状态色系
                "success": "#27ae60",
                "warning": "#f39c12",
                "error": "#e74c3c",
                "info": "#3498db",
                
                # 边框和分割线
                "border": "#555555",
                "divider": "#404040",
                
                # 标题栏
                "titlebar": "#1e1e1e",
                "titlebar_text": "#ffffff",
                
                # 工具栏
                "toolbar": "#2d2d2d",
                "toolbar_text": "#ffffff",
                
                # 侧边栏
                "sidebar": "#2d2d2d",
                "sidebar_text": "#ffffff",
                "sidebar_selected": "#404040",
                
                # 按钮
                "button_normal": "#3498db",
                "button_hover": "#5dade2",
                "button_pressed": "#2980b9",
                "button_text": "#ffffff",
                
                # 输入框
                "input_background": "#404040",
                "input_border": "#555555",
                "input_focus": "#3498db",
                "input_text": "#ffffff",
                
                # 卡片
                "card_background": "#2d2d2d",
                "card_border": "#404040",
                "card_shadow": "rgba(0, 0, 0, 0.3)",
                
                # 滚动条
                "scrollbar_background": "#2d2d2d",
                "scrollbar_handle": "#555555",
                "scrollbar_handle_hover": "#777777"
            }
        }
    
    def load_theme(self, theme_name: str):
        """加载指定主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)
        else:
            print(f"主题 '{theme_name}' 不存在，使用默认主题")
            self.current_theme = "light"
            
    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme
        
    def get_color(self, color_key: str) -> str:
        """获取当前主题的颜色值"""
        theme_colors = self.themes.get(self.current_theme, self.themes["light"])
        return theme_colors.get(color_key, "#000000")
        
    def get_theme_colors(self) -> Dict[str, str]:
        """获取当前主题的所有颜色"""
        return self.themes.get(self.current_theme, self.themes["light"])
        
    def apply_theme_to_widget(self, widget: QWidget):
        """将当前主题应用到指定控件"""
        colors = self.get_theme_colors()
        
        # 生成样式表
        stylesheet = self._generate_stylesheet(colors)
        
        # 应用样式表
        widget.setStyleSheet(stylesheet)
        
    def _generate_stylesheet(self, colors: Dict[str, str]) -> str:
        """生成完整的样式表"""
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}
        
        /* 工具栏样式 */
        QToolBar {{
            background-color: {colors['toolbar']};
            border: none;
            spacing: 5px;
            padding: 5px;
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {colors['button_normal']};
            color: {colors['button_text']};
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {colors['button_hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {colors['button_pressed']};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['text_disabled']};
            color: {colors['background']};
        }}
        
        /* 输入框样式 */
        QLineEdit {{
            background-color: {colors['input_background']};
            border: 1px solid {colors['input_border']};
            border-radius: 4px;
            padding: 8px;
            color: {colors['input_text']};
        }}
        
        QLineEdit:focus {{
            border-color: {colors['input_focus']};
        }}
        
        /* 列表控件样式 */
        QListWidget {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            color: {colors['text_primary']};
        }}
        
        QListWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {colors['divider']};
        }}
        
        QListWidget::item:selected {{
            background-color: {colors['sidebar_selected']};
        }}
        
        QListWidget::item:hover {{
            background-color: {colors['surface_variant']};
        }}
        
        /* 树形控件样式 */
        QTreeWidget {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            color: {colors['text_primary']};
        }}
        
        QTreeWidget::item {{
            padding: 4px;
        }}
        
        QTreeWidget::item:selected {{
            background-color: {colors['sidebar_selected']};
        }}
        
        QTreeWidget::item:hover {{
            background-color: {colors['surface_variant']};
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {colors['scrollbar_background']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['scrollbar_handle']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['scrollbar_handle_hover']};
        }}
        
        QScrollBar:horizontal {{
            background-color: {colors['scrollbar_background']};
            height: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {colors['scrollbar_handle']};
            border-radius: 6px;
            min-width: 20px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {colors['scrollbar_handle_hover']};
        }}
        
        /* 标签页样式 */
        QTabWidget::pane {{
            border: 1px solid {colors['border']};
            background-color: {colors['surface']};
        }}
        
        QTabBar::tab {{
            background-color: {colors['surface_variant']};
            color: {colors['text_primary']};
            padding: 8px 16px;
            margin-right: 2px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors['surface']};
            border-bottom: 2px solid {colors['primary']};
        }}
        
        QTabBar::tab:hover {{
            background-color: {colors['primary_light']};
            color: {colors['button_text']};
        }}
        
        /* 状态栏样式 */
        QStatusBar {{
            background-color: {colors['toolbar']};
            color: {colors['toolbar_text']};
            border-top: 1px solid {colors['border']};
        }}
        
        /* 菜单样式 */
        QMenuBar {{
            background-color: {colors['toolbar']};
            color: {colors['toolbar_text']};
        }}
        
        QMenuBar::item {{
            padding: 4px 8px;
        }}
        
        QMenuBar::item:selected {{
            background-color: {colors['primary']};
            color: {colors['button_text']};
        }}
        
        QMenu {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            color: {colors['text_primary']};
        }}
        
        QMenu::item {{
            padding: 6px 20px;
        }}
        
        QMenu::item:selected {{
            background-color: {colors['primary']};
            color: {colors['button_text']};
        }}
        """
        
    def switch_theme(self):
        """切换主题"""
        new_theme = "dark" if self.current_theme == "light" else "light"
        self.load_theme(new_theme)
        
    def get_available_themes(self) -> list:
        """获取可用主题列表"""
        return list(self.themes.keys())
