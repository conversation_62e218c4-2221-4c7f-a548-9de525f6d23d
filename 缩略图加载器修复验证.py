#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缩略图加载器修复验证工具
验证修复后的缩略图加载器资源管理是否正常
"""

import sys
import time
import gc
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

class ThumbnailLoaderTestWindow(QMainWindow):
    """缩略图加载器测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("缩略图加载器修复验证")
        self.setGeometry(100, 100, 800, 600)
        
        self.test_loaders = []
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔧 缩略图加载器修复验证")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 测试按钮
        test_btn = QPushButton("🧪 开始资源管理测试")
        test_btn.clicked.connect(self.run_resource_test)
        layout.addWidget(test_btn)
        
        stress_btn = QPushButton("⚡ 压力测试")
        stress_btn.clicked.connect(self.run_stress_test)
        layout.addWidget(stress_btn)
        
        cleanup_btn = QPushButton("🧹 清理测试")
        cleanup_btn.clicked.connect(self.run_cleanup_test)
        layout.addWidget(cleanup_btn)
        
        gc_btn = QPushButton("🗑️ 强制垃圾回收")
        gc_btn.clicked.connect(self.force_gc)
        layout.addWidget(gc_btn)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.result_text)
    
    def log(self, message):
        """记录日志"""
        self.result_text.append(f"[{time.strftime('%H:%M:%S')}] {message}")
        QApplication.processEvents()
    
    def run_resource_test(self):
        """运行资源管理测试"""
        self.result_text.clear()
        self.log("🧪 开始缩略图加载器资源管理测试")
        
        try:
            from ui.components.content_area import HighPerformanceThumbnailLoader
            
            # 测试1: 正常创建和销毁
            self.log("\n📝 测试1: 正常创建和销毁")
            loader = HighPerformanceThumbnailLoader()
            self.log("✅ 加载器创建成功")
            
            # 检查资源管理标志
            if hasattr(loader, '_is_destroyed'):
                self.log("✅ 资源管理标志存在")
            else:
                self.log("❌ 资源管理标志缺失")
            
            # 检查安全清理方法
            if hasattr(loader, 'safe_cleanup'):
                self.log("✅ 安全清理方法存在")
            else:
                self.log("❌ 安全清理方法缺失")
            
            # 启动加载器
            loader.start()
            self.log("✅ 加载器启动成功")
            
            # 等待一下
            time.sleep(0.5)
            
            # 安全清理
            loader.safe_cleanup()
            self.log("✅ 安全清理完成")
            
            # 测试2: 重复清理
            self.log("\n📝 测试2: 重复清理测试")
            try:
                loader.safe_cleanup()  # 再次清理
                self.log("✅ 重复清理不会出错")
            except Exception as e:
                self.log(f"❌ 重复清理出错: {e}")
            
            # 测试3: 析构函数
            self.log("\n📝 测试3: 析构函数测试")
            del loader
            self.log("✅ 析构函数调用完成")
            
        except Exception as e:
            self.log(f"❌ 测试失败: {e}")
            import traceback
            self.log(traceback.format_exc())
    
    def run_stress_test(self):
        """运行压力测试"""
        self.log("\n⚡ 开始压力测试")
        
        try:
            from ui.components.content_area import HighPerformanceThumbnailLoader
            
            # 创建多个加载器
            loaders = []
            for i in range(10):
                loader = HighPerformanceThumbnailLoader()
                loader.start()
                loaders.append(loader)
                self.log(f"创建加载器 {i+1}/10")
            
            self.log("✅ 10个加载器创建完成")
            
            # 等待一下
            time.sleep(1)
            
            # 逐个清理
            for i, loader in enumerate(loaders):
                try:
                    loader.safe_cleanup()
                    self.log(f"清理加载器 {i+1}/10")
                except Exception as e:
                    self.log(f"❌ 清理加载器 {i+1} 失败: {e}")
            
            # 清空列表
            loaders.clear()
            self.log("✅ 压力测试完成")
            
        except Exception as e:
            self.log(f"❌ 压力测试失败: {e}")
    
    def run_cleanup_test(self):
        """运行清理测试"""
        self.log("\n🧹 开始清理测试")
        
        try:
            from ui.components.content_area import HighPerformanceThumbnailLoader
            
            # 创建加载器并立即销毁
            for i in range(5):
                loader = HighPerformanceThumbnailLoader()
                loader.start()
                
                # 模拟一些工作
                loader.load_thumbnail("test_file.jpg", priority=1)
                
                # 立即清理
                loader.safe_cleanup()
                del loader
                
                self.log(f"快速创建销毁测试 {i+1}/5")
            
            self.log("✅ 快速创建销毁测试完成")
            
            # 测试ContentAreaWidget的清理
            self.log("\n📝 测试ContentAreaWidget清理")
            try:
                from theme.theme_manager import ThemeManager
                from database.db_manager import DatabaseManager
                from utils.config_manager import ConfigManager
                from ui.components.content_area import ContentAreaWidget
                
                theme_manager = ThemeManager()
                db_manager = DatabaseManager()
                config_manager = ConfigManager()
                
                content_area = ContentAreaWidget(theme_manager, db_manager, config_manager)
                self.log("✅ ContentAreaWidget创建成功")
                
                # 清理资源
                content_area.cleanup_resources()
                self.log("✅ ContentAreaWidget清理成功")
                
                del content_area
                self.log("✅ ContentAreaWidget销毁成功")
                
            except Exception as e:
                self.log(f"❌ ContentAreaWidget测试失败: {e}")
            
        except Exception as e:
            self.log(f"❌ 清理测试失败: {e}")
    
    def force_gc(self):
        """强制垃圾回收"""
        self.log("\n🗑️ 执行强制垃圾回收")
        
        # 执行垃圾回收
        collected = gc.collect()
        self.log(f"✅ 垃圾回收完成，回收了 {collected} 个对象")
        
        # 显示垃圾回收统计
        stats = gc.get_stats()
        for i, stat in enumerate(stats):
            self.log(f"  代 {i}: 收集次数={stat['collections']}, 对象数={stat.get('collected', 0)}")
        
        # 显示当前对象数量
        object_count = len(gc.get_objects())
        self.log(f"  当前对象总数: {object_count}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = ThumbnailLoaderTestWindow()
    window.show()
    
    print("缩略图加载器修复验证工具启动成功！")
    print("功能特性：")
    print("1. 🧪 资源管理测试 - 验证创建、清理、销毁流程")
    print("2. ⚡ 压力测试 - 大量创建和销毁测试")
    print("3. 🧹 清理测试 - 快速创建销毁和组件清理测试")
    print("4. 🗑️ 垃圾回收 - 强制垃圾回收和统计")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
