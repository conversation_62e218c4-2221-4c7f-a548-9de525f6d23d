#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片压缩测试工具
测试大尺寸图片智能压缩功能和性能
"""

import sys
import time
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                               QProgressBar, QFileDialog, QComboBox, QSpinBox,
                               QGroupBox, QGridLayout, QCheckBox)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont, QPixmap

from core.image_compressor import SmartImageCompressor, CompressionConfig, CompressionLevel
from core.async_image_processor import AsyncImageProcessor, ProcessingPriority

class ImageCompressionTestThread(QThread):
    """图片压缩测试线程"""
    
    progress_updated = Signal(int, str)
    test_completed = Signal(dict)
    
    def __init__(self, test_files, config):
        super().__init__()
        self.test_files = test_files
        self.config = config
        self.results = {}
    
    def run(self):
        """运行测试"""
        compressor = SmartImageCompressor(self.config)
        total_files = len(self.test_files)
        
        for i, file_path in enumerate(self.test_files):
            try:
                self.progress_updated.emit(int((i / total_files) * 100), f"正在处理: {Path(file_path).name}")
                
                # 分析图片
                start_time = time.time()
                image_info = compressor.analyze_image(file_path)
                analysis_time = time.time() - start_time
                
                # 压缩图片
                start_time = time.time()
                compressed_pixmap = compressor.compress_for_thumbnail(file_path)
                compression_time = time.time() - start_time
                
                # 记录结果
                self.results[file_path] = {
                    'original_size': image_info.original_size,
                    'file_size': image_info.file_size,
                    'needs_compression': image_info.needs_compression,
                    'compression_ratio': image_info.compression_ratio,
                    'estimated_memory': image_info.estimated_memory,
                    'analysis_time': analysis_time,
                    'compression_time': compression_time,
                    'compressed_success': not compressed_pixmap.isNull(),
                    'compressed_size': (compressed_pixmap.width(), compressed_pixmap.height()) if not compressed_pixmap.isNull() else (0, 0)
                }
                
            except Exception as e:
                self.results[file_path] = {
                    'error': str(e)
                }
        
        self.progress_updated.emit(100, "测试完成")
        self.test_completed.emit(self.results)

class ImageCompressionTestWindow(QMainWindow):
    """图片压缩测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🖼️ 图片压缩测试工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.test_files = []
        self.test_thread = None
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🖼️ 大尺寸图片智能压缩测试")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 配置区域
        config_group = self.create_config_group()
        layout.addWidget(config_group)
        
        # 文件选择区域
        file_group = self.create_file_group()
        layout.addWidget(file_group)
        
        # 测试控制区域
        control_group = self.create_control_group()
        layout.addWidget(control_group)
        
        # 结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.result_text)
    
    def create_config_group(self):
        """创建配置组"""
        group = QGroupBox("⚙️ 压缩配置")
        layout = QGridLayout(group)
        
        # 最大宽度
        layout.addWidget(QLabel("最大宽度:"), 0, 0)
        self.max_width_spin = QSpinBox()
        self.max_width_spin.setRange(100, 4000)
        self.max_width_spin.setValue(1920)
        layout.addWidget(self.max_width_spin, 0, 1)
        
        # 最大高度
        layout.addWidget(QLabel("最大高度:"), 0, 2)
        self.max_height_spin = QSpinBox()
        self.max_height_spin.setRange(100, 4000)
        self.max_height_spin.setValue(1080)
        layout.addWidget(self.max_height_spin, 0, 3)
        
        # 缩略图大小
        layout.addWidget(QLabel("缩略图大小:"), 1, 0)
        self.thumbnail_size_spin = QSpinBox()
        self.thumbnail_size_spin.setRange(50, 500)
        self.thumbnail_size_spin.setValue(200)
        layout.addWidget(self.thumbnail_size_spin, 1, 1)
        
        # JPEG质量
        layout.addWidget(QLabel("JPEG质量:"), 1, 2)
        self.jpeg_quality_spin = QSpinBox()
        self.jpeg_quality_spin.setRange(10, 100)
        self.jpeg_quality_spin.setValue(85)
        layout.addWidget(self.jpeg_quality_spin, 1, 3)
        
        # 压缩级别
        layout.addWidget(QLabel("压缩级别:"), 2, 0)
        self.compression_level_combo = QComboBox()
        self.compression_level_combo.addItems([
            "ultra_fast", "fast", "balanced", "high_quality"
        ])
        self.compression_level_combo.setCurrentText("balanced")
        layout.addWidget(self.compression_level_combo, 2, 1)
        
        # 启用缓存
        self.enable_cache_check = QCheckBox("启用缓存")
        self.enable_cache_check.setChecked(True)
        layout.addWidget(self.enable_cache_check, 2, 2)
        
        return group
    
    def create_file_group(self):
        """创建文件组"""
        group = QGroupBox("📁 测试文件")
        layout = QHBoxLayout(group)
        
        # 文件列表标签
        self.file_count_label = QLabel("已选择 0 个文件")
        layout.addWidget(self.file_count_label)
        
        layout.addStretch()
        
        # 选择文件按钮
        select_files_btn = QPushButton("📂 选择图片文件")
        select_files_btn.clicked.connect(self.select_files)
        layout.addWidget(select_files_btn)
        
        # 选择文件夹按钮
        select_folder_btn = QPushButton("📁 选择图片文件夹")
        select_folder_btn.clicked.connect(self.select_folder)
        layout.addWidget(select_folder_btn)
        
        # 生成测试图片按钮
        generate_test_btn = QPushButton("🎨 生成测试图片")
        generate_test_btn.clicked.connect(self.generate_test_images)
        layout.addWidget(generate_test_btn)
        
        return group
    
    def create_control_group(self):
        """创建控制组"""
        group = QGroupBox("🎯 测试控制")
        layout = QHBoxLayout(group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 开始测试按钮
        self.start_test_btn = QPushButton("🚀 开始压缩测试")
        self.start_test_btn.clicked.connect(self.start_test)
        layout.addWidget(self.start_test_btn)
        
        # 异步测试按钮
        self.async_test_btn = QPushButton("⚡ 异步处理测试")
        self.async_test_btn.clicked.connect(self.start_async_test)
        layout.addWidget(self.async_test_btn)
        
        # 清空结果按钮
        clear_btn = QPushButton("🗑️ 清空结果")
        clear_btn.clicked.connect(self.clear_results)
        layout.addWidget(clear_btn)
        
        return group
    
    def select_files(self):
        """选择文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择图片文件", "", 
            "图片文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.webp);;所有文件 (*)"
        )
        
        if files:
            self.test_files = files
            self.file_count_label.setText(f"已选择 {len(files)} 个文件")
    
    def select_folder(self):
        """选择文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择图片文件夹")
        
        if folder:
            # 扫描文件夹中的图片文件
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
            files = []
            
            for file_path in Path(folder).rglob('*'):
                if file_path.suffix.lower() in image_extensions:
                    files.append(str(file_path))
            
            self.test_files = files
            self.file_count_label.setText(f"已选择 {len(files)} 个文件")
    
    def generate_test_images(self):
        """生成测试图片"""
        from PySide6.QtGui import QPixmap, QPainter, QColor, QFont
        
        test_dir = Path("test_images")
        test_dir.mkdir(exist_ok=True)
        
        # 生成不同尺寸的测试图片
        test_sizes = [
            (800, 600, "小尺寸"),
            (1920, 1080, "标准尺寸"),
            (2560, 1440, "2K尺寸"),
            (3840, 2160, "4K尺寸"),
            (5120, 2880, "5K尺寸")
        ]
        
        generated_files = []
        
        for width, height, name in test_sizes:
            # 创建测试图片
            pixmap = QPixmap(width, height)
            pixmap.fill(QColor(100, 150, 200))
            
            # 绘制文字
            painter = QPainter(pixmap)
            painter.setPen(QColor(255, 255, 255))
            painter.setFont(QFont("Arial", max(20, width // 100)))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, f"{name}\n{width}x{height}")
            painter.end()
            
            # 保存文件
            file_path = test_dir / f"test_{width}x{height}.png"
            pixmap.save(str(file_path))
            generated_files.append(str(file_path))
        
        self.test_files = generated_files
        self.file_count_label.setText(f"已生成 {len(generated_files)} 个测试文件")
        self.log(f"✅ 已生成 {len(generated_files)} 个测试图片到 {test_dir}")
    
    def get_config(self):
        """获取配置"""
        level_map = {
            "ultra_fast": CompressionLevel.ULTRA_FAST,
            "fast": CompressionLevel.FAST,
            "balanced": CompressionLevel.BALANCED,
            "high_quality": CompressionLevel.HIGH_QUALITY
        }
        
        return CompressionConfig(
            max_width=self.max_width_spin.value(),
            max_height=self.max_height_spin.value(),
            thumbnail_size=self.thumbnail_size_spin.value(),
            jpeg_quality=self.jpeg_quality_spin.value(),
            compression_level=level_map[self.compression_level_combo.currentText()],
            enable_cache=self.enable_cache_check.isChecked()
        )
    
    def start_test(self):
        """开始测试"""
        if not self.test_files:
            self.log("❌ 请先选择测试文件")
            return
        
        self.result_text.clear()
        self.log("🚀 开始图片压缩测试...")
        
        config = self.get_config()
        self.log(f"📋 测试配置:")
        self.log(f"  • 最大尺寸: {config.max_width}x{config.max_height}")
        self.log(f"  • 缩略图大小: {config.thumbnail_size}px")
        self.log(f"  • 压缩级别: {config.compression_level.value}")
        self.log(f"  • JPEG质量: {config.jpeg_quality}")
        self.log(f"  • 启用缓存: {config.enable_cache}")
        
        # 启动测试线程
        self.test_thread = ImageCompressionTestThread(self.test_files, config)
        self.test_thread.progress_updated.connect(self.on_progress_updated)
        self.test_thread.test_completed.connect(self.on_test_completed)
        self.test_thread.start()
        
        self.start_test_btn.setEnabled(False)
    
    def start_async_test(self):
        """开始异步测试"""
        if not self.test_files:
            self.log("❌ 请先选择测试文件")
            return
        
        self.result_text.clear()
        self.log("⚡ 开始异步图片处理测试...")
        
        config = self.get_config()
        processor = AsyncImageProcessor(config, max_workers=4)
        
        # 连接信号
        processor.image_processed.connect(self.on_async_image_processed)
        processor.processing_progress.connect(self.on_async_progress)
        processor.queue_status_changed.connect(self.on_queue_status_changed)
        
        # 添加任务
        added_count = processor.add_batch_tasks(self.test_files, ProcessingPriority.HIGH)
        self.log(f"📝 已添加 {added_count} 个处理任务")
        
        self.async_test_btn.setEnabled(False)
        
        # 5秒后显示统计
        QTimer.singleShot(5000, lambda: self.show_async_stats(processor))
    
    def on_progress_updated(self, progress, message):
        """进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
    
    def on_test_completed(self, results):
        """测试完成"""
        self.log("\n📊 测试结果统计:")
        
        total_files = len(results)
        compressed_files = sum(1 for r in results.values() if r.get('needs_compression', False))
        successful_files = sum(1 for r in results.values() if r.get('compressed_success', False))
        error_files = sum(1 for r in results.values() if 'error' in r)
        
        total_original_memory = sum(r.get('estimated_memory', 0) for r in results.values()) / 1024 / 1024
        total_compression_time = sum(r.get('compression_time', 0) for r in results.values())
        
        self.log(f"  • 总文件数: {total_files}")
        self.log(f"  • 需要压缩: {compressed_files}")
        self.log(f"  • 压缩成功: {successful_files}")
        self.log(f"  • 处理失败: {error_files}")
        self.log(f"  • 原始内存占用: {total_original_memory:.1f} MB")
        self.log(f"  • 总压缩时间: {total_compression_time:.3f} 秒")
        
        if compressed_files > 0:
            avg_compression_time = total_compression_time / compressed_files
            self.log(f"  • 平均压缩时间: {avg_compression_time:.3f} 秒/文件")
        
        # 详细结果
        self.log("\n📋 详细结果:")
        for file_path, result in results.items():
            file_name = Path(file_path).name
            if 'error' in result:
                self.log(f"  ❌ {file_name}: {result['error']}")
            else:
                original_size = result.get('original_size', (0, 0))
                compressed_size = result.get('compressed_size', (0, 0))
                compression_time = result.get('compression_time', 0)
                
                self.log(f"  ✅ {file_name}: {original_size} → {compressed_size} ({compression_time:.3f}s)")
        
        self.start_test_btn.setEnabled(True)
        self.status_label.setText("测试完成")
    
    def on_async_image_processed(self, file_path, pixmap, stats):
        """异步图片处理完成"""
        file_name = Path(file_path).name
        processing_time = stats.get('processing_time', 0)
        priority = stats.get('priority', 'UNKNOWN')
        
        self.log(f"  ✅ {file_name}: 处理完成 ({processing_time:.3f}s, {priority})")
    
    def on_async_progress(self, progress, total):
        """异步处理进度"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"异步处理进度: {progress}% ({total} 任务)")
    
    def on_queue_status_changed(self, queue_size, processing_count, completed_count):
        """队列状态变化"""
        if queue_size == 0 and processing_count == 0:
            self.async_test_btn.setEnabled(True)
            self.status_label.setText("异步测试完成")
    
    def show_async_stats(self, processor):
        """显示异步统计"""
        stats = processor.get_processing_stats()
        
        self.log("\n📊 异步处理统计:")
        proc_stats = stats['processor_stats']
        comp_stats = stats['compression_stats']
        
        self.log(f"  • 队列任务: {proc_stats['total_queued']}")
        self.log(f"  • 已处理: {proc_stats['total_processed']}")
        self.log(f"  • 处理中: {proc_stats['processing_tasks']}")
        self.log(f"  • 已完成: {proc_stats['completed_tasks']}")
        self.log(f"  • 错误率: {proc_stats['error_rate']:.1f}%")
        self.log(f"  • 平均处理时间: {proc_stats['average_processing_time']:.3f}s")
        self.log(f"  • 缓存命中率: {comp_stats['cache_hit_rate']:.1f}%")
        self.log(f"  • 节省内存: {comp_stats['total_memory_saved_mb']:.1f} MB")
    
    def clear_results(self):
        """清空结果"""
        self.result_text.clear()
        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")
    
    def log(self, message):
        """记录日志"""
        self.result_text.append(f"[{time.strftime('%H:%M:%S')}] {message}")
        QApplication.processEvents()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = ImageCompressionTestWindow()
    window.show()
    
    print("图片压缩测试工具启动成功！")
    print("功能特性：")
    print("1. 🖼️ 大尺寸图片智能压缩测试")
    print("2. ⚙️ 多种压缩级别和配置选项")
    print("3. ⚡ 异步处理性能测试")
    print("4. 📊 详细的性能统计和分析")
    print("5. 🎨 自动生成测试图片")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
