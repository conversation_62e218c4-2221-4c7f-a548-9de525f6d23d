#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入分类功能完整测试工具
测试所有修复的功能：
1. 导入时弹出分类选择对话框
2. 分类名称显示文件数量
3. 素材移动到其他分类
4. 分类管理中的文件数更新
"""

import sys
import time
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                               QHBoxLayout, QLabel, QPushButton, QTextEdit,
                               QGroupBox, QListWidget, QListWidgetItem, QCheckBox,
                               QSpinBox, QProgressBar, QMessageBox, QFileDialog,
                               QTabWidget)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class ImportCategoryCompleteTestWindow(QMainWindow):
    """导入分类功能完整测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 导入分类功能完整测试工具")
        self.setGeometry(100, 100, 1200, 800)

        self.test_files = []
        self.setup_ui()
        self.create_test_files()

    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 标题
        title = QLabel("🔧 导入分类功能完整测试工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 修复说明
        desc_group = QGroupBox("🎯 测试目标")
        desc_layout = QVBoxLayout(desc_group)

        desc_text = QLabel("""
✅ 问题1: 导入素材时没有弹出添加到哪个分类，而是直接添加到了临时分组
✅ 问题2: 分类名称后面要显示该分类下有多少文件
✅ 问题3: 现在不支持批量或者将单个素材移动到其他分类
✅ 问题4: 分类管理中的文件数没有进行更新

🧪 测试内容:
• 拖拽导入时弹出分类选择对话框
• 分类树显示文件数量 (分类名 (文件数))
• 选中素材后可以移动到其他分类
• 移动后分类文件数自动更新
        """)
        desc_text.setWordWrap(True)
        desc_text.setStyleSheet("color: #333; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        desc_layout.addWidget(desc_text)

        layout.addWidget(desc_group)

        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # 测试1: 导入分类选择
        import_tab = self.create_import_test_tab()
        tab_widget.addTab(import_tab, "1️⃣ 导入分类测试")

        # 测试2: 文件数量显示
        count_tab = self.create_count_test_tab()
        tab_widget.addTab(count_tab, "2️⃣ 文件数量测试")

        # 测试3: 素材移动
        move_tab = self.create_move_test_tab()
        tab_widget.addTab(move_tab, "3️⃣ 素材移动测试")

        # 测试4: 完整流程
        complete_tab = self.create_complete_test_tab()
        tab_widget.addTab(complete_tab, "4️⃣ 完整流程测试")

        # 测试日志
        log_group = QGroupBox("📝 测试日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)

        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_log_btn)

        layout.addWidget(log_group)

    def create_import_test_tab(self):
        """创建导入测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明
        desc = QLabel("测试导入时是否弹出分类选择对话框")
        desc.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(desc)

        # 测试文件准备
        file_group = QGroupBox("📁 测试文件准备")
        file_layout = QVBoxLayout(file_group)

        create_files_btn = QPushButton("📁 创建测试文件")
        create_files_btn.clicked.connect(self.create_test_files)
        file_layout.addWidget(create_files_btn)

        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(100)
        file_layout.addWidget(self.file_list)

        layout.addWidget(file_group)

        # 导入测试
        import_group = QGroupBox("📥 导入测试")
        import_layout = QVBoxLayout(import_group)

        test_dialog_btn = QPushButton("🗂️ 测试分类选择对话框")
        test_dialog_btn.clicked.connect(self.test_import_dialog)
        test_dialog_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        import_layout.addWidget(test_dialog_btn)

        test_drag_btn = QPushButton("🖱️ 测试拖拽导入（打开主程序）")
        test_drag_btn.clicked.connect(self.test_drag_import)
        import_layout.addWidget(test_drag_btn)

        layout.addWidget(import_group)

        return widget

    def create_count_test_tab(self):
        """创建文件数量测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明
        desc = QLabel("测试分类名称后是否显示文件数量")
        desc.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(desc)

        # 数据库操作
        db_group = QGroupBox("💾 数据库操作")
        db_layout = QVBoxLayout(db_group)

        add_test_data_btn = QPushButton("➕ 添加测试数据到数据库")
        add_test_data_btn.clicked.connect(self.add_test_data_to_db)
        db_layout.addWidget(add_test_data_btn)

        check_counts_btn = QPushButton("📊 检查分类文件数量")
        check_counts_btn.clicked.connect(self.check_category_counts)
        db_layout.addWidget(check_counts_btn)

        layout.addWidget(db_group)

        # 界面测试
        ui_group = QGroupBox("🖥️ 界面测试")
        ui_layout = QVBoxLayout(ui_group)

        test_sidebar_btn = QPushButton("📋 测试侧边栏文件数显示")
        test_sidebar_btn.clicked.connect(self.test_sidebar_counts)
        ui_layout.addWidget(test_sidebar_btn)

        layout.addWidget(ui_group)

        return widget

    def create_move_test_tab(self):
        """创建素材移动测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明
        desc = QLabel("测试素材移动到其他分类功能")
        desc.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(desc)

        # 移动功能测试
        move_group = QGroupBox("📁 移动功能测试")
        move_layout = QVBoxLayout(move_group)

        test_move_dialog_btn = QPushButton("🗂️ 测试分类选择对话框")
        test_move_dialog_btn.clicked.connect(self.test_move_dialog)
        move_layout.addWidget(test_move_dialog_btn)

        test_move_function_btn = QPushButton("📦 测试移动功能（需要主程序）")
        test_move_function_btn.clicked.connect(self.test_move_function)
        move_layout.addWidget(test_move_function_btn)

        layout.addWidget(move_group)

        return widget

    def create_complete_test_tab(self):
        """创建完整流程测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明
        desc = QLabel("完整流程测试：导入 → 查看 → 移动 → 验证")
        desc.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(desc)

        # 完整流程
        complete_group = QGroupBox("🔄 完整流程测试")
        complete_layout = QVBoxLayout(complete_group)

        step1_btn = QPushButton("1️⃣ 清空数据库")
        step1_btn.clicked.connect(self.step1_clear_db)
        complete_layout.addWidget(step1_btn)

        step2_btn = QPushButton("2️⃣ 打开主程序")
        step2_btn.clicked.connect(self.step2_open_main)
        complete_layout.addWidget(step2_btn)

        step3_btn = QPushButton("3️⃣ 验证分类文件数")
        step3_btn.clicked.connect(self.step3_verify_counts)
        complete_layout.addWidget(step3_btn)

        layout.addWidget(complete_group)

        # 自动化测试
        auto_group = QGroupBox("🤖 自动化测试")
        auto_layout = QVBoxLayout(auto_group)

        auto_test_btn = QPushButton("🚀 运行完整自动化测试")
        auto_test_btn.clicked.connect(self.run_auto_test)
        auto_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        auto_layout.addWidget(auto_test_btn)

        layout.addWidget(auto_group)

        return widget

    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()

    def create_test_files(self):
        """创建测试文件"""
        try:
            self.log("📁 创建测试文件...")

            test_dir = Path("test_import_files")
            test_dir.mkdir(exist_ok=True)

            # 创建不同类型的测试文件
            test_files_info = [
                ("test_image1.jpg", "图片文件1"),
                ("test_image2.png", "图片文件2"),
                ("test_audio1.mp3", "音频文件1"),
                ("test_video1.mp4", "视频文件1"),
                ("test_document1.pdf", "文档文件1"),
                ("test_design1.psd", "设计文件1")
            ]

            created_files = []
            for filename, description in test_files_info:
                file_path = test_dir / filename

                # 创建测试文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"测试文件: {description}\n")
                    f.write(f"创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

                created_files.append(str(file_path))

            self.test_files = created_files
            self.update_file_list()

            self.log(f"✅ 已创建 {len(created_files)} 个测试文件")

        except Exception as e:
            self.log(f"❌ 创建测试文件失败: {e}")

    def update_file_list(self):
        """更新文件列表显示"""
        self.file_list.clear()

        for file_path in self.test_files:
            file_name = Path(file_path).name
            item = QListWidgetItem(file_name)
            item.setData(Qt.UserRole, file_path)
            self.file_list.addItem(item)

    def test_import_dialog(self):
        """测试导入分类选择对话框"""
        if not self.test_files:
            QMessageBox.warning(self, "警告", "请先创建测试文件！")
            return

        try:
            self.log("🗂️ 测试导入分类选择对话框...")

            from ui.dialogs.import_category_dialog import show_import_category_dialog

            result = show_import_category_dialog(self.test_files, self)

            if result:
                self.log("✅ 导入分类选择对话框测试成功:")
                self.log(f"  • 选择分类: {result['category_name']} (ID: {result['category_id']})")
                self.log(f"  • 自动分类: {result['auto_categorize']}")
                self.log(f"  • 检查重复: {result['check_duplicates']}")

                QMessageBox.information(
                    self, "测试成功",
                    f"导入分类选择对话框工作正常！\n\n选择的分类: {result['category_name']}"
                )
            else:
                self.log("⚠️ 用户取消了分类选择")

        except Exception as e:
            self.log(f"❌ 测试导入分类选择对话框失败: {e}")
            QMessageBox.critical(
                self, "测试失败",
                f"导入分类选择对话框测试失败:\n\n{e}"
            )

    def test_drag_import(self):
        """测试拖拽导入"""
        try:
            self.log("🖱️ 启动主程序进行拖拽导入测试...")

            import subprocess
            import sys

            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"],
                           cwd=str(project_root))

            self.log("✅ 主程序已启动")
            self.log("💡 请将测试文件拖拽到主程序中，验证是否弹出分类选择对话框")

        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")

    def add_test_data_to_db(self):
        """添加测试数据到数据库"""
        try:
            self.log("💾 添加测试数据到数据库...")

            from database.db_manager import DatabaseManager

            db_manager = DatabaseManager()

            # 测试数据
            test_materials = [
                {"name": "测试图片1.jpg", "file_path": "test/img1.jpg", "file_type": "image", "category_id": "images"},
                {"name": "测试图片2.png", "file_path": "test/img2.png", "file_type": "image", "category_id": "images"},
                {"name": "测试音频1.mp3", "file_path": "test/audio1.mp3", "file_type": "audio", "category_id": "audio"},
                {"name": "测试视频1.mp4", "file_path": "test/video1.mp4", "file_type": "video", "category_id": "video"},
                {"name": "测试文档1.pdf", "file_path": "test/doc1.pdf", "file_type": "document", "category_id": "documents"},
                {"name": "临时文件1.txt", "file_path": "test/temp1.txt", "file_type": "text", "category_id": "temp"},
                {"name": "临时文件2.txt", "file_path": "test/temp2.txt", "file_type": "text", "category_id": "temp"},
            ]

            added_count = 0
            for material in test_materials:
                try:
                    material_id = db_manager.add_material(material)
                    if material_id:
                        added_count += 1
                        self.log(f"  ✅ 添加: {material['name']} -> {material['category_id']}")
                except Exception as e:
                    self.log(f"  ❌ 添加失败: {material['name']} - {e}")

            self.log(f"✅ 成功添加 {added_count} 条测试数据")

        except Exception as e:
            self.log(f"❌ 添加测试数据失败: {e}")

    def check_category_counts(self):
        """检查分类文件数量"""
        try:
            self.log("📊 检查分类文件数量...")

            from database.db_manager import DatabaseManager

            db_manager = DatabaseManager()
            file_counts = db_manager.get_all_category_file_counts()

            self.log("📊 分类文件数量统计:")
            for category_id, count in file_counts.items():
                self.log(f"  • {category_id}: {count} 个文件")

            if file_counts:
                self.log("✅ 分类文件数量统计功能正常")
            else:
                self.log("⚠️ 没有找到任何文件")

        except Exception as e:
            self.log(f"❌ 检查分类文件数量失败: {e}")

    def test_sidebar_counts(self):
        """测试侧边栏文件数显示"""
        try:
            self.log("📋 启动主程序测试侧边栏文件数显示...")

            import subprocess
            import sys

            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"],
                           cwd=str(project_root))

            self.log("✅ 主程序已启动")
            self.log("💡 请查看侧边栏分类是否显示文件数量，格式如：图片文件 (2)")

        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")

    def test_move_dialog(self):
        """测试移动分类选择对话框"""
        try:
            self.log("🗂️ 测试移动分类选择对话框...")

            from ui.dialogs.category_selection_dialog import show_category_selection_dialog

            result = show_category_selection_dialog(self)

            if result:
                self.log("✅ 移动分类选择对话框测试成功:")
                self.log(f"  • 选择分类: {result['name']} (ID: {result['id']})")

                QMessageBox.information(
                    self, "测试成功",
                    f"移动分类选择对话框工作正常！\n\n选择的分类: {result['name']}"
                )
            else:
                self.log("⚠️ 用户取消了分类选择")

        except Exception as e:
            self.log(f"❌ 测试移动分类选择对话框失败: {e}")
            QMessageBox.critical(
                self, "测试失败",
                f"移动分类选择对话框测试失败:\n\n{e}"
            )

    def test_move_function(self):
        """测试移动功能"""
        try:
            self.log("📦 启动主程序测试移动功能...")

            import subprocess
            import sys

            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"],
                           cwd=str(project_root))

            self.log("✅ 主程序已启动")
            self.log("💡 请在主程序中:")
            self.log("  1. 选择一些素材")
            self.log("  2. 点击'移动到分类'按钮")
            self.log("  3. 选择目标分类")
            self.log("  4. 验证移动是否成功")

        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")

    def step1_clear_db(self):
        """步骤1：清空数据库"""
        try:
            self.log("1️⃣ 清空数据库...")

            from database.db_manager import DatabaseManager

            db_manager = DatabaseManager()
            cleared_count = db_manager.clear_all_materials()

            self.log(f"✅ 已清空数据库，删除了 {cleared_count} 条记录")

        except Exception as e:
            self.log(f"❌ 清空数据库失败: {e}")

    def step2_open_main(self):
        """步骤2：打开主程序"""
        try:
            self.log("2️⃣ 打开主程序...")

            import subprocess
            import sys

            # 启动主程序
            subprocess.Popen([sys.executable, "main_optimized.py"],
                           cwd=str(project_root))

            self.log("✅ 主程序已启动")
            self.log("💡 请在主程序中导入一些文件，验证分类选择对话框")

        except Exception as e:
            self.log(f"❌ 启动主程序失败: {e}")

    def step3_verify_counts(self):
        """步骤3：验证分类文件数"""
        try:
            self.log("3️⃣ 验证分类文件数...")

            from database.db_manager import DatabaseManager

            db_manager = DatabaseManager()
            file_counts = db_manager.get_all_category_file_counts()

            self.log("📊 当前分类文件数量:")
            total_files = 0
            for category_id, count in file_counts.items():
                if count > 0:
                    self.log(f"  • {category_id}: {count} 个文件")
                    total_files += count

            if total_files > 0:
                self.log(f"✅ 验证完成，共 {total_files} 个文件")
            else:
                self.log("⚠️ 没有找到任何文件，请先导入一些文件")

        except Exception as e:
            self.log(f"❌ 验证分类文件数失败: {e}")

    def run_auto_test(self):
        """运行完整自动化测试"""
        try:
            self.log("🚀 开始运行完整自动化测试...")

            # 测试1：检查导入分类对话框
            self.log("\n=== 测试1：导入分类对话框 ===")
            if self.test_files:
                self.test_import_dialog()
            else:
                self.create_test_files()
                QTimer.singleShot(1000, self.test_import_dialog)

            # 测试2：检查数据库功能
            self.log("\n=== 测试2：数据库功能 ===")
            self.add_test_data_to_db()
            QTimer.singleShot(2000, self.check_category_counts)

            # 测试3：检查移动对话框
            self.log("\n=== 测试3：移动分类对话框 ===")
            QTimer.singleShot(3000, self.test_move_dialog)

            # 测试4：启动主程序
            self.log("\n=== 测试4：主程序集成测试 ===")
            QTimer.singleShot(4000, self.test_sidebar_counts)

            self.log("✅ 自动化测试序列已启动")

        except Exception as e:
            self.log(f"❌ 自动化测试失败: {e}")

def main():
    """主函数"""
    app = QApplication(sys.argv)

    window = ImportCategoryCompleteTestWindow()
    window.show()

    print("导入分类功能完整测试工具启动成功！")
    print("测试内容：")
    print("1. 🗂️ 导入时弹出分类选择对话框")
    print("2. 📊 分类名称显示文件数量")
    print("3. 📁 素材移动到其他分类")
    print("4. 🔄 分类管理文件数更新")

    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
