# AI分析模块
# 功能：图像内容识别、自动标签生成、颜色分析、相似度检测等AI智能分析功能

import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from PIL import Image, ImageStat
import colorsys
import cv2

from PySide6.QtCore import QObject, Signal, QThread

class AIAnalyzer(QObject):
    """AI分析器类"""

    # 信号定义
    analysis_completed = Signal(int, dict)  # 分析完成信号
    analysis_progress = Signal(int, int, str)  # 分析进度信号

    def __init__(self, config_manager):
        super().__init__()

        self.config_manager = config_manager
        self.ai_config = config_manager.get_ai_config()

        # AI模型（这里使用简化的实现，实际项目中可以集成真实的AI模型）
        self.color_analyzer = ColorAnalyzer()
        self.content_analyzer = ContentAnalyzer()
        self.similarity_analyzer = SimilarityAnalyzer()

    def analyze_material(self, material_id: int, file_path: str, file_type: str) -> Dict[str, Any]:
        """分析单个素材"""
        try:
            analysis_result = {
                'material_id': material_id,
                'ai_tags': [],
                'color_palette': [],
                'dominant_color': '',
                'content_features': {},
                'similarity_hash': ''
            }

            if file_type == 'image':
                # 颜色分析
                if self.ai_config.get('color_analysis', True):
                    color_result = self.color_analyzer.analyze_colors(file_path)
                    analysis_result.update(color_result)

                # 内容分析
                if self.ai_config.get('object_detection', True):
                    content_result = self.content_analyzer.analyze_content(file_path)
                    analysis_result['ai_tags'].extend(content_result.get('tags', []))
                    analysis_result['content_features'] = content_result.get('features', {})

                # 相似度哈希
                similarity_hash = self.similarity_analyzer.calculate_similarity_hash(file_path)
                analysis_result['similarity_hash'] = similarity_hash

            return analysis_result

        except Exception as e:
            print(f"分析素材失败: {e}")
            return {}

    def batch_analyze_materials(self, materials: List[Dict[str, Any]]):
        """批量分析素材"""
        if not materials:
            return

        # 创建分析线程
        analysis_thread = MaterialAnalysisThread(materials, self)
        analysis_thread.analysis_completed.connect(self.analysis_completed.emit)
        analysis_thread.progress_updated.connect(self.analysis_progress.emit)
        analysis_thread.start()

    def find_similar_materials(self, target_material_id: int, materials: List[Dict[str, Any]],
                             threshold: float = None) -> List[Tuple[int, float]]:
        """查找相似素材"""
        if threshold is None:
            threshold = self.ai_config.get('similarity_threshold', 0.8)

        return self.similarity_analyzer.find_similar_materials(
            target_material_id, materials, threshold
        )

    def detect_duplicates(self, materials: List[Dict[str, Any]]) -> List[List[int]]:
        """检测重复素材"""
        return self.similarity_analyzer.detect_duplicates(materials)

    def generate_auto_tags(self, file_path: str, file_type: str) -> List[str]:
        """生成自动标签"""
        tags = []

        try:
            if file_type == 'image':
                # 基于内容的标签
                content_tags = self.content_analyzer.generate_content_tags(file_path)
                tags.extend(content_tags)

                # 基于颜色的标签
                color_tags = self.color_analyzer.generate_color_tags(file_path)
                tags.extend(color_tags)

                # 基于风格的标签
                style_tags = self.content_analyzer.generate_style_tags(file_path)
                tags.extend(style_tags)

        except Exception as e:
            print(f"生成自动标签失败: {e}")

        return list(set(tags))  # 去重

class ColorAnalyzer:
    """颜色分析器"""

    def analyze_colors(self, file_path: str) -> Dict[str, Any]:
        """分析图片颜色"""
        try:
            with Image.open(file_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 提取主色调
                dominant_color = self._get_dominant_color(img)

                # 提取调色板
                color_palette = self._extract_color_palette(img)

                return {
                    'dominant_color': dominant_color,
                    'color_palette': color_palette
                }

        except Exception as e:
            print(f"颜色分析失败: {e}")
            return {}

    def _get_dominant_color(self, img: Image.Image) -> str:
        """获取主色调"""
        try:
            # 缩小图片以提高性能
            img_small = img.resize((100, 100))

            # 转换为numpy数组
            img_array = np.array(img_small)

            # 重塑为像素列表
            pixels = img_array.reshape(-1, 3)

            # 使用K-means聚类找到主色调
            from sklearn.cluster import KMeans

            kmeans = KMeans(n_clusters=1, random_state=42, n_init=10)
            kmeans.fit(pixels)

            dominant_color = kmeans.cluster_centers_[0]

            # 转换为十六进制颜色
            hex_color = '#{:02x}{:02x}{:02x}'.format(
                int(dominant_color[0]),
                int(dominant_color[1]),
                int(dominant_color[2])
            )

            return hex_color

        except Exception as e:
            print(f"获取主色调失败: {e}")
            return '#000000'

    def _extract_color_palette(self, img: Image.Image, num_colors: int = 5) -> List[str]:
        """提取调色板"""
        try:
            # 缩小图片
            img_small = img.resize((100, 100))

            # 转换为numpy数组
            img_array = np.array(img_small)
            pixels = img_array.reshape(-1, 3)

            # 使用K-means聚类
            from sklearn.cluster import KMeans

            kmeans = KMeans(n_clusters=num_colors, random_state=42, n_init=10)
            kmeans.fit(pixels)

            colors = []
            for color in kmeans.cluster_centers_:
                hex_color = '#{:02x}{:02x}{:02x}'.format(
                    int(color[0]),
                    int(color[1]),
                    int(color[2])
                )
                colors.append(hex_color)

            return colors

        except Exception as e:
            print(f"提取调色板失败: {e}")
            return []

    def generate_color_tags(self, file_path: str) -> List[str]:
        """生成颜色相关标签"""
        try:
            with Image.open(file_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 获取主色调
                dominant_color = self._get_dominant_color(img)

                # 转换为HSV以分析颜色属性
                rgb = tuple(int(dominant_color[i:i+2], 16) for i in (1, 3, 5))
                hsv = colorsys.rgb_to_hsv(rgb[0]/255, rgb[1]/255, rgb[2]/255)

                tags = []

                # 基于色相的标签
                hue = hsv[0] * 360
                if 0 <= hue < 30 or 330 <= hue <= 360:
                    tags.append('红色')
                elif 30 <= hue < 90:
                    tags.append('黄色')
                elif 90 <= hue < 150:
                    tags.append('绿色')
                elif 150 <= hue < 210:
                    tags.append('青色')
                elif 210 <= hue < 270:
                    tags.append('蓝色')
                elif 270 <= hue < 330:
                    tags.append('紫色')

                # 基于饱和度的标签
                saturation = hsv[1]
                if saturation < 0.2:
                    tags.append('灰色调')
                elif saturation > 0.8:
                    tags.append('鲜艳')

                # 基于明度的标签
                value = hsv[2]
                if value < 0.3:
                    tags.append('深色')
                elif value > 0.7:
                    tags.append('明亮')

                return tags

        except Exception as e:
            print(f"生成颜色标签失败: {e}")
            return []

class ContentAnalyzer:
    """内容分析器"""

    def analyze_content(self, file_path: str) -> Dict[str, Any]:
        """分析图片内容"""
        try:
            # 这里使用简化的实现
            # 实际项目中可以集成YOLO、ResNet等深度学习模型

            features = self._extract_basic_features(file_path)
            tags = self._generate_basic_tags(file_path)

            return {
                'features': features,
                'tags': tags
            }

        except Exception as e:
            print(f"内容分析失败: {e}")
            return {}

    def _extract_basic_features(self, file_path: str) -> Dict[str, Any]:
        """提取基本特征"""
        try:
            with Image.open(file_path) as img:
                # 图片基本信息
                features = {
                    'width': img.width,
                    'height': img.height,
                    'aspect_ratio': img.width / img.height,
                    'format': img.format
                }

                # 转换为灰度图分析纹理
                gray_img = img.convert('L')
                img_array = np.array(gray_img)

                # 计算图像统计特征
                features.update({
                    'mean_brightness': float(np.mean(img_array)),
                    'std_brightness': float(np.std(img_array)),
                    'min_brightness': int(np.min(img_array)),
                    'max_brightness': int(np.max(img_array))
                })

                return features

        except Exception as e:
            print(f"提取基本特征失败: {e}")
            return {}

    def _generate_basic_tags(self, file_path: str) -> List[str]:
        """生成基本标签"""
        try:
            tags = []

            with Image.open(file_path) as img:
                # 基于尺寸的标签
                if img.width > img.height:
                    tags.append('横向')
                elif img.height > img.width:
                    tags.append('纵向')
                else:
                    tags.append('正方形')

                # 基于分辨率的标签
                total_pixels = img.width * img.height
                if total_pixels > 8000000:  # 8MP
                    tags.append('高分辨率')
                elif total_pixels < 1000000:  # 1MP
                    tags.append('低分辨率')

                # 基于文件格式的标签
                if img.format:
                    tags.append(img.format.lower())

            return tags

        except Exception as e:
            print(f"生成基本标签失败: {e}")
            return []

    def generate_content_tags(self, file_path: str) -> List[str]:
        """生成内容标签"""
        # 简化实现，实际项目中可以使用深度学习模型
        return self._generate_basic_tags(file_path)

    def generate_style_tags(self, file_path: str) -> List[str]:
        """生成风格标签"""
        try:
            tags = []

            with Image.open(file_path) as img:
                # 转换为灰度图分析对比度
                gray_img = img.convert('L')
                img_array = np.array(gray_img)

                # 计算对比度
                contrast = np.std(img_array)

                if contrast > 60:
                    tags.append('高对比度')
                elif contrast < 30:
                    tags.append('低对比度')

                # 分析亮度分布
                mean_brightness = np.mean(img_array)

                if mean_brightness > 180:
                    tags.append('高调')
                elif mean_brightness < 80:
                    tags.append('低调')

            return tags

        except Exception as e:
            print(f"生成风格标签失败: {e}")
            return []

class SimilarityAnalyzer:
    """相似度分析器"""

    def calculate_similarity_hash(self, file_path: str) -> str:
        """计算相似度哈希"""
        try:
            with Image.open(file_path) as img:
                # 使用感知哈希算法
                import imagehash
                hash_value = imagehash.phash(img)
                return str(hash_value)

        except Exception as e:
            print(f"计算相似度哈希失败: {e}")
            return ""

    def find_similar_materials(self, target_material_id: int, materials: List[Dict[str, Any]],
                             threshold: float) -> List[Tuple[int, float]]:
        """查找相似素材"""
        try:
            target_material = None
            for material in materials:
                if material['id'] == target_material_id:
                    target_material = material
                    break

            if not target_material or not target_material.get('similarity_hash'):
                return []

            target_hash = target_material['similarity_hash']
            similar_materials = []

            for material in materials:
                if material['id'] == target_material_id:
                    continue

                material_hash = material.get('similarity_hash')
                if not material_hash:
                    continue

                # 计算汉明距离
                similarity = self._calculate_hash_similarity(target_hash, material_hash)

                if similarity >= threshold:
                    similar_materials.append((material['id'], similarity))

            # 按相似度排序
            similar_materials.sort(key=lambda x: x[1], reverse=True)

            return similar_materials

        except Exception as e:
            print(f"查找相似素材失败: {e}")
            return []

    def detect_duplicates(self, materials: List[Dict[str, Any]]) -> List[List[int]]:
        """检测重复素材"""
        try:
            duplicates = []
            processed = set()

            for i, material1 in enumerate(materials):
                if material1['id'] in processed:
                    continue

                duplicate_group = [material1['id']]
                hash1 = material1.get('similarity_hash')

                if not hash1:
                    continue

                for j, material2 in enumerate(materials[i+1:], i+1):
                    if material2['id'] in processed:
                        continue

                    hash2 = material2.get('similarity_hash')
                    if not hash2:
                        continue

                    # 检查是否为重复（相似度很高）
                    similarity = self._calculate_hash_similarity(hash1, hash2)

                    if similarity > 0.95:  # 95%以上相似度认为是重复
                        duplicate_group.append(material2['id'])
                        processed.add(material2['id'])

                if len(duplicate_group) > 1:
                    duplicates.append(duplicate_group)
                    processed.update(duplicate_group)

            return duplicates

        except Exception as e:
            print(f"检测重复素材失败: {e}")
            return []

    def _calculate_hash_similarity(self, hash1: str, hash2: str) -> float:
        """计算哈希相似度"""
        try:
            if len(hash1) != len(hash2):
                return 0.0

            # 计算汉明距离
            hamming_distance = sum(c1 != c2 for c1, c2 in zip(hash1, hash2))

            # 转换为相似度（0-1）
            similarity = 1.0 - (hamming_distance / len(hash1))

            return similarity

        except Exception as e:
            print(f"计算哈希相似度失败: {e}")
            return 0.0

class MaterialAnalysisThread(QThread):
    """素材分析线程"""

    analysis_completed = Signal(int, dict)
    progress_updated = Signal(int, int, str)

    def __init__(self, materials, ai_analyzer):
        super().__init__()
        self.materials = materials
        self.ai_analyzer = ai_analyzer

    def run(self):
        """运行分析任务"""
        total_materials = len(self.materials)

        for i, material in enumerate(self.materials):
            try:
                # 发送进度信号
                self.progress_updated.emit(i + 1, total_materials, material.get('name', ''))

                # 分析素材
                result = self.ai_analyzer.analyze_material(
                    material['id'],
                    material['file_path'],
                    material['file_type']
                )

                if result:
                    self.analysis_completed.emit(material['id'], result)

            except Exception as e:
                print(f"分析素材 {material.get('name', '')} 失败: {e}")
                continue
