# 功能测试脚本
# 功能：测试各个功能模块的具体实现

import sys
import os
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_operations():
    """测试数据库操作"""
    print("测试数据库操作...")

    try:
        from database.db_manager import DatabaseManager

        # 使用临时数据库
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_manager = DatabaseManager(Path(tmp_db.name))
            db_manager.initialize_database()
            print("✓ 数据库初始化成功")

            # 测试添加素材
            material_data = {
                'name': 'test_image.jpg',
                'file_path': '/test/path/test_image.jpg',
                'file_type': 'image',
                'mime_type': 'image/jpeg',
                'size': 1024000,
                'width': 1920,
                'height': 1080
            }

            material_id = db_manager.add_material(material_data)
            print(f"✓ 添加素材成功，ID: {material_id}")

            # 测试获取素材
            material = db_manager.get_material(material_id)
            if material and material['name'] == 'test_image.jpg':
                print("✓ 获取素材成功")
            else:
                print("✗ 获取素材失败")

            # 测试添加标签
            tag_id = db_manager.add_tag('测试标签', '#ff0000', '测试分类')
            print(f"✓ 添加标签成功，ID: {tag_id}")

            # 测试标签关联
            db_manager.add_material_tag(material_id, tag_id)
            print("✓ 标签关联成功")

            # 测试搜索
            results = db_manager.search_materials('test')
            if results and len(results) > 0:
                print(f"✓ 搜索成功，找到 {len(results)} 个结果")
            else:
                print("✗ 搜索失败")

            # 关闭数据库连接
            db_manager.close()

            # 清理临时文件
            try:
                os.unlink(tmp_db.name)
            except PermissionError:
                pass  # 文件可能仍被占用

        return True

    except Exception as e:
        print(f"✗ 数据库操作测试失败: {e}")
        return False

def test_file_manager():
    """测试文件管理器"""
    print("\n测试文件管理器...")

    try:
        from core.file_manager import FileManager
        from database.db_manager import DatabaseManager
        from utils.config_manager import ConfigManager

        # 创建临时数据库和配置
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_manager = DatabaseManager(Path(tmp_db.name))
            db_manager.initialize_database()

            config_manager = ConfigManager()
            file_manager = FileManager(db_manager, config_manager)
            print("✓ 文件管理器创建成功")

            # 测试文件哈希计算
            test_file = Path(__file__)  # 使用当前脚本文件作为测试
            if test_file.exists():
                file_hash = file_manager.calculate_file_hash(str(test_file))
                if file_hash:
                    print(f"✓ 文件哈希计算成功: {file_hash[:8]}...")
                else:
                    print("✗ 文件哈希计算失败")

                # 测试元数据提取
                metadata = file_manager.extract_file_metadata(str(test_file))
                if metadata and 'name' in metadata:
                    print(f"✓ 元数据提取成功: {metadata['name']}")
                else:
                    print("✗ 元数据提取失败")

            # 关闭数据库连接
            db_manager.close()

            # 清理
            try:
                os.unlink(tmp_db.name)
            except PermissionError:
                pass

        return True

    except Exception as e:
        print(f"✗ 文件管理器测试失败: {e}")
        return False

def test_search_engine():
    """测试搜索引擎"""
    print("\n测试搜索引擎...")

    try:
        from core.search_engine import SearchEngine
        from database.db_manager import DatabaseManager
        from utils.config_manager import ConfigManager

        # 创建临时数据库
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_manager = DatabaseManager(Path(tmp_db.name))
            db_manager.initialize_database()

            config_manager = ConfigManager()
            search_engine = SearchEngine(db_manager, config_manager)
            print("✓ 搜索引擎创建成功")

            # 添加测试数据
            test_materials = [
                {
                    'name': 'sunset.jpg',
                    'file_path': '/test/sunset.jpg',
                    'file_type': 'image',
                    'user_tags': '日落 风景 自然',
                    'ai_tags': '天空 云朵 橙色'
                },
                {
                    'name': 'portrait.jpg',
                    'file_path': '/test/portrait.jpg',
                    'file_type': 'image',
                    'user_tags': '人像 摄影',
                    'ai_tags': '人脸 微笑'
                }
            ]

            for material in test_materials:
                db_manager.add_material(material)

            print("✓ 测试数据添加成功")

            # 测试搜索
            results, count = search_engine.search('日落')
            if results and len(results) > 0:
                print(f"✓ 搜索测试成功，找到 {len(results)} 个结果")
            else:
                print("✗ 搜索测试失败")

            # 测试高级搜索
            criteria = {
                'filename': 'sunset',
                'file_types': ['image'],
                'tags': ['风景']
            }

            results, count = search_engine.advanced_search(criteria)
            print(f"✓ 高级搜索测试完成，找到 {len(results)} 个结果")

            # 关闭数据库连接
            db_manager.close()

            # 清理
            try:
                os.unlink(tmp_db.name)
            except PermissionError:
                pass

        return True

    except Exception as e:
        print(f"✗ 搜索引擎测试失败: {e}")
        return False

def test_ai_analyzer():
    """测试AI分析器"""
    print("\n测试AI分析器...")

    try:
        from ai.ai_analyzer import AIAnalyzer, ColorAnalyzer, ContentAnalyzer, SimilarityAnalyzer
        from utils.config_manager import ConfigManager

        config_manager = ConfigManager()
        ai_analyzer = AIAnalyzer(config_manager)
        print("✓ AI分析器创建成功")

        # 测试颜色分析器
        color_analyzer = ColorAnalyzer()
        print("✓ 颜色分析器创建成功")

        # 测试内容分析器
        content_analyzer = ContentAnalyzer()
        print("✓ 内容分析器创建成功")

        # 测试相似度分析器
        similarity_analyzer = SimilarityAnalyzer()
        print("✓ 相似度分析器创建成功")

        # 测试哈希相似度计算
        hash1 = "abcd1234"
        hash2 = "abcd5678"
        similarity = similarity_analyzer._calculate_hash_similarity(hash1, hash2)
        print(f"✓ 哈希相似度计算成功: {similarity:.2f}")

        return True

    except Exception as e:
        print(f"✗ AI分析器测试失败: {e}")
        return False

def test_theme_manager():
    """测试主题管理器"""
    print("\n测试主题管理器...")

    try:
        from theme.theme_manager import ThemeManager

        theme_manager = ThemeManager()
        print("✓ 主题管理器创建成功")

        # 测试主题切换
        theme_manager.load_theme("light")
        if theme_manager.get_current_theme() == "light":
            print("✓ 浅色主题加载成功")

        theme_manager.load_theme("dark")
        if theme_manager.get_current_theme() == "dark":
            print("✓ 深色主题加载成功")

        # 测试颜色获取
        primary_color = theme_manager.get_color("primary")
        if primary_color:
            print(f"✓ 颜色获取成功: {primary_color}")

        # 测试样式表生成
        colors = theme_manager.get_theme_colors()
        stylesheet = theme_manager._generate_stylesheet(colors)
        if stylesheet and len(stylesheet) > 100:
            print("✓ 样式表生成成功")

        return True

    except Exception as e:
        print(f"✗ 主题管理器测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")

    try:
        from utils.config_manager import ConfigManager

        config_manager = ConfigManager()
        print("✓ 配置管理器创建成功")

        # 测试配置读取
        theme = config_manager.get_theme()
        print(f"✓ 主题配置读取成功: {theme}")

        # 测试配置设置
        config_manager.set_theme("dark")
        if config_manager.get_theme() == "dark":
            print("✓ 主题配置设置成功")

        # 测试嵌套配置
        ui_config = config_manager.get_ui_config()
        if ui_config and 'thumbnail_size' in ui_config:
            print("✓ UI配置读取成功")

        # 测试目录获取
        cache_dir = config_manager.get_cache_dir()
        if cache_dir.exists():
            print(f"✓ 缓存目录获取成功: {cache_dir}")

        return True

    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("智能素材管理器 - 功能模块测试")
    print("=" * 60)

    tests = [
        ("数据库操作", test_database_operations),
        ("文件管理器", test_file_manager),
        ("搜索引擎", test_search_engine),
        ("AI分析器", test_ai_analyzer),
        ("主题管理器", test_theme_manager),
        ("配置管理器", test_config_manager)
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))

    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1

    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("\n🎉 所有功能测试通过！")
        return 0
    else:
        print(f"\n❌ {total - passed} 个测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
