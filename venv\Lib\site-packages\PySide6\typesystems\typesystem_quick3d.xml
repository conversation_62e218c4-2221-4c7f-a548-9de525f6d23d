<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtQuick3D"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <load-typesystem name="typesystem_quick.xml" generate="no"/>

    <object-type name="QQuick3D"/>
    <object-type name="QQuick3DObject">
        <enum-type name="ItemChange"/>
        <modify-function signature="QQuick3DObject(QQuick3DObject*)" remove="all"/>
    </object-type>
    <object-type name="QQuick3DGeometry">
        <value-type name="Attribute">
            <enum-type name="Semantic"/>
            <enum-type name="ComponentType"/>
        </value-type>
        <value-type name="TargetAttribute" since="6.6"/>
        <enum-type name="PrimitiveType"/>
    </object-type>
    <object-type name="QQuick3DInstancing">
        <value-type name="InstanceTableEntry"/>
        <add-function signature="getInstanceBufferOverride()"
                      return-type="std::pair&lt;QByteArray,int&gt;" python-override="true"/>
        <modify-function signature="getInstanceBuffer(int*)">
          <inject-code class="shell" position="override" file="../glue/qtquick3d.cpp"
                       snippet="qquick3dinstancing-getinstancebuffer-virtual-redirect"/>
          <modify-argument index="return" pyi-type="Tuple[bool, str]"/>
          <modify-argument index="1"><remove-default-expression/><remove-argument/></modify-argument>
          <inject-code class="target" position="beginning" file="../glue/qtquick3d.cpp"
                       snippet="qquick3dinstancing-getinstancebuffer-return"/>
        </modify-function>
    </object-type>
    <object-type name="QQuick3DTextureData">
        <enum-type name="Format"/>
    </object-type>
    <object-type name="QQuick3DRenderExtension" since="6.7"/>
</typesystem>
