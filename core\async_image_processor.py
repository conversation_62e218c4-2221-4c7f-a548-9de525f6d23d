#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步图片处理器
高性能异步图片压缩和处理，确保UI流畅度
"""

import time
import queue
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import concurrent.futures

from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PySide6.QtGui import QPixmap

from core.image_compressor import SmartImageCompressor, CompressionConfig, CompressionLevel

class ProcessingPriority(Enum):
    """处理优先级"""
    LOW = 1      # 低优先级：预加载
    NORMAL = 2   # 普通优先级：正常显示
    HIGH = 3     # 高优先级：当前可见
    URGENT = 4   # 紧急优先级：用户交互

@dataclass
class ProcessingTask:
    """处理任务"""
    file_path: str
    priority: ProcessingPriority
    target_size: Optional[tuple] = None
    callback: Optional[Callable] = None
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()
    
    def __lt__(self, other):
        # 优先级队列排序：优先级高的先处理，同优先级按时间排序
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.timestamp < other.timestamp

class AsyncImageProcessor(QObject):
    """异步图片处理器"""
    
    # 信号定义
    image_processed = Signal(str, QPixmap, dict)  # 文件路径, 处理后图片, 统计信息
    processing_progress = Signal(int, int)  # 当前进度, 总任务数
    processing_error = Signal(str, str)  # 文件路径, 错误信息
    queue_status_changed = Signal(int, int, int)  # 队列大小, 处理中任务数, 完成任务数
    
    def __init__(self, config: Optional[CompressionConfig] = None, max_workers: int = 4):
        super().__init__()
        
        self.config = config or CompressionConfig()
        self.max_workers = max_workers
        
        # 图片压缩器
        self.compressor = SmartImageCompressor(self.config)
        self.compressor.compression_completed.connect(self._on_compression_completed)
        self.compressor.compression_error.connect(self._on_compression_error)
        
        # 任务队列
        self.task_queue = queue.PriorityQueue()
        self.processing_tasks = {}  # 正在处理的任务
        self.completed_tasks = set()  # 已完成的任务
        
        # 线程池
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        
        # 统计信息
        self.stats = {
            'total_queued': 0,
            'total_processed': 0,
            'total_errors': 0,
            'average_processing_time': 0.0,
            'queue_peak_size': 0
        }
        
        # 控制标志
        self.is_running = True
        self.is_paused = False
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(1000)  # 每秒更新一次状态
        
        # 启动处理线程
        self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.processing_thread.start()
        
        print(f"异步图片处理器初始化完成")
        print(f"  • 工作线程数: {max_workers}")
        print(f"  • 压缩级别: {self.config.compression_level.value}")
    
    def add_task(self, file_path: str, priority: ProcessingPriority = ProcessingPriority.NORMAL,
                 target_size: Optional[tuple] = None, callback: Optional[Callable] = None) -> bool:
        """添加处理任务"""
        try:
            # 检查文件是否存在
            if not Path(file_path).exists():
                self.processing_error.emit(file_path, "文件不存在")
                return False
            
            # 检查是否已经在处理或已完成
            if file_path in self.processing_tasks or file_path in self.completed_tasks:
                return False
            
            # 创建任务
            task = ProcessingTask(
                file_path=file_path,
                priority=priority,
                target_size=target_size,
                callback=callback
            )
            
            # 添加到队列
            self.task_queue.put(task)
            self.stats['total_queued'] += 1
            
            # 更新队列峰值
            current_size = self.task_queue.qsize()
            if current_size > self.stats['queue_peak_size']:
                self.stats['queue_peak_size'] = current_size
            
            return True
            
        except Exception as e:
            self.processing_error.emit(file_path, f"添加任务失败: {e}")
            return False
    
    def add_batch_tasks(self, file_paths: List[str], priority: ProcessingPriority = ProcessingPriority.NORMAL,
                       target_size: Optional[tuple] = None) -> int:
        """批量添加任务"""
        added_count = 0
        for file_path in file_paths:
            if self.add_task(file_path, priority, target_size):
                added_count += 1
        
        print(f"批量添加任务: {added_count}/{len(file_paths)} 个任务已添加")
        return added_count
    
    def prioritize_tasks(self, file_paths: List[str], priority: ProcessingPriority):
        """提升指定文件的处理优先级"""
        # 这里可以实现优先级提升逻辑
        # 由于PriorityQueue不支持直接修改优先级，我们可以重新添加高优先级任务
        for file_path in file_paths:
            if file_path not in self.processing_tasks and file_path not in self.completed_tasks:
                self.add_task(file_path, priority)
    
    def _processing_loop(self):
        """处理循环"""
        while self.is_running:
            try:
                if self.is_paused:
                    time.sleep(0.1)
                    continue
                
                # 获取任务
                try:
                    task = self.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 检查是否已经处理过
                if task.file_path in self.completed_tasks:
                    continue
                
                # 标记为正在处理
                self.processing_tasks[task.file_path] = task
                
                # 提交到线程池处理
                future = self.executor.submit(self._process_single_task, task)
                future.add_done_callback(lambda f, t=task: self._on_task_completed(t, f))
                
            except Exception as e:
                print(f"处理循环错误: {e}")
    
    def _process_single_task(self, task: ProcessingTask) -> tuple:
        """处理单个任务"""
        start_time = time.time()
        
        try:
            # 执行图片压缩
            if task.target_size:
                compressed_pixmap = self.compressor.compress_image(task.file_path, task.target_size)
            else:
                compressed_pixmap = self.compressor.compress_for_thumbnail(task.file_path)
            
            processing_time = time.time() - start_time
            
            # 更新统计
            self.stats['total_processed'] += 1
            
            # 更新平均处理时间
            total_time = self.stats['average_processing_time'] * (self.stats['total_processed'] - 1)
            self.stats['average_processing_time'] = (total_time + processing_time) / self.stats['total_processed']
            
            return compressed_pixmap, {
                'processing_time': processing_time,
                'priority': task.priority.name,
                'queue_wait_time': start_time - task.timestamp
            }
            
        except Exception as e:
            self.stats['total_errors'] += 1
            raise e
    
    def _on_task_completed(self, task: ProcessingTask, future: concurrent.futures.Future):
        """任务完成回调"""
        try:
            # 从处理中任务移除
            if task.file_path in self.processing_tasks:
                del self.processing_tasks[task.file_path]
            
            # 添加到已完成任务
            self.completed_tasks.add(task.file_path)
            
            # 获取结果
            compressed_pixmap, stats_info = future.result()
            
            # 调用回调函数
            if task.callback:
                task.callback(task.file_path, compressed_pixmap, stats_info)
            
            # 发送信号
            self.image_processed.emit(task.file_path, compressed_pixmap, stats_info)
            
        except Exception as e:
            self.stats['total_errors'] += 1
            error_msg = f"任务处理失败: {e}"
            self.processing_error.emit(task.file_path, error_msg)
            print(f"任务完成处理错误 {task.file_path}: {error_msg}")
    
    def _on_compression_completed(self, file_path: str, pixmap: QPixmap, stats: dict):
        """压缩完成回调"""
        # 这里可以添加额外的处理逻辑
        pass
    
    def _on_compression_error(self, file_path: str, error_msg: str):
        """压缩错误回调"""
        self.processing_error.emit(file_path, error_msg)
    
    def _update_status(self):
        """更新状态"""
        queue_size = self.task_queue.qsize()
        processing_count = len(self.processing_tasks)
        completed_count = len(self.completed_tasks)
        
        self.queue_status_changed.emit(queue_size, processing_count, completed_count)
        
        # 更新进度
        total_tasks = self.stats['total_queued']
        if total_tasks > 0:
            progress = int((completed_count / total_tasks) * 100)
            self.processing_progress.emit(progress, total_tasks)
    
    def pause_processing(self):
        """暂停处理"""
        self.is_paused = True
        print("图片处理已暂停")
    
    def resume_processing(self):
        """恢复处理"""
        self.is_paused = False
        print("图片处理已恢复")
    
    def clear_queue(self):
        """清空队列"""
        # 清空任务队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
            except queue.Empty:
                break
        
        # 清空已完成任务
        self.completed_tasks.clear()
        
        print("处理队列已清空")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        compression_stats = self.compressor.get_compression_stats()
        
        return {
            'processor_stats': {
                'total_queued': self.stats['total_queued'],
                'total_processed': self.stats['total_processed'],
                'total_errors': self.stats['total_errors'],
                'error_rate': (self.stats['total_errors'] / max(1, self.stats['total_processed'])) * 100,
                'average_processing_time': self.stats['average_processing_time'],
                'queue_peak_size': self.stats['queue_peak_size'],
                'current_queue_size': self.task_queue.qsize(),
                'processing_tasks': len(self.processing_tasks),
                'completed_tasks': len(self.completed_tasks)
            },
            'compression_stats': compression_stats
        }
    
    def optimize_for_viewport(self, visible_files: List[str], preload_files: List[str] = None):
        """为视口优化处理顺序"""
        # 为可见文件设置高优先级
        self.prioritize_tasks(visible_files, ProcessingPriority.HIGH)
        
        # 为预加载文件设置低优先级
        if preload_files:
            self.prioritize_tasks(preload_files, ProcessingPriority.LOW)
    
    def cleanup(self):
        """清理资源"""
        print("正在清理异步图片处理器...")
        
        # 停止处理
        self.is_running = False
        
        # 停止定时器
        if self.status_timer:
            self.status_timer.stop()
        
        # 等待处理线程结束
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=3.0)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        # 清理压缩器
        if self.compressor:
            self.compressor.clear_cache()
        
        print("异步图片处理器清理完成")

# 全局异步处理器实例
_async_processor = None

def get_async_image_processor(config: Optional[CompressionConfig] = None, max_workers: int = 4) -> AsyncImageProcessor:
    """获取全局异步图片处理器实例"""
    global _async_processor
    if _async_processor is None:
        _async_processor = AsyncImageProcessor(config, max_workers)
    return _async_processor

def cleanup_async_image_processor():
    """清理全局异步图片处理器"""
    global _async_processor
    if _async_processor:
        _async_processor.cleanup()
        _async_processor = None
