# 数据库管理模块
# 功能：管理SQLite数据库连接，创建表结构，提供数据访问接口

import sqlite3
import json
import queue
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import threading
from contextlib import contextmanager

class ConnectionPool:
    """数据库连接池"""

    def __init__(self, db_path: str, max_connections: int = 10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.pool = queue.Queue(maxsize=max_connections)
        self.created_connections = 0
        self.lock = threading.Lock()

        # 预创建一些连接
        self._initialize_pool()

    def _initialize_pool(self):
        """初始化连接池"""
        # 预创建一半的连接
        initial_count = max(1, self.max_connections // 2)
        for _ in range(initial_count):
            conn = self._create_connection()
            if conn:
                self.pool.put(conn)
                self.created_connections += 1

    def _create_connection(self):
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0, check_same_thread=False)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA foreign_keys = ON")
            conn.execute("PRAGMA journal_mode = WAL")  # 启用WAL模式提升并发性能
            conn.execute("PRAGMA synchronous = NORMAL")  # 平衡性能和安全性
            conn.execute("PRAGMA cache_size = -64000")  # 64MB缓存
            return conn
        except Exception as e:
            print(f"创建数据库连接失败: {e}")
            return None

    def get_connection(self, timeout: float = 30.0):
        """获取数据库连接"""
        try:
            # 尝试从池中获取连接
            return self.pool.get(timeout=timeout)
        except queue.Empty:
            # 池中没有可用连接，尝试创建新连接
            with self.lock:
                if self.created_connections < self.max_connections:
                    conn = self._create_connection()
                    if conn:
                        self.created_connections += 1
                        return conn

            # 无法创建新连接，等待现有连接释放
            raise Exception("无法获取数据库连接：连接池已满且超时")

    def return_connection(self, conn):
        """归还数据库连接"""
        if conn:
            try:
                # 检查连接是否仍然有效
                conn.execute("SELECT 1")
                self.pool.put(conn, block=False)
            except (sqlite3.Error, queue.Full):
                # 连接无效或池已满，关闭连接
                try:
                    conn.close()
                except:
                    pass
                with self.lock:
                    self.created_connections -= 1

    def close_all(self):
        """关闭所有连接"""
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                conn.close()
            except (queue.Empty, sqlite3.Error):
                break

        with self.lock:
            self.created_connections = 0

class DatabaseManager:
    """数据库管理器类"""

    def __init__(self, db_path: Optional[Path] = None):
        if db_path is None:
            from utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            self.db_path = config_manager.get_database_path()
        else:
            self.db_path = db_path

        # 使用连接池替代单一连接
        self.connection_pool = ConnectionPool(str(self.db_path), max_connections=10)
        self.lock = threading.Lock()

    def initialize_database(self):
        """初始化数据库，创建表结构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 创建素材表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS materials (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    file_type TEXT NOT NULL,
                    mime_type TEXT,
                    size INTEGER,
                    width INTEGER,
                    height INTEGER,
                    duration REAL,
                    created_time TIMESTAMP,
                    modified_time TIMESTAMP,
                    indexed_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    thumbnail_path TEXT,
                    md5_hash TEXT,
                    image_hash TEXT,
                    color_palette TEXT,
                    dominant_color TEXT,
                    ai_tags TEXT,
                    user_tags TEXT,
                    rating INTEGER DEFAULT 0,
                    favorite BOOLEAN DEFAULT 0,
                    description TEXT,
                    metadata TEXT
                )
            ''')

            # 创建分类表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    parent_id INTEGER,
                    icon TEXT,
                    color TEXT,
                    description TEXT,
                    sort_order INTEGER DEFAULT 0,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES categories (id)
                )
            ''')

            # 创建标签表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    color TEXT,
                    usage_count INTEGER DEFAULT 0,
                    category TEXT,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                )
            ''')

            # 创建素材标签关联表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS material_tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    material_id INTEGER NOT NULL,
                    tag_id INTEGER NOT NULL,
                    confidence REAL DEFAULT 1.0,
                    source TEXT DEFAULT 'user',
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (material_id) REFERENCES materials (id) ON DELETE CASCADE,
                    FOREIGN KEY (tag_id) REFERENCES tags (id) ON DELETE CASCADE,
                    UNIQUE(material_id, tag_id)
                )
            ''')

            # 创建收藏夹表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS collections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    cover_image TEXT,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    item_count INTEGER DEFAULT 0
                )
            ''')

            # 创建收藏夹内容表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS collection_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    collection_id INTEGER NOT NULL,
                    material_id INTEGER NOT NULL,
                    sort_order INTEGER DEFAULT 0,
                    added_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (collection_id) REFERENCES collections (id) ON DELETE CASCADE,
                    FOREIGN KEY (material_id) REFERENCES materials (id) ON DELETE CASCADE,
                    UNIQUE(collection_id, material_id)
                )
            ''')

            # 创建设置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    type TEXT,
                    description TEXT,
                    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建搜索历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS search_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    query TEXT NOT NULL,
                    filters TEXT,
                    result_count INTEGER,
                    search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建索引
            self._create_indexes(cursor)

            conn.commit()

    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_materials_file_path ON materials(file_path)",
            "CREATE INDEX IF NOT EXISTS idx_materials_file_type ON materials(file_type)",
            "CREATE INDEX IF NOT EXISTS idx_materials_created_time ON materials(created_time)",
            "CREATE INDEX IF NOT EXISTS idx_materials_rating ON materials(rating)",
            "CREATE INDEX IF NOT EXISTS idx_materials_favorite ON materials(favorite)",
            "CREATE INDEX IF NOT EXISTS idx_materials_md5_hash ON materials(md5_hash)",
            "CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name)",
            "CREATE INDEX IF NOT EXISTS idx_material_tags_material_id ON material_tags(material_id)",
            "CREATE INDEX IF NOT EXISTS idx_material_tags_tag_id ON material_tags(tag_id)",
            "CREATE INDEX IF NOT EXISTS idx_collection_items_collection_id ON collection_items(collection_id)",
            "CREATE INDEX IF NOT EXISTS idx_collection_items_material_id ON collection_items(material_id)"
        ]

        for index_sql in indexes:
            cursor.execute(index_sql)

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器（使用连接池）"""
        conn = None
        try:
            conn = self.connection_pool.get_connection()
            yield conn
        finally:
            if conn:
                self.connection_pool.return_connection(conn)

    def add_material(self, material_data: Dict[str, Any]) -> int:
        """添加素材记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 准备插入数据
            columns = []
            values = []
            placeholders = []

            for key, value in material_data.items():
                if value is not None:
                    columns.append(key)
                    values.append(value)
                    placeholders.append('?')

            sql = f"INSERT INTO materials ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            cursor.execute(sql, values)

            material_id = cursor.lastrowid
            conn.commit()
            return material_id

    def batch_add_materials(self, materials_data: List[Dict[str, Any]], batch_size: int = 1000) -> List[int]:
        """批量添加素材记录（优化性能）"""
        from PySide6.QtWidgets import QApplication

        inserted_ids = []

        for i in range(0, len(materials_data), batch_size):
            batch = materials_data[i:i + batch_size]

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 批量插入
                insert_sql = '''
                    INSERT INTO materials (
                        file_path, name, file_type, mime_type, size, width, height,
                        duration, created_time, modified_time, thumbnail_path,
                        md5_hash, image_hash, color_palette, dominant_color,
                        ai_tags, user_tags, rating, favorite, description, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                '''

                batch_data = []
                for material in batch:
                    batch_data.append((
                        material.get('file_path'),
                        material.get('name'),
                        material.get('file_type'),
                        material.get('mime_type'),
                        material.get('size'),
                        material.get('width'),
                        material.get('height'),
                        material.get('duration'),
                        material.get('created_time'),
                        material.get('modified_time'),
                        material.get('thumbnail_path'),
                        material.get('md5_hash'),
                        material.get('image_hash'),
                        material.get('color_palette'),
                        material.get('dominant_color'),
                        material.get('ai_tags'),
                        material.get('user_tags'),
                        material.get('rating', 0),
                        material.get('favorite', False),
                        material.get('description'),
                        material.get('metadata')
                    ))

                cursor.executemany(insert_sql, batch_data)
                conn.commit()

                # 获取插入的ID（近似）
                last_id = cursor.lastrowid
                batch_ids = list(range(last_id - len(batch) + 1, last_id + 1))
                inserted_ids.extend(batch_ids)

            # 允许UI更新
            if QApplication.instance():
                QApplication.processEvents()

        return inserted_ids

    def update_material(self, material_id: int, material_data: Dict[str, Any]) -> bool:
        """更新素材记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 准备更新数据
            set_clauses = []
            values = []

            for key, value in material_data.items():
                set_clauses.append(f"{key} = ?")
                values.append(value)

            values.append(material_id)

            sql = f"UPDATE materials SET {', '.join(set_clauses)} WHERE id = ?"
            cursor.execute(sql, values)

            success = cursor.rowcount > 0
            conn.commit()
            return success

    def get_material(self, material_id: int) -> Optional[Dict[str, Any]]:
        """获取单个素材记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM materials WHERE id = ?", (material_id,))
            row = cursor.fetchone()
            return dict(row) if row else None

    def get_material_by_path(self, file_path: str) -> Optional[Dict[str, Any]]:
        """根据文件路径获取素材记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM materials WHERE file_path = ?", (file_path,))
            row = cursor.fetchone()
            return dict(row) if row else None

    def delete_material(self, material_id: int) -> bool:
        """删除素材记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM materials WHERE id = ?", (material_id,))
            success = cursor.rowcount > 0
            conn.commit()
            return success

    def search_materials(self, query: str = "", filters: Dict[str, Any] = None,
                        limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索素材"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 构建查询条件
            where_clauses = []
            params = []

            if query:
                where_clauses.append("(name LIKE ? OR user_tags LIKE ? OR ai_tags LIKE ?)")
                query_pattern = f"%{query}%"
                params.extend([query_pattern, query_pattern, query_pattern])

            if filters:
                if 'file_type' in filters:
                    where_clauses.append("file_type = ?")
                    params.append(filters['file_type'])

                if 'rating_min' in filters:
                    where_clauses.append("rating >= ?")
                    params.append(filters['rating_min'])

                if 'favorite' in filters:
                    where_clauses.append("favorite = ?")
                    params.append(filters['favorite'])

                if 'date_from' in filters:
                    where_clauses.append("created_time >= ?")
                    params.append(filters['date_from'])

                if 'date_to' in filters:
                    where_clauses.append("created_time <= ?")
                    params.append(filters['date_to'])

            # 构建SQL查询
            sql = "SELECT * FROM materials"
            if where_clauses:
                sql += " WHERE " + " AND ".join(where_clauses)

            sql += " ORDER BY created_time DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor.execute(sql, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]

    def get_materials_count(self, query: str = "", filters: Dict[str, Any] = None) -> int:
        """获取符合条件的素材总数"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 构建查询条件（与search_materials相同的逻辑）
            where_clauses = []
            params = []

            if query:
                where_clauses.append("(name LIKE ? OR user_tags LIKE ? OR ai_tags LIKE ?)")
                query_pattern = f"%{query}%"
                params.extend([query_pattern, query_pattern, query_pattern])

            if filters:
                if 'file_type' in filters:
                    where_clauses.append("file_type = ?")
                    params.append(filters['file_type'])

                if 'rating_min' in filters:
                    where_clauses.append("rating >= ?")
                    params.append(filters['rating_min'])

                if 'favorite' in filters:
                    where_clauses.append("favorite = ?")
                    params.append(filters['favorite'])

                if 'date_from' in filters:
                    where_clauses.append("created_time >= ?")
                    params.append(filters['date_from'])

                if 'date_to' in filters:
                    where_clauses.append("created_time <= ?")
                    params.append(filters['date_to'])

            sql = "SELECT COUNT(*) FROM materials"
            if where_clauses:
                sql += " WHERE " + " AND ".join(where_clauses)

            cursor.execute(sql, params)
            return cursor.fetchone()[0]

    def add_tag(self, name: str, color: str = None, category: str = None) -> int:
        """添加标签"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT OR IGNORE INTO tags (name, color, category) VALUES (?, ?, ?)",
                (name, color, category)
            )

            # 获取标签ID
            cursor.execute("SELECT id FROM tags WHERE name = ?", (name,))
            tag_id = cursor.fetchone()[0]

            conn.commit()
            return tag_id

    def get_all_tags(self) -> List[Dict[str, Any]]:
        """获取所有标签"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM tags ORDER BY usage_count DESC, name")
            rows = cursor.fetchall()
            return [dict(row) for row in rows]

    def add_material_tag(self, material_id: int, tag_id: int, confidence: float = 1.0, source: str = 'user'):
        """为素材添加标签"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT OR IGNORE INTO material_tags (material_id, tag_id, confidence, source) VALUES (?, ?, ?, ?)",
                (material_id, tag_id, confidence, source)
            )

            # 更新标签使用次数
            cursor.execute("UPDATE tags SET usage_count = usage_count + 1 WHERE id = ?", (tag_id,))

            conn.commit()

    def get_material_tags(self, material_id: int) -> List[Dict[str, Any]]:
        """获取素材的所有标签"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT t.*, mt.confidence, mt.source
                FROM tags t
                JOIN material_tags mt ON t.id = mt.tag_id
                WHERE mt.material_id = ?
                ORDER BY mt.confidence DESC, t.name
            ''', (material_id,))
            rows = cursor.fetchall()
            return [dict(row) for row in rows]

    def close(self):
        """关闭数据库连接池"""
        if self.connection_pool:
            self.connection_pool.close_all()
            self.connection_pool = None
