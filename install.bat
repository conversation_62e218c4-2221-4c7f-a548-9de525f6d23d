@echo off
chcp 65001 >nul
title 智能素材管理器 - 安装程序

echo.
echo ========================================
echo      智能素材管理器 - 安装程序
echo ========================================
echo.

REM 检查Python版本
echo [1/5] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Python，请先安装Python 3.12或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [成功] 检测到Python %PYTHON_VERSION%

REM 检查Python版本是否符合要求
python -c "import sys; exit(0 if sys.version_info >= (3, 12) else 1)" >nul 2>&1
if errorlevel 1 (
    echo [警告] Python版本可能过低，建议使用Python 3.12或更高版本
    echo 当前版本: %PYTHON_VERSION%
    echo.
    set /p choice="是否继续安装? (y/n): "
    if /i not "%choice%"=="y" (
        echo 安装已取消
        pause
        exit /b 1
    )
)

REM 创建虚拟环境
echo.
echo [2/5] 创建虚拟环境...
if exist "venv" (
    echo [信息] 虚拟环境已存在，跳过创建
) else (
    python -m venv venv
    if errorlevel 1 (
        echo [错误] 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo [成功] 虚拟环境创建完成
)

REM 激活虚拟环境
echo.
echo [3/5] 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo [错误] 激活虚拟环境失败
    pause
    exit /b 1
)
echo [成功] 虚拟环境已激活

REM 升级pip
echo.
echo [4/5] 升级pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo [警告] pip升级失败，继续安装...
) else (
    echo [成功] pip升级完成
)

REM 安装依赖包
echo.
echo [5/5] 安装依赖包...
echo [信息] 这可能需要几分钟时间，请耐心等待...
pip install -r requirements.txt
if errorlevel 1 (
    echo [错误] 安装依赖包失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查网络连接
    echo 2. 尝试使用国内镜像源: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    echo 3. 手动安装主要依赖: pip install PySide6 Pillow opencv-python scikit-learn
    pause
    exit /b 1
)

echo [成功] 依赖包安装完成

REM 创建桌面快捷方式
echo.
echo [额外] 创建桌面快捷方式...
set "desktop=%USERPROFILE%\Desktop"
set "shortcut=%desktop%\智能素材管理器.lnk"
set "target=%CD%\run.bat"
set "workdir=%CD%"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut%'); $Shortcut.TargetPath = '%target%'; $Shortcut.WorkingDirectory = '%workdir%'; $Shortcut.Save()" >nul 2>&1

if exist "%shortcut%" (
    echo [成功] 桌面快捷方式创建完成
) else (
    echo [信息] 桌面快捷方式创建失败，可以手动运行run.bat启动程序
)

REM 安装完成
echo.
echo ========================================
echo            安装完成！
echo ========================================
echo.
echo 安装信息:
echo - Python版本: %PYTHON_VERSION%
echo - 安装路径: %CD%
echo - 虚拟环境: %CD%\venv
echo.
echo 启动方式:
echo 1. 双击桌面上的"智能素材管理器"快捷方式
echo 2. 双击项目目录中的run.bat文件
echo 3. 在命令行中运行: python main.py
echo.
echo 如有问题，请查看README.md文件或联系技术支持
echo.

set /p choice="是否立即启动程序? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo [信息] 启动智能素材管理器...
    python main.py
)

echo.
echo 感谢使用智能素材管理器！
pause
