#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类管理器
支持自定义分类的创建、删除、修改和管理
"""

import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from PySide6.QtCore import QObject, Signal

class CategoryType(Enum):
    """分类类型"""
    SYSTEM = "system"      # 系统预定义分类
    CUSTOM = "custom"      # 用户自定义分类

@dataclass
class Category:
    """分类数据类"""
    id: str
    name: str
    type: CategoryType
    icon: str = "📁"
    color: str = "#4A90E2"
    description: str = ""
    created_time: float = 0.0
    modified_time: float = 0.0
    file_count: int = 0
    parent_id: Optional[str] = None

    def __post_init__(self):
        if self.created_time == 0.0:
            self.created_time = time.time()
        if self.modified_time == 0.0:
            self.modified_time = time.time()

class CategoryManager(QObject):
    """分类管理器"""

    # 信号定义
    category_added = Signal(dict)      # 分类添加
    category_removed = Signal(str)     # 分类删除
    category_updated = Signal(dict)    # 分类更新
    categories_loaded = Signal(list)   # 分类加载完成

    def __init__(self, config_dir: Optional[Path] = None):
        super().__init__()

        # 配置目录
        self.config_dir = config_dir or Path.home() / ".smart_asset_manager"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.categories_file = self.config_dir / "categories.json"

        # 分类存储
        self.categories: Dict[str, Category] = {}

        # 初始化系统分类
        self._init_system_categories()

        # 加载用户分类
        self.load_categories()

        print("分类管理器初始化完成")

    def _init_system_categories(self):
        """初始化系统预定义分类"""
        system_categories = [
            Category("all", "全部素材", CategoryType.SYSTEM, "📦", "#6C7B7F"),
            Category("smart", "智能分类", CategoryType.SYSTEM, "🧠", "#FF6B6B"),
            Category("images", "图片文件", CategoryType.SYSTEM, "🖼️", "#4ECDC4"),
            Category("audio", "音频文件", CategoryType.SYSTEM, "🎵", "#45B7D1"),
            Category("design", "设计文件", CategoryType.SYSTEM, "🎨", "#96CEB4"),
            Category("documents", "文档文件", CategoryType.SYSTEM, "📄", "#FFEAA7"),
            Category("temp", "临时分组", CategoryType.SYSTEM, "📦", "#FFA726", "导入时的临时分组"),
        ]

        for category in system_categories:
            self.categories[category.id] = category

    def load_categories(self):
        """加载分类配置"""
        try:
            if self.categories_file.exists():
                with open(self.categories_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 加载自定义分类
                for cat_data in data.get('custom_categories', []):
                    category = Category(
                        id=cat_data['id'],
                        name=cat_data['name'],
                        type=CategoryType.CUSTOM,
                        icon=cat_data.get('icon', '📁'),
                        color=cat_data.get('color', '#4A90E2'),
                        description=cat_data.get('description', ''),
                        created_time=cat_data.get('created_time', time.time()),
                        modified_time=cat_data.get('modified_time', time.time()),
                        file_count=cat_data.get('file_count', 0),
                        parent_id=cat_data.get('parent_id')
                    )
                    self.categories[category.id] = category

                print(f"✅ 已加载 {len(data.get('custom_categories', []))} 个自定义分类")

            # 发送加载完成信号
            self.categories_loaded.emit(self.get_all_categories())

        except Exception as e:
            print(f"加载分类配置失败: {e}")

    def save_categories(self):
        """保存分类配置"""
        try:
            # 只保存自定义分类
            custom_categories = [
                asdict(cat) for cat in self.categories.values()
                if cat.type == CategoryType.CUSTOM
            ]

            # 转换枚举为字符串
            for cat_data in custom_categories:
                cat_data['type'] = cat_data['type'].value

            data = {
                'custom_categories': custom_categories,
                'version': '1.0',
                'last_modified': time.time()
            }

            with open(self.categories_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print(f"✅ 已保存 {len(custom_categories)} 个自定义分类")

        except Exception as e:
            print(f"保存分类配置失败: {e}")

    def add_category(self, name: str, icon: str = "📁", color: str = "#4A90E2",
                    description: str = "", parent_id: Optional[str] = None) -> Optional[str]:
        """添加自定义分类"""
        try:
            # 生成唯一ID
            category_id = self._generate_category_id(name)

            # 检查名称是否已存在
            if self._is_name_exists(name):
                print(f"分类名称 '{name}' 已存在")
                return None

            # 创建分类
            category = Category(
                id=category_id,
                name=name,
                type=CategoryType.CUSTOM,
                icon=icon,
                color=color,
                description=description,
                parent_id=parent_id
            )

            # 添加到存储
            self.categories[category_id] = category

            # 保存配置
            self.save_categories()

            # 发送信号
            self.category_added.emit(asdict(category))

            print(f"✅ 已添加分类: {name}")
            return category_id

        except Exception as e:
            print(f"添加分类失败: {e}")
            return None

    def remove_category(self, category_id: str) -> bool:
        """删除分类"""
        try:
            if category_id not in self.categories:
                print(f"分类 {category_id} 不存在")
                return False

            category = self.categories[category_id]

            # 不能删除系统分类
            if category.type == CategoryType.SYSTEM:
                print(f"不能删除系统分类: {category.name}")
                return False

            # 检查是否有子分类
            child_categories = self.get_child_categories(category_id)
            if child_categories:
                print(f"分类 '{category.name}' 有子分类，无法删除")
                return False

            # 删除分类
            del self.categories[category_id]

            # 保存配置
            self.save_categories()

            # 发送信号
            self.category_removed.emit(category_id)

            print(f"✅ 已删除分类: {category.name}")
            return True

        except Exception as e:
            print(f"删除分类失败: {e}")
            return False

    def update_category(self, category_id: str, name: Optional[str] = None,
                       icon: Optional[str] = None, color: Optional[str] = None,
                       description: Optional[str] = None) -> bool:
        """更新分类信息"""
        try:
            if category_id not in self.categories:
                print(f"分类 {category_id} 不存在")
                return False

            category = self.categories[category_id]

            # 不能修改系统分类
            if category.type == CategoryType.SYSTEM:
                print(f"不能修改系统分类: {category.name}")
                return False

            # 检查新名称是否已存在
            if name and name != category.name and self._is_name_exists(name):
                print(f"分类名称 '{name}' 已存在")
                return False

            # 更新字段
            if name:
                category.name = name
            if icon:
                category.icon = icon
            if color:
                category.color = color
            if description is not None:
                category.description = description

            category.modified_time = time.time()

            # 保存配置
            self.save_categories()

            # 发送信号
            self.category_updated.emit(asdict(category))

            print(f"✅ 已更新分类: {category.name}")
            return True

        except Exception as e:
            print(f"更新分类失败: {e}")
            return False

    def get_category(self, category_id: str) -> Optional[Category]:
        """获取分类"""
        return self.categories.get(category_id)

    def get_all_categories(self) -> List[Dict[str, Any]]:
        """获取所有分类"""
        return [asdict(cat) for cat in self.categories.values()]

    def get_custom_categories(self) -> List[Dict[str, Any]]:
        """获取自定义分类"""
        return [
            asdict(cat) for cat in self.categories.values()
            if cat.type == CategoryType.CUSTOM
        ]

    def get_system_categories(self) -> List[Dict[str, Any]]:
        """获取系统分类"""
        return [
            asdict(cat) for cat in self.categories.values()
            if cat.type == CategoryType.SYSTEM
        ]

    def get_child_categories(self, parent_id: str) -> List[Dict[str, Any]]:
        """获取子分类"""
        return [
            asdict(cat) for cat in self.categories.values()
            if cat.parent_id == parent_id
        ]

    def get_root_categories(self) -> List[Dict[str, Any]]:
        """获取根分类（无父分类）"""
        return [
            asdict(cat) for cat in self.categories.values()
            if cat.parent_id is None
        ]

    def _generate_category_id(self, name: str) -> str:
        """生成分类ID"""
        import hashlib
        import uuid

        # 使用名称和时间戳生成唯一ID
        base_string = f"{name}_{time.time()}_{uuid.uuid4().hex[:8]}"
        return hashlib.md5(base_string.encode()).hexdigest()[:12]

    def _is_name_exists(self, name: str) -> bool:
        """检查名称是否已存在"""
        return any(cat.name == name for cat in self.categories.values())

    def update_file_count(self, category_id: str, count: int):
        """更新分类文件数量"""
        if category_id in self.categories:
            self.categories[category_id].file_count = count
            self.save_categories()

    def search_categories(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索分类"""
        keyword = keyword.lower()
        results = []

        for category in self.categories.values():
            if (keyword in category.name.lower() or
                keyword in category.description.lower()):
                results.append(asdict(category))

        return results

    def export_categories(self, file_path: str) -> bool:
        """导出分类配置"""
        try:
            data = {
                'categories': self.get_custom_categories(),
                'export_time': time.time(),
                'version': '1.0'
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print(f"✅ 分类配置已导出到: {file_path}")
            return True

        except Exception as e:
            print(f"导出分类配置失败: {e}")
            return False

    def import_categories(self, file_path: str) -> bool:
        """导入分类配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            imported_count = 0
            for cat_data in data.get('categories', []):
                # 检查名称是否冲突
                if not self._is_name_exists(cat_data['name']):
                    category = Category(
                        id=self._generate_category_id(cat_data['name']),
                        name=cat_data['name'],
                        type=CategoryType.CUSTOM,
                        icon=cat_data.get('icon', '📁'),
                        color=cat_data.get('color', '#4A90E2'),
                        description=cat_data.get('description', ''),
                        parent_id=cat_data.get('parent_id')
                    )
                    self.categories[category.id] = category
                    imported_count += 1

            if imported_count > 0:
                self.save_categories()
                self.categories_loaded.emit(self.get_all_categories())

            print(f"✅ 已导入 {imported_count} 个分类")
            return True

        except Exception as e:
            print(f"导入分类配置失败: {e}")
            return False

    def get_category_stats(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        total_categories = len(self.categories)
        system_categories = len([c for c in self.categories.values() if c.type == CategoryType.SYSTEM])
        custom_categories = len([c for c in self.categories.values() if c.type == CategoryType.CUSTOM])
        total_files = sum(c.file_count for c in self.categories.values())

        return {
            'total_categories': total_categories,
            'system_categories': system_categories,
            'custom_categories': custom_categories,
            'total_files': total_files,
            'categories_with_files': len([c for c in self.categories.values() if c.file_count > 0])
        }

# 全局分类管理器实例
_category_manager = None

def get_category_manager() -> CategoryManager:
    """获取全局分类管理器实例"""
    global _category_manager
    if _category_manager is None:
        _category_manager = CategoryManager()
    return _category_manager

def cleanup_category_manager():
    """清理全局分类管理器"""
    global _category_manager
    if _category_manager:
        _category_manager.save_categories()
        _category_manager = None
