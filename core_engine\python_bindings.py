#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python绑定接口
使用ctypes调用C++性能引擎
"""

import ctypes
import os
import sys
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# 文件项目结构
class FileItem(ctypes.Structure):
    _fields_ = [
        ("id", ctypes.c_int),
        ("name", ctypes.c_char_p),
        ("file_type", ctypes.c_char_p),
        ("file_path", ctypes.c_char_p),
        ("size", ctypes.c_longlong),
        ("created_time", ctypes.c_char_p),
        ("width", ctypes.c_int),
        ("height", ctypes.c_int),
        ("rating", ctypes.c_int)
    ]

class PerformanceEngineWrapper:
    """C++性能引擎的Python包装器"""
    
    # 排序键枚举
    SORT_NAME = 0
    SORT_SIZE = 1
    SORT_DATE = 2
    SORT_TYPE = 3
    SORT_RATING = 4
    
    def __init__(self, library_path: Optional[str] = None):
        """初始化性能引擎"""
        self.engine = None
        self.lib = None
        
        # 加载动态库
        if library_path is None:
            library_path = self._find_library()
        
        try:
            self.lib = ctypes.CDLL(library_path)
            self._setup_function_signatures()
            self._create_engine()
            print(f"✅ C++性能引擎加载成功: {library_path}")
        except Exception as e:
            print(f"❌ C++性能引擎加载失败: {e}")
            print("🔄 回退到Python实现")
            self.lib = None
    
    def _find_library(self) -> str:
        """查找动态库文件"""
        # 根据平台确定库文件名
        if sys.platform == "win32":
            lib_name = "performance_engine.dll"
        elif sys.platform == "darwin":
            lib_name = "libperformance_engine.dylib"
        else:
            lib_name = "libperformance_engine.so"
        
        # 搜索路径
        search_paths = [
            Path(__file__).parent,
            Path(__file__).parent / "build",
            Path(__file__).parent / "lib",
            Path.cwd(),
            Path.cwd() / "core_engine",
            Path.cwd() / "build"
        ]
        
        for path in search_paths:
            lib_path = path / lib_name
            if lib_path.exists():
                return str(lib_path)
        
        raise FileNotFoundError(f"找不到性能引擎库文件: {lib_name}")
    
    def _setup_function_signatures(self):
        """设置函数签名"""
        if not self.lib:
            return
        
        # 引擎管理
        self.lib.create_engine.restype = ctypes.c_void_p
        self.lib.destroy_engine.argtypes = [ctypes.c_void_p]
        
        # 数据初始化
        self.lib.engine_initialize_data.argtypes = [
            ctypes.c_void_p, ctypes.POINTER(FileItem), ctypes.c_int
        ]
        
        # 搜索和过滤
        self.lib.engine_search_and_filter.argtypes = [
            ctypes.c_void_p, ctypes.c_char_p, ctypes.c_char_p, ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.engine_search_and_filter.restype = ctypes.POINTER(ctypes.c_int)
        
        # 排序
        self.lib.engine_sort_results.argtypes = [
            ctypes.c_void_p, ctypes.POINTER(ctypes.c_int), ctypes.c_int,
            ctypes.c_int, ctypes.c_bool, ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.engine_sort_results.restype = ctypes.POINTER(ctypes.c_int)
        
        # 虚拟滚动
        self.lib.engine_update_visible_range.argtypes = [
            ctypes.c_void_p, ctypes.c_int, ctypes.c_int, ctypes.c_int
        ]
        
        self.lib.engine_get_items_to_load.argtypes = [
            ctypes.c_void_p, ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.engine_get_items_to_load.restype = ctypes.POINTER(ctypes.c_int)
        
        self.lib.engine_get_items_to_unload.argtypes = [
            ctypes.c_void_p, ctypes.POINTER(ctypes.c_int)
        ]
        self.lib.engine_get_items_to_unload.restype = ctypes.POINTER(ctypes.c_int)
        
        self.lib.engine_mark_item_loaded.argtypes = [ctypes.c_void_p, ctypes.c_int]
        
        # 缩略图管理
        self.lib.engine_request_thumbnail.argtypes = [
            ctypes.c_void_p, ctypes.c_int, ctypes.c_char_p, ctypes.c_int
        ]
        
        self.lib.engine_is_thumbnail_completed.argtypes = [ctypes.c_void_p, ctypes.c_int]
        self.lib.engine_is_thumbnail_completed.restype = ctypes.c_bool
        
        # 数据获取
        self.lib.engine_get_item.argtypes = [ctypes.c_void_p, ctypes.c_int]
        self.lib.engine_get_item.restype = ctypes.POINTER(FileItem)
        
        # 内存管理
        self.lib.free_int_array.argtypes = [ctypes.POINTER(ctypes.c_int)]
        self.lib.free_file_item.argtypes = [ctypes.POINTER(FileItem)]
    
    def _create_engine(self):
        """创建引擎实例"""
        if self.lib:
            self.engine = self.lib.create_engine()
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.lib and self.engine:
            self.lib.destroy_engine(self.engine)
            self.engine = None
    
    def is_available(self) -> bool:
        """检查C++引擎是否可用"""
        return self.lib is not None and self.engine is not None
    
    def initialize_data(self, items: List[Dict[str, Any]]) -> bool:
        """初始化数据"""
        if not self.is_available():
            return False
        
        try:
            # 转换Python数据到C结构
            c_items = (FileItem * len(items))()
            
            for i, item in enumerate(items):
                c_items[i].id = item.get('id', 0)
                c_items[i].name = item.get('name', '').encode('utf-8')
                c_items[i].file_type = item.get('file_type', '').encode('utf-8')
                c_items[i].file_path = item.get('file_path', '').encode('utf-8')
                c_items[i].size = item.get('size', 0)
                c_items[i].created_time = item.get('created_time', '').encode('utf-8')
                c_items[i].width = item.get('width', 0)
                c_items[i].height = item.get('height', 0)
                c_items[i].rating = item.get('rating', 0)
            
            # 调用C++函数
            self.lib.engine_initialize_data(self.engine, c_items, len(items))
            return True
            
        except Exception as e:
            print(f"数据初始化失败: {e}")
            return False
    
    def search_and_filter(self, query: str = "", file_type_filter: str = "") -> List[int]:
        """搜索和过滤"""
        if not self.is_available():
            return []
        
        try:
            result_count = ctypes.c_int()
            
            query_bytes = query.encode('utf-8') if query else None
            filter_bytes = file_type_filter.encode('utf-8') if file_type_filter else None
            
            result_ptr = self.lib.engine_search_and_filter(
                self.engine, query_bytes, filter_bytes, ctypes.byref(result_count)
            )
            
            if not result_ptr or result_count.value == 0:
                return []
            
            # 转换结果
            results = []
            for i in range(result_count.value):
                results.append(result_ptr[i])
            
            # 释放内存
            self.lib.free_int_array(result_ptr)
            
            return results
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return []
    
    def sort_results(self, indices: List[int], sort_key: int, reverse: bool = False) -> List[int]:
        """排序结果"""
        if not self.is_available() or not indices:
            return indices
        
        try:
            # 转换输入
            c_indices = (ctypes.c_int * len(indices))(*indices)
            result_count = ctypes.c_int()
            
            result_ptr = self.lib.engine_sort_results(
                self.engine, c_indices, len(indices), sort_key, reverse, ctypes.byref(result_count)
            )
            
            if not result_ptr or result_count.value == 0:
                return indices
            
            # 转换结果
            results = []
            for i in range(result_count.value):
                results.append(result_ptr[i])
            
            # 释放内存
            self.lib.free_int_array(result_ptr)
            
            return results
            
        except Exception as e:
            print(f"排序失败: {e}")
            return indices
    
    def update_visible_range(self, start_index: int, visible_count: int, total_count: int):
        """更新可见范围"""
        if self.is_available():
            self.lib.engine_update_visible_range(self.engine, start_index, visible_count, total_count)
    
    def get_items_to_load(self) -> List[int]:
        """获取需要加载的项目"""
        if not self.is_available():
            return []
        
        try:
            count = ctypes.c_int()
            result_ptr = self.lib.engine_get_items_to_load(self.engine, ctypes.byref(count))
            
            if not result_ptr or count.value == 0:
                return []
            
            results = []
            for i in range(count.value):
                results.append(result_ptr[i])
            
            self.lib.free_int_array(result_ptr)
            return results
            
        except Exception as e:
            print(f"获取加载项目失败: {e}")
            return []
    
    def get_items_to_unload(self) -> List[int]:
        """获取需要卸载的项目"""
        if not self.is_available():
            return []
        
        try:
            count = ctypes.c_int()
            result_ptr = self.lib.engine_get_items_to_unload(self.engine, ctypes.byref(count))
            
            if not result_ptr or count.value == 0:
                return []
            
            results = []
            for i in range(count.value):
                results.append(result_ptr[i])
            
            self.lib.free_int_array(result_ptr)
            return results
            
        except Exception as e:
            print(f"获取卸载项目失败: {e}")
            return []
    
    def mark_item_loaded(self, index: int):
        """标记项目已加载"""
        if self.is_available():
            self.lib.engine_mark_item_loaded(self.engine, index)
    
    def request_thumbnail(self, item_id: int, file_path: str, priority: int = 1):
        """请求缩略图"""
        if self.is_available():
            path_bytes = file_path.encode('utf-8')
            self.lib.engine_request_thumbnail(self.engine, item_id, path_bytes, priority)
    
    def is_thumbnail_completed(self, item_id: int) -> bool:
        """检查缩略图是否完成"""
        if not self.is_available():
            return False
        
        return self.lib.engine_is_thumbnail_completed(self.engine, item_id)
    
    def get_item(self, index: int) -> Optional[Dict[str, Any]]:
        """获取数据项"""
        if not self.is_available():
            return None
        
        try:
            item_ptr = self.lib.engine_get_item(self.engine, index)
            if not item_ptr:
                return None
            
            item = item_ptr.contents
            result = {
                'id': item.id,
                'name': item.name.decode('utf-8') if item.name else '',
                'file_type': item.file_type.decode('utf-8') if item.file_type else '',
                'file_path': item.file_path.decode('utf-8') if item.file_path else '',
                'size': item.size,
                'created_time': item.created_time.decode('utf-8') if item.created_time else '',
                'width': item.width,
                'height': item.height,
                'rating': item.rating
            }
            
            self.lib.free_file_item(item_ptr)
            return result
            
        except Exception as e:
            print(f"获取数据项失败: {e}")
            return None

# 全局引擎实例
_global_engine = None

def get_performance_engine() -> PerformanceEngineWrapper:
    """获取全局性能引擎实例"""
    global _global_engine
    if _global_engine is None:
        _global_engine = PerformanceEngineWrapper()
    return _global_engine

def cleanup_performance_engine():
    """清理全局性能引擎"""
    global _global_engine
    if _global_engine:
        _global_engine.cleanup()
        _global_engine = None
