#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C++引擎性能测试工具
对比C++引擎和Python实现的性能差异
"""

import sys
import time
import random
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                               QTabWidget, QProgressBar, QSpinBox, QCheckBox,
                               QSplitter, QFrame, QComboBox)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont

# 导入C++加速组件
from ui.components.cpp_accelerated_list import (CppAcceleratedListView, 
                                               PerformanceMonitorWidget, 
                                               create_test_data)

class PerformanceBenchmarkThread(QThread):
    """性能基准测试线程"""
    
    test_completed = Signal(str, dict)
    progress_updated = Signal(int)
    log_message = Signal(str)
    
    def __init__(self, test_config):
        super().__init__()
        self.test_config = test_config
        self.results = {}
    
    def run(self):
        """运行基准测试"""
        try:
            data_size = self.test_config['data_size']
            test_types = self.test_config['test_types']
            
            self.log_message.emit(f"🚀 开始性能基准测试 (数据量: {data_size})")
            
            # 生成测试数据
            self.log_message.emit("📊 生成测试数据...")
            test_data = create_test_data(data_size)
            self.progress_updated.emit(10)
            
            # 测试搜索性能
            if 'search' in test_types:
                self.log_message.emit("🔍 测试搜索性能...")
                search_results = self._test_search_performance(test_data)
                self.results['search'] = search_results
                self.progress_updated.emit(40)
            
            # 测试排序性能
            if 'sort' in test_types:
                self.log_message.emit("📊 测试排序性能...")
                sort_results = self._test_sort_performance(test_data)
                self.results['sort'] = sort_results
                self.progress_updated.emit(70)
            
            # 测试加载性能
            if 'load' in test_types:
                self.log_message.emit("📥 测试加载性能...")
                load_results = self._test_load_performance(test_data)
                self.results['load'] = load_results
                self.progress_updated.emit(90)
            
            self.progress_updated.emit(100)
            self.test_completed.emit("benchmark", self.results)
            
        except Exception as e:
            self.log_message.emit(f"❌ 测试失败: {e}")
            self.test_completed.emit("error", {"error": str(e)})
    
    def _test_search_performance(self, data):
        """测试搜索性能"""
        # 创建列表视图
        list_view = CppAcceleratedListView()
        
        # 初始化数据
        start_time = time.time()
        list_view.setDataSource(data)
        init_time = time.time() - start_time
        
        # 测试搜索查询
        search_queries = ['test', 'file', 'image', 'video', '001', '999', 'jpg']
        search_times = []
        
        for query in search_queries:
            start_time = time.time()
            result_count = list_view.searchAndFilter(query)
            search_time = time.time() - start_time
            search_times.append(search_time)
            
            self.log_message.emit(f"  搜索 '{query}': {result_count} 项, {search_time*1000:.1f}ms")
        
        return {
            'init_time': init_time,
            'search_times': search_times,
            'avg_search_time': sum(search_times) / len(search_times),
            'min_search_time': min(search_times),
            'max_search_time': max(search_times),
            'total_queries': len(search_queries)
        }
    
    def _test_sort_performance(self, data):
        """测试排序性能"""
        list_view = CppAcceleratedListView()
        list_view.setDataSource(data)
        
        # 测试不同排序
        sort_keys = ['name', 'size', 'date', 'type']
        sort_times = []
        
        for sort_key in sort_keys:
            # 正序
            start_time = time.time()
            result_count = list_view.sortResults(sort_key, False)
            sort_time = time.time() - start_time
            sort_times.append(sort_time)
            
            self.log_message.emit(f"  排序 {sort_key} (正序): {result_count} 项, {sort_time*1000:.1f}ms")
            
            # 倒序
            start_time = time.time()
            result_count = list_view.sortResults(sort_key, True)
            sort_time = time.time() - start_time
            sort_times.append(sort_time)
            
            self.log_message.emit(f"  排序 {sort_key} (倒序): {result_count} 项, {sort_time*1000:.1f}ms")
        
        return {
            'sort_times': sort_times,
            'avg_sort_time': sum(sort_times) / len(sort_times),
            'min_sort_time': min(sort_times),
            'max_sort_time': max(sort_times),
            'total_sorts': len(sort_times)
        }
    
    def _test_load_performance(self, data):
        """测试加载性能"""
        # 测试不同大小的数据集
        data_sizes = [1000, 5000, 10000, len(data)]
        load_times = []
        
        for size in data_sizes:
            if size > len(data):
                continue
                
            subset_data = data[:size]
            
            start_time = time.time()
            list_view = CppAcceleratedListView()
            list_view.setDataSource(subset_data)
            load_time = time.time() - start_time
            
            load_times.append(load_time)
            self.log_message.emit(f"  加载 {size} 项: {load_time*1000:.1f}ms")
        
        return {
            'data_sizes': data_sizes[:len(load_times)],
            'load_times': load_times,
            'avg_load_time': sum(load_times) / len(load_times) if load_times else 0,
            'items_per_second': [size/time if time > 0 else 0 for size, time in zip(data_sizes[:len(load_times)], load_times)]
        }

class CppEngineTestWindow(QMainWindow):
    """C++引擎测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("C++引擎性能测试工具")
        self.setGeometry(100, 100, 1400, 900)
        
        self.setup_ui()
        
        # 检查C++引擎状态
        self.check_cpp_engine_status()
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QHBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建左侧控制面板
        self.create_control_panel(layout)
        
        # 创建右侧测试区域
        self.create_test_area(layout)
    
    def create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.StyledPanel)
        control_frame.setMaximumWidth(400)
        
        layout = QVBoxLayout(control_frame)
        
        # 标题
        title = QLabel("⚡ C++引擎性能测试")
        title.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        layout.addWidget(title)
        
        # 引擎状态
        self.engine_status_label = QLabel("🔍 检测引擎状态...")
        layout.addWidget(self.engine_status_label)
        
        # 测试参数
        params_frame = QFrame()
        params_frame.setFrameStyle(QFrame.Box)
        params_layout = QVBoxLayout(params_frame)
        
        params_title = QLabel("🔧 测试参数")
        params_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        params_layout.addWidget(params_title)
        
        # 数据大小
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("数据大小:"))
        self.data_size_spin = QSpinBox()
        self.data_size_spin.setRange(1000, 100000)
        self.data_size_spin.setValue(20000)
        self.data_size_spin.setSuffix(" 项")
        size_layout.addWidget(self.data_size_spin)
        params_layout.addLayout(size_layout)
        
        # 测试类型
        self.test_search = QCheckBox("搜索性能测试")
        self.test_search.setChecked(True)
        params_layout.addWidget(self.test_search)
        
        self.test_sort = QCheckBox("排序性能测试")
        self.test_sort.setChecked(True)
        params_layout.addWidget(self.test_sort)
        
        self.test_load = QCheckBox("加载性能测试")
        self.test_load.setChecked(True)
        params_layout.addWidget(self.test_load)
        
        layout.addWidget(params_frame)
        
        # 控制按钮
        buttons_layout = QVBoxLayout()
        
        self.compile_btn = QPushButton("🔨 编译C++引擎")
        self.compile_btn.clicked.connect(self.compile_cpp_engine)
        buttons_layout.addWidget(self.compile_btn)
        
        self.benchmark_btn = QPushButton("🚀 运行基准测试")
        self.benchmark_btn.clicked.connect(self.run_benchmark_test)
        buttons_layout.addWidget(self.benchmark_btn)
        
        self.demo_btn = QPushButton("🎯 演示C++加速")
        self.demo_btn.clicked.connect(self.show_cpp_demo)
        buttons_layout.addWidget(self.demo_btn)
        
        layout.addLayout(buttons_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 测试日志
        log_frame = QFrame()
        log_frame.setFrameStyle(QFrame.Box)
        log_layout = QVBoxLayout(log_frame)
        
        log_title = QLabel("📋 测试日志")
        log_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        log_layout.addWidget(log_title)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_frame)
        
        layout.addStretch()
        parent_layout.addWidget(control_frame)
    
    def create_test_area(self, parent_layout):
        """创建测试区域"""
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # C++加速演示标签页
        self.cpp_demo_tab = QWidget()
        self.tab_widget.addTab(self.cpp_demo_tab, "⚡ C++加速演示")
        
        # 性能对比标签页
        self.benchmark_tab = QWidget()
        self.tab_widget.addTab(self.benchmark_tab, "📊 性能对比")
        
        parent_layout.addWidget(self.tab_widget)
    
    def check_cpp_engine_status(self):
        """检查C++引擎状态"""
        try:
            from core_engine.python_bindings import get_performance_engine
            engine = get_performance_engine()
            
            if engine.is_available():
                self.engine_status_label.setText("✅ C++引擎: 可用")
                self.engine_status_label.setStyleSheet("color: green;")
                self.log("✅ C++性能引擎检测成功")
            else:
                self.engine_status_label.setText("❌ C++引擎: 不可用")
                self.engine_status_label.setStyleSheet("color: red;")
                self.log("❌ C++引擎不可用，需要编译")
                
        except ImportError as e:
            self.engine_status_label.setText("❌ C++引擎: 未找到")
            self.engine_status_label.setStyleSheet("color: red;")
            self.log(f"❌ C++引擎导入失败: {e}")
    
    def compile_cpp_engine(self):
        """编译C++引擎"""
        self.log("🔨 开始编译C++引擎...")
        
        try:
            import subprocess
            import os
            
            # 运行编译脚本
            build_script = Path(__file__).parent / "core_engine" / "build.py"
            
            if build_script.exists():
                result = subprocess.run([sys.executable, str(build_script)], 
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log("✅ C++引擎编译成功!")
                    self.log(result.stdout)
                    self.check_cpp_engine_status()
                else:
                    self.log("❌ C++引擎编译失败:")
                    self.log(result.stderr)
            else:
                self.log(f"❌ 编译脚本不存在: {build_script}")
                
        except Exception as e:
            self.log(f"❌ 编译异常: {e}")
    
    def run_benchmark_test(self):
        """运行基准测试"""
        # 收集测试配置
        test_config = {
            'data_size': self.data_size_spin.value(),
            'test_types': []
        }
        
        if self.test_search.isChecked():
            test_config['test_types'].append('search')
        if self.test_sort.isChecked():
            test_config['test_types'].append('sort')
        if self.test_load.isChecked():
            test_config['test_types'].append('load')
        
        if not test_config['test_types']:
            self.log("❌ 请至少选择一个测试类型")
            return
        
        # 启动测试线程
        self.benchmark_thread = PerformanceBenchmarkThread(test_config)
        self.benchmark_thread.test_completed.connect(self._on_benchmark_completed)
        self.benchmark_thread.progress_updated.connect(self.progress_bar.setValue)
        self.benchmark_thread.log_message.connect(self.log)
        
        self.progress_bar.setVisible(True)
        self.benchmark_btn.setEnabled(False)
        
        self.benchmark_thread.start()
    
    def _on_benchmark_completed(self, test_type, results):
        """基准测试完成"""
        self.progress_bar.setVisible(False)
        self.benchmark_btn.setEnabled(True)
        
        if test_type == "error":
            self.log(f"❌ 测试失败: {results.get('error', 'Unknown error')}")
            return
        
        # 显示结果
        self.log("\n🎉 基准测试完成!")
        self._display_benchmark_results(results)
    
    def _display_benchmark_results(self, results):
        """显示基准测试结果"""
        self.log("\n📊 性能测试结果:")
        
        if 'search' in results:
            search_data = results['search']
            self.log(f"🔍 搜索性能:")
            self.log(f"  • 平均搜索时间: {search_data['avg_search_time']*1000:.1f}ms")
            self.log(f"  • 最快搜索: {search_data['min_search_time']*1000:.1f}ms")
            self.log(f"  • 最慢搜索: {search_data['max_search_time']*1000:.1f}ms")
        
        if 'sort' in results:
            sort_data = results['sort']
            self.log(f"📊 排序性能:")
            self.log(f"  • 平均排序时间: {sort_data['avg_sort_time']*1000:.1f}ms")
            self.log(f"  • 最快排序: {sort_data['min_sort_time']*1000:.1f}ms")
            self.log(f"  • 最慢排序: {sort_data['max_sort_time']*1000:.1f}ms")
        
        if 'load' in results:
            load_data = results['load']
            self.log(f"📥 加载性能:")
            self.log(f"  • 平均加载时间: {load_data['avg_load_time']*1000:.1f}ms")
            if load_data['items_per_second']:
                avg_speed = sum(load_data['items_per_second']) / len(load_data['items_per_second'])
                self.log(f"  • 平均加载速度: {avg_speed:.0f} 项/秒")
    
    def show_cpp_demo(self):
        """显示C++加速演示"""
        # 清空演示标签页
        if hasattr(self, 'cpp_demo_widget'):
            self.cpp_demo_widget.setParent(None)
        
        # 创建演示控件
        demo_layout = QVBoxLayout(self.cpp_demo_tab)
        
        # 创建C++加速列表
        self.cpp_list_view = CppAcceleratedListView()
        
        # 创建性能监控
        self.performance_monitor = PerformanceMonitorWidget(self.cpp_list_view)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(self.cpp_list_view)
        splitter.addWidget(self.performance_monitor)
        splitter.setSizes([800, 300])
        
        demo_layout.addWidget(splitter)
        
        # 加载测试数据
        test_data = create_test_data(10000)
        self.cpp_list_view.setDataSource(test_data)
        
        # 切换到演示标签页
        self.tab_widget.setCurrentWidget(self.cpp_demo_tab)
        
        self.log("🎯 C++加速演示已加载")
    
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        self.log_text.ensureCursorVisible()
        QApplication.processEvents()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = CppEngineTestWindow()
    window.show()
    
    print("C++引擎性能测试工具启动成功！")
    print("功能特性：")
    print("1. ⚡ C++核心引擎 - 极致性能优化")
    print("2. 🔍 智能搜索索引 - 毫秒级搜索")
    print("3. 📊 高速排序算法 - 多线程并行")
    print("4. 🎯 虚拟滚动管理 - 内存优化")
    print("5. 📈 实时性能监控 - 性能可视化")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
