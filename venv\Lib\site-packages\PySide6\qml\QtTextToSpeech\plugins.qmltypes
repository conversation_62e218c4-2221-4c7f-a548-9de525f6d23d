import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qdeclarativetexttospeech_p.h"
        name: "QDeclarativeTextToSpeech"
        accessSemantics: "reference"
        prototype: "QTextToSpeech"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtTextToSpeech/TextToSpeech 6.0",
            "QtTextToSpeech/TextToSpeech 6.6"
        ]
        exportMetaObjectRevisions: [1536, 1542]
        Property {
            name: "engine"
            type: "QString"
            read: "engine"
            write: "setEngine"
            notify: "engineChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "engineParameters"
            revision: 1542
            type: "QVariantMap"
            read: "engineParameters"
            write: "setEngineParameters"
            notify: "engineParametersChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "engineChanged"
            Parameter { type: "QString" }
        }
        Signal { name: "engineParametersChanged"; revision: 1542 }
        Method {
            name: "findVoices"
            revision: 1542
            type: "QVoice"
            isList: true
            isMethodConstant: true
            Parameter { name: "criteria"; type: "QVariantMap" }
        }
    }
    Component {
        file: "qtexttospeech.h"
        name: "QTextToSpeech"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "State"
            values: ["Ready", "Speaking", "Paused", "Error", "Synthesizing"]
        }
        Enum {
            name: "ErrorReason"
            isScoped: true
            values: [
                "NoError",
                "Initialization",
                "Configuration",
                "Input",
                "Playback"
            ]
        }
        Enum {
            name: "BoundaryHint"
            isScoped: true
            values: ["Default", "Immediate", "Word", "Sentence", "Utterance"]
        }
        Enum {
            name: "Capabilities"
            alias: "Capability"
            isFlag: true
            isScoped: true
            values: [
                "None",
                "Speak",
                "PauseResume",
                "WordByWordProgress",
                "Synthesize"
            ]
        }
        Property {
            name: "engine"
            type: "QString"
            read: "engine"
            write: "setEngine"
            notify: "engineChanged"
            index: 0
        }
        Property {
            name: "state"
            type: "State"
            read: "state"
            notify: "stateChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "volume"
            type: "double"
            read: "volume"
            write: "setVolume"
            notify: "volumeChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "rate"
            type: "double"
            read: "rate"
            write: "setRate"
            notify: "rateChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "pitch"
            type: "double"
            read: "pitch"
            write: "setPitch"
            notify: "pitchChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            notify: "localeChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "voice"
            type: "QVoice"
            read: "voice"
            write: "setVoice"
            notify: "voiceChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "engineCapabilities"
            revision: 1542
            type: "Capabilities"
            read: "engineCapabilities"
            notify: "engineChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "engineChanged"
            Parameter { name: "engine"; type: "QString" }
        }
        Signal {
            name: "stateChanged"
            Parameter { name: "state"; type: "QTextToSpeech::State" }
        }
        Signal {
            name: "errorOccurred"
            Parameter { name: "error"; type: "QTextToSpeech::ErrorReason" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Signal {
            name: "localeChanged"
            Parameter { name: "locale"; type: "QLocale" }
        }
        Signal {
            name: "rateChanged"
            Parameter { name: "rate"; type: "double" }
        }
        Signal {
            name: "pitchChanged"
            Parameter { name: "pitch"; type: "double" }
        }
        Signal {
            name: "volumeChanged"
            Parameter { name: "volume"; type: "double" }
        }
        Signal {
            name: "voiceChanged"
            Parameter { name: "voice"; type: "QVoice" }
        }
        Signal {
            name: "sayingWord"
            Parameter { name: "word"; type: "QString" }
            Parameter { name: "id"; type: "qsizetype" }
            Parameter { name: "start"; type: "qsizetype" }
            Parameter { name: "length"; type: "qsizetype" }
        }
        Signal {
            name: "aboutToSynthesize"
            Parameter { name: "id"; type: "qsizetype" }
        }
        Method {
            name: "say"
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "enqueue"
            type: "qsizetype"
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "stop"
            Parameter { name: "boundaryHint"; type: "QTextToSpeech::BoundaryHint" }
        }
        Method { name: "stop"; isCloned: true }
        Method {
            name: "pause"
            Parameter { name: "boundaryHint"; type: "QTextToSpeech::BoundaryHint" }
        }
        Method { name: "pause"; isCloned: true }
        Method { name: "resume" }
        Method {
            name: "setLocale"
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "setRate"
            Parameter { name: "rate"; type: "double" }
        }
        Method {
            name: "setPitch"
            Parameter { name: "pitch"; type: "double" }
        }
        Method {
            name: "setVolume"
            Parameter { name: "volume"; type: "double" }
        }
        Method {
            name: "setVoice"
            Parameter { name: "voice"; type: "QVoice" }
        }
        Method {
            name: "setEngine"
            type: "bool"
            Parameter { name: "engine"; type: "QString" }
            Parameter { name: "params"; type: "QVariantMap" }
        }
        Method {
            name: "setEngine"
            type: "bool"
            isCloned: true
            Parameter { name: "engine"; type: "QString" }
        }
        Method { name: "errorReason"; type: "QTextToSpeech::ErrorReason"; isMethodConstant: true }
        Method { name: "errorString"; type: "QString"; isMethodConstant: true }
        Method { name: "availableLocales"; type: "QLocale"; isList: true; isMethodConstant: true }
        Method { name: "availableVoices"; type: "QVoice"; isList: true; isMethodConstant: true }
        Method { name: "availableEngines"; type: "QStringList" }
    }
    Component {
        file: "qtexttospeech_qmltypes_p.h"
        name: "QVoice"
        accessSemantics: "value"
        exports: ["QtTextToSpeech/voice 6.0", "QtTextToSpeech/voice 6.6"]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1542]
        Enum {
            name: "Gender"
            values: ["Male", "Female", "Unknown"]
        }
        Enum {
            name: "Age"
            values: ["Child", "Teenager", "Adult", "Senior", "Other"]
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "gender"
            type: "Gender"
            read: "gender"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "age"
            type: "Age"
            read: "age"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "language"
            revision: 1542
            type: "QLocale::Language"
            read: "language"
            index: 4
            isReadonly: true
        }
    }
    Component {
        file: "qtexttospeech_qmltypes_p.h"
        name: "QVoiceDerived"
        accessSemantics: "none"
        prototype: "QVoice"
        exports: ["QtTextToSpeech/Voice 6.0", "QtTextToSpeech/Voice 6.6"]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1542]
    }
    Component {
        file: "qvoiceselectorattached_p.h"
        name: "QVoiceSelectorAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtTextToSpeech/VoiceSelector 6.6"]
        isCreatable: false
        exportMetaObjectRevisions: [1542]
        attachedType: "QVoiceSelectorAttached"
        Property {
            name: "name"
            type: "QVariant"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "gender"
            type: "QVoice::Gender"
            read: "gender"
            write: "setGender"
            notify: "genderChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "age"
            type: "QVoice::Age"
            read: "age"
            write: "setAge"
            notify: "ageChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            notify: "localeChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "language"
            type: "QLocale"
            read: "language"
            write: "setLanguage"
            notify: "languageChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "nameChanged" }
        Signal { name: "genderChanged" }
        Signal { name: "ageChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "languageChanged" }
        Method { name: "select" }
    }
}
