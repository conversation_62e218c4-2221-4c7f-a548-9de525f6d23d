# 预览面板组件
# 功能：右侧预览面板，显示选中文件的预览图、属性信息、标签管理和操作按钮

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QScrollArea, QFrame, QTextEdit,
                               QGroupBox, QGridLayout, QLineEdit, QSpinBox,
                               QSlider, QComboBox, QListWidget, QListWidgetItem,
                               QTabWidget, QProgressBar, QCheckBox, QMessageBox,
                               QInputDialog)
from PySide6.QtCore import Qt, Signal, QSize, QTimer, QThread
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont, QMovie

import os
from pathlib import Path
from typing import Dict, Any, List, Optional

class PreviewPanelWidget(QWidget):
    """预览面板控件类"""

    # 信号定义
    tag_added = Signal(int, str)  # 标签添加信号
    tag_removed = Signal(int, str)  # 标签移除信号
    rating_changed = Signal(int, int)  # 评分变更信号
    favorite_changed = Signal(int, bool)  # 收藏状态变更信号

    def __init__(self, theme_manager, db_manager, config_manager):
        super().__init__()

        self.theme_manager = theme_manager
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 界面组件
        self.preview_label = None
        self.info_widget = None
        self.tags_widget = None
        self.actions_widget = None

        # 当前项目
        self.current_item = None
        self.current_item_id = None

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 设置固定宽度
        self.setFixedWidth(300)

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        main_layout.addWidget(scroll_area)

        # 创建内容控件
        content_widget = QWidget()
        scroll_area.setWidget(content_widget)

        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(5, 5, 5, 5)
        content_layout.setSpacing(15)

        # 预览区域
        preview_group = self.create_preview_group()
        content_layout.addWidget(preview_group)

        # 基本信息区域
        info_group = self.create_info_group()
        content_layout.addWidget(info_group)

        # 标签管理区域
        tags_group = self.create_tags_group()
        content_layout.addWidget(tags_group)

        # 操作按钮区域
        actions_group = self.create_actions_group()
        content_layout.addWidget(actions_group)

        # 添加弹性空间
        content_layout.addStretch()

        # 默认显示空状态
        self.show_empty_state()

    def create_preview_group(self):
        """创建预览区域"""
        group = QGroupBox("预览")
        layout = QVBoxLayout(group)

        # 预览标签
        self.preview_label = QLabel()
        self.preview_label.setFixedSize(280, 200)
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #f8f9fa;
            }
        """)
        layout.addWidget(self.preview_label)

        # 预览控制按钮
        controls_layout = QHBoxLayout()

        self.zoom_in_btn = QPushButton("放大")
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        controls_layout.addWidget(self.zoom_in_btn)

        self.zoom_out_btn = QPushButton("缩小")
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        controls_layout.addWidget(self.zoom_out_btn)

        self.fit_btn = QPushButton("适应")
        self.fit_btn.clicked.connect(self.fit_to_window)
        controls_layout.addWidget(self.fit_btn)

        layout.addLayout(controls_layout)

        return group

    def create_info_group(self):
        """创建信息区域"""
        group = QGroupBox("属性信息")
        layout = QVBoxLayout(group)

        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        tab_widget.addTab(basic_tab, "基本")

        # 详细信息标签页
        detail_tab = self.create_detail_info_tab()
        tab_widget.addTab(detail_tab, "详细")

        # EXIF信息标签页
        exif_tab = self.create_exif_info_tab()
        tab_widget.addTab(exif_tab, "EXIF")

        return group

    def create_basic_info_tab(self):
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QGridLayout(widget)
        layout.setSpacing(5)

        # 文件名
        layout.addWidget(QLabel("文件名:"), 0, 0)
        self.name_label = QLabel("-")
        self.name_label.setWordWrap(True)
        layout.addWidget(self.name_label, 0, 1)

        # 文件类型
        layout.addWidget(QLabel("类型:"), 1, 0)
        self.type_label = QLabel("-")
        layout.addWidget(self.type_label, 1, 1)

        # 文件大小
        layout.addWidget(QLabel("大小:"), 2, 0)
        self.size_label = QLabel("-")
        layout.addWidget(self.size_label, 2, 1)

        # 尺寸
        layout.addWidget(QLabel("尺寸:"), 3, 0)
        self.dimensions_label = QLabel("-")
        layout.addWidget(self.dimensions_label, 3, 1)

        # 创建时间
        layout.addWidget(QLabel("创建时间:"), 4, 0)
        self.created_label = QLabel("-")
        self.created_label.setWordWrap(True)
        layout.addWidget(self.created_label, 4, 1)

        # 修改时间
        layout.addWidget(QLabel("修改时间:"), 5, 0)
        self.modified_label = QLabel("-")
        self.modified_label.setWordWrap(True)
        layout.addWidget(self.modified_label, 5, 1)

        return widget

    def create_detail_info_tab(self):
        """创建详细信息标签页"""
        widget = QWidget()
        layout = QGridLayout(widget)
        layout.setSpacing(5)

        # 文件路径
        layout.addWidget(QLabel("路径:"), 0, 0)
        self.path_label = QLabel("-")
        self.path_label.setWordWrap(True)
        layout.addWidget(self.path_label, 0, 1)

        # MD5哈希
        layout.addWidget(QLabel("MD5:"), 1, 0)
        self.md5_label = QLabel("-")
        self.md5_label.setWordWrap(True)
        layout.addWidget(self.md5_label, 1, 1)

        # 主色调
        layout.addWidget(QLabel("主色调:"), 2, 0)
        self.color_label = QLabel("-")
        layout.addWidget(self.color_label, 2, 1)

        # 评分
        layout.addWidget(QLabel("评分:"), 3, 0)
        self.rating_spinbox = QSpinBox()
        self.rating_spinbox.setRange(0, 5)
        self.rating_spinbox.valueChanged.connect(self.on_rating_changed)
        layout.addWidget(self.rating_spinbox, 3, 1)

        # 收藏状态
        layout.addWidget(QLabel("收藏:"), 4, 0)
        self.favorite_checkbox = QCheckBox()
        self.favorite_checkbox.toggled.connect(self.on_favorite_changed)
        layout.addWidget(self.favorite_checkbox, 4, 1)

        return widget

    def create_exif_info_tab(self):
        """创建EXIF信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        self.exif_text = QTextEdit()
        self.exif_text.setReadOnly(True)
        self.exif_text.setMaximumHeight(150)
        layout.addWidget(self.exif_text)

        return widget

    def create_tags_group(self):
        """创建标签管理区域"""
        group = QGroupBox("标签管理")
        layout = QVBoxLayout(group)

        # 标签输入
        input_layout = QHBoxLayout()

        self.tag_input = QLineEdit()
        self.tag_input.setPlaceholderText("输入标签名称...")
        self.tag_input.returnPressed.connect(self.add_tag)
        input_layout.addWidget(self.tag_input)

        add_tag_btn = QPushButton("添加")
        add_tag_btn.clicked.connect(self.add_tag)
        input_layout.addWidget(add_tag_btn)

        layout.addLayout(input_layout)

        # 标签列表
        self.tags_list = QListWidget()
        self.tags_list.setMaximumHeight(120)
        layout.addWidget(self.tags_list)

        # AI标签区域
        ai_tags_label = QLabel("AI建议标签:")
        ai_tags_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        layout.addWidget(ai_tags_label)

        self.ai_tags_widget = QWidget()
        self.ai_tags_layout = QHBoxLayout(self.ai_tags_widget)
        self.ai_tags_layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.ai_tags_widget)

        return group

    def create_actions_group(self):
        """创建操作按钮区域"""
        group = QGroupBox("操作")
        layout = QVBoxLayout(group)

        # 第一行按钮
        row1_layout = QHBoxLayout()

        edit_btn = QPushButton("编辑")
        edit_btn.clicked.connect(self.edit_item)
        row1_layout.addWidget(edit_btn)

        copy_btn = QPushButton("复制")
        copy_btn.clicked.connect(self.copy_item)
        row1_layout.addWidget(copy_btn)

        layout.addLayout(row1_layout)

        # 第二行按钮
        row2_layout = QHBoxLayout()

        share_btn = QPushButton("分享")
        share_btn.clicked.connect(self.share_item)
        row2_layout.addWidget(share_btn)

        delete_btn = QPushButton("删除")
        delete_btn.clicked.connect(self.delete_item)
        delete_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        row2_layout.addWidget(delete_btn)

        layout.addLayout(row2_layout)

        # 添加到收藏夹按钮
        collection_btn = QPushButton("添加到收藏夹")
        collection_btn.clicked.connect(self.add_to_collection)
        layout.addWidget(collection_btn)

        return group

    def show_item(self, item_id: int):
        """显示指定项目"""
        try:
            # 从数据库获取项目信息
            item = self.db_manager.get_material(item_id)
            if not item:
                self.show_empty_state()
                return

            self.current_item = item
            self.current_item_id = item_id

            # 更新预览
            self.update_preview()

            # 更新信息
            self.update_info()

            # 更新标签
            self.update_tags()

            # 启用控件
            self.setEnabled(True)

        except Exception as e:
            print(f"显示项目失败: {e}")
            self.show_empty_state()

    def show_empty_state(self):
        """显示空状态"""
        self.current_item = None
        self.current_item_id = None

        # 清空预览
        self.preview_label.setText("未选择文件")
        self.preview_label.setFont(QFont("Microsoft YaHei", 12))

        # 清空信息
        self.name_label.setText("-")
        self.type_label.setText("-")
        self.size_label.setText("-")
        self.dimensions_label.setText("-")
        self.created_label.setText("-")
        self.modified_label.setText("-")
        self.path_label.setText("-")
        self.md5_label.setText("-")
        self.color_label.setText("-")
        self.rating_spinbox.setValue(0)
        self.favorite_checkbox.setChecked(False)
        self.exif_text.clear()

        # 清空标签
        self.tags_list.clear()
        self.tag_input.clear()

        # 禁用控件
        self.setEnabled(False)

    def update_preview(self):
        """更新预览"""
        if not self.current_item:
            return

        # TODO: 根据文件类型加载预览
        file_path = self.current_item.get('file_path', '')
        file_type = self.current_item.get('file_type', '')

        if file_type.startswith('image'):
            self.load_image_preview(file_path)
        elif file_type.startswith('video'):
            self.load_video_preview(file_path)
        else:
            # 显示文件类型图标
            self.preview_label.setText("📄")
            self.preview_label.setFont(QFont("", 48))

    def load_image_preview(self, file_path: str):
        """加载图片预览"""
        try:
            if os.path.exists(file_path):
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # 保存原始图片用于缩放
                    self.original_pixmap = pixmap
                    self.zoom_factor = 1.0

                    # 缩放到合适大小
                    scaled_pixmap = pixmap.scaled(
                        self.preview_label.size(),
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.preview_label.setPixmap(scaled_pixmap)
                else:
                    self.preview_label.setText("无法加载图片")
            else:
                self.preview_label.setText("文件不存在")
        except Exception as e:
            print(f"加载图片预览失败: {e}")
            self.preview_label.setText("预览失败")

    def load_video_preview(self, file_path: str):
        """加载视频预览"""
        # TODO: 实现视频预览
        self.preview_label.setText("🎬")
        self.preview_label.setFont(QFont("", 48))

    def update_info(self):
        """更新信息"""
        if not self.current_item:
            return

        # 基本信息
        self.name_label.setText(self.current_item.get('name', '-'))
        self.type_label.setText(self.current_item.get('file_type', '-'))

        # 文件大小
        size = self.current_item.get('size', 0)
        if size > 0:
            size_mb = size / (1024 * 1024)
            self.size_label.setText(f"{size_mb:.2f} MB")
        else:
            self.size_label.setText("-")

        # 尺寸
        width = self.current_item.get('width')
        height = self.current_item.get('height')
        if width and height:
            self.dimensions_label.setText(f"{width} x {height}")
        else:
            self.dimensions_label.setText("-")

        # 时间信息
        self.created_label.setText(self.current_item.get('created_time', '-'))
        self.modified_label.setText(self.current_item.get('modified_time', '-'))

        # 详细信息
        self.path_label.setText(self.current_item.get('file_path', '-'))
        self.md5_label.setText(self.current_item.get('md5_hash', '-'))
        self.color_label.setText(self.current_item.get('dominant_color', '-'))

        # 评分和收藏
        self.rating_spinbox.setValue(self.current_item.get('rating', 0))
        self.favorite_checkbox.setChecked(bool(self.current_item.get('favorite', False)))

        # EXIF信息
        metadata = self.current_item.get('metadata', '')
        self.exif_text.setPlainText(metadata if metadata else "无EXIF信息")

    def update_tags(self):
        """更新标签"""
        if not self.current_item_id:
            return

        # 清空现有标签
        self.tags_list.clear()

        # 获取标签
        tags = self.db_manager.get_material_tags(self.current_item_id)

        for tag in tags:
            item = QListWidgetItem(f"{tag['name']} ({tag['source']})")
            item.setData(Qt.UserRole, tag['name'])
            self.tags_list.addItem(item)

    def add_tag(self):
        """添加标签"""
        tag_name = self.tag_input.text().strip()
        if not tag_name or not self.current_item_id:
            return

        try:
            # 添加标签到数据库
            tag_id = self.db_manager.add_tag(tag_name)
            self.db_manager.add_material_tag(self.current_item_id, tag_id)

            # 更新界面
            self.update_tags()
            self.tag_input.clear()

            # 发送信号
            self.tag_added.emit(self.current_item_id, tag_name)

        except Exception as e:
            print(f"添加标签失败: {e}")

    def on_rating_changed(self, rating: int):
        """评分变更处理"""
        if self.current_item_id:
            try:
                self.db_manager.update_material(self.current_item_id, {'rating': rating})
                self.rating_changed.emit(self.current_item_id, rating)
            except Exception as e:
                print(f"更新评分失败: {e}")

    def on_favorite_changed(self, favorite: bool):
        """收藏状态变更处理"""
        if self.current_item_id:
            try:
                self.db_manager.update_material(self.current_item_id, {'favorite': favorite})
                self.favorite_changed.emit(self.current_item_id, favorite)
            except Exception as e:
                print(f"更新收藏状态失败: {e}")

    # 操作按钮处理函数
    def zoom_in(self):
        """放大预览"""
        try:
            if hasattr(self, 'zoom_factor'):
                self.zoom_factor = min(self.zoom_factor * 1.2, 5.0)
            else:
                self.zoom_factor = 1.2
            self._apply_zoom()
        except Exception as e:
            print(f"放大预览失败: {e}")

    def zoom_out(self):
        """缩小预览"""
        try:
            if hasattr(self, 'zoom_factor'):
                self.zoom_factor = max(self.zoom_factor / 1.2, 0.1)
            else:
                self.zoom_factor = 0.8
            self._apply_zoom()
        except Exception as e:
            print(f"缩小预览失败: {e}")

    def _apply_zoom(self):
        """应用缩放"""
        if self.current_item and hasattr(self, 'original_pixmap'):
            scaled_size = self.original_pixmap.size() * self.zoom_factor
            scaled_pixmap = self.original_pixmap.scaled(
                scaled_size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )
            self.preview_label.setPixmap(scaled_pixmap)

    def fit_to_window(self):
        """适应窗口"""
        if self.current_item:
            self.zoom_factor = 1.0
            self.update_preview()

    def edit_item(self):
        """编辑项目"""
        try:
            if self.current_item:
                import subprocess
                import os

                file_path = self.current_item.get('file_path', '')
                if os.path.exists(file_path):
                    # 使用系统默认程序打开文件
                    if os.name == 'nt':  # Windows
                        os.startfile(file_path)
                    elif os.name == 'posix':  # macOS and Linux
                        subprocess.call(['open', file_path])
                else:
                    QMessageBox.warning(self, "文件不存在", "原始文件不存在，可能已被移动或删除")

        except Exception as e:
            QMessageBox.critical(self, "打开失败", f"无法打开文件: {e}")

    def copy_item(self):
        """复制项目"""
        try:
            if self.current_item:
                from PySide6.QtGui import QClipboard
                from PySide6.QtCore import QMimeData, QUrl

                file_path = self.current_item.get('file_path', '')
                if file_path:
                    # 创建MIME数据
                    mime_data = QMimeData()
                    urls = [QUrl.fromLocalFile(file_path)]
                    mime_data.setUrls(urls)

                    # 复制到剪贴板
                    clipboard = QClipboard()
                    clipboard.setMimeData(mime_data)

                    print(f"已复制文件到剪贴板: {file_path}")

        except Exception as e:
            QMessageBox.warning(self, "复制失败", f"复制文件失败: {e}")

    def share_item(self):
        """分享项目"""
        try:
            if self.current_item:
                file_path = self.current_item.get('file_path', '')
                file_name = self.current_item.get('name', '')

                # 显示分享信息对话框
                from PySide6.QtWidgets import QInputDialog

                share_info = f"文件名: {file_name}\n文件路径: {file_path}"
                QMessageBox.information(self, "分享信息", share_info)

        except Exception as e:
            QMessageBox.warning(self, "分享失败", f"分享失败: {e}")

    def delete_item(self):
        """删除项目"""
        try:
            if self.current_item:
                reply = QMessageBox.question(
                    self, "确认删除",
                    f"确定要从索引中删除 '{self.current_item.get('name', '')}' 吗？\n注意：这只会从索引中删除，不会删除原始文件。",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # TODO: 实现删除逻辑
                    # self.db_manager.delete_material(self.current_item_id)
                    QMessageBox.information(self, "删除完成", "项目已从索引中删除")
                    self.show_empty_state()

        except Exception as e:
            QMessageBox.critical(self, "删除失败", f"删除项目失败: {e}")

    def add_to_collection(self):
        """添加到收藏夹"""
        try:
            if self.current_item:
                from PySide6.QtWidgets import QInputDialog

                # 获取收藏夹名称
                collection_name, ok = QInputDialog.getText(
                    self, "添加到收藏夹", "请输入收藏夹名称:"
                )

                if ok and collection_name.strip():
                    # TODO: 实现添加到收藏夹逻辑
                    QMessageBox.information(
                        self, "添加成功",
                        f"已将 '{self.current_item.get('name', '')}' 添加到收藏夹 '{collection_name.strip()}'"
                    )

        except Exception as e:
            QMessageBox.warning(self, "添加失败", f"添加到收藏夹失败: {e}")
