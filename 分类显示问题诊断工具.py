#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类显示问题诊断工具
诊断和修复文件分类不显示的问题
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QTextEdit, QPushButton, QHBoxLayout, QLabel,
                               QGroupBox, QTreeWidget, QTreeWidgetItem)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class CategoryDisplayDiagnosticWindow(QMainWindow):
    """分类显示问题诊断窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 分类显示问题诊断工具")
        self.setGeometry(100, 100, 1000, 700)
        
        self.setup_ui()
        self.start_diagnosis()
    
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔧 分类显示问题诊断工具")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 诊断按钮组
        button_layout = QHBoxLayout()
        
        diagnose_btn = QPushButton("🔍 开始诊断")
        diagnose_btn.clicked.connect(self.start_diagnosis)
        button_layout.addWidget(diagnose_btn)
        
        test_category_manager_btn = QPushButton("🗂️ 测试分类管理器")
        test_category_manager_btn.clicked.connect(self.test_category_manager)
        button_layout.addWidget(test_category_manager_btn)
        
        test_sidebar_btn = QPushButton("📋 测试侧边栏")
        test_sidebar_btn.clicked.connect(self.test_sidebar)
        button_layout.addWidget(test_sidebar_btn)
        
        fix_issues_btn = QPushButton("🔧 修复问题")
        fix_issues_btn.clicked.connect(self.fix_issues)
        button_layout.addWidget(fix_issues_btn)
        
        layout.addLayout(button_layout)
        
        # 分类预览区域
        preview_group = QGroupBox("📋 分类预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderLabels(["分类名称", "类型", "状态"])
        preview_layout.addWidget(self.category_tree)
        
        layout.addWidget(preview_group)
        
        # 诊断日志
        log_group = QGroupBox("📝 诊断日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        clear_btn = QPushButton("🗑️ 清空日志")
        clear_btn.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_btn)
        
        layout.addWidget(log_group)
    
    def log(self, message, level="INFO"):
        """记录日志"""
        import time
        timestamp = time.strftime("%H:%M:%S")
        
        level_icons = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "WARNING": "⚠️",
            "ERROR": "❌",
            "DEBUG": "🔍"
        }
        
        icon = level_icons.get(level, "📝")
        self.log_text.append(f"[{timestamp}] {icon} {message}")
        QApplication.processEvents()
    
    def start_diagnosis(self):
        """开始诊断"""
        self.log("🔍 开始分类显示问题诊断...", "INFO")
        self.log_text.clear()
        
        # 1. 检查分类管理器
        self.check_category_manager()
        
        # 2. 检查分类数据
        self.check_category_data()
        
        # 3. 检查侧边栏组件
        self.check_sidebar_component()
        
        # 4. 检查界面渲染
        self.check_ui_rendering()
        
        # 5. 生成诊断报告
        self.generate_diagnosis_report()
    
    def check_category_manager(self):
        """检查分类管理器"""
        self.log("🗂️ 检查分类管理器...", "INFO")
        
        try:
            from core.category_manager import get_category_manager
            category_manager = get_category_manager()
            
            self.log("✅ 分类管理器导入成功", "SUCCESS")
            
            # 检查分类数据
            all_categories = category_manager.get_all_categories()
            system_categories = category_manager.get_system_categories()
            custom_categories = category_manager.get_custom_categories()
            
            self.log(f"📊 总分类数: {len(all_categories)}", "INFO")
            self.log(f"📊 系统分类数: {len(system_categories)}", "INFO")
            self.log(f"📊 自定义分类数: {len(custom_categories)}", "INFO")
            
            # 显示分类详情
            for category in all_categories:
                self.log(f"  - {category['icon']} {category['name']} ({category['type']})", "DEBUG")
            
            if len(all_categories) == 0:
                self.log("⚠️ 没有找到任何分类数据", "WARNING")
                return False
            
            return True
            
        except Exception as e:
            self.log(f"❌ 分类管理器检查失败: {e}", "ERROR")
            self.log(f"详细错误: {traceback.format_exc()}", "DEBUG")
            return False
    
    def check_category_data(self):
        """检查分类数据"""
        self.log("📊 检查分类数据完整性...", "INFO")
        
        try:
            from core.category_manager import get_category_manager
            category_manager = get_category_manager()
            
            # 检查配置文件
            config_file = category_manager.categories_file
            self.log(f"📁 配置文件路径: {config_file}", "INFO")
            
            if config_file.exists():
                self.log("✅ 配置文件存在", "SUCCESS")
                
                # 检查文件内容
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                custom_count = len(data.get('custom_categories', []))
                self.log(f"📊 配置文件中的自定义分类数: {custom_count}", "INFO")
                
            else:
                self.log("⚠️ 配置文件不存在，将使用默认分类", "WARNING")
            
            # 检查系统分类
            system_categories = category_manager.get_system_categories()
            if len(system_categories) > 0:
                self.log("✅ 系统分类正常", "SUCCESS")
            else:
                self.log("❌ 系统分类缺失", "ERROR")
                return False
            
            return True
            
        except Exception as e:
            self.log(f"❌ 分类数据检查失败: {e}", "ERROR")
            self.log(f"详细错误: {traceback.format_exc()}", "DEBUG")
            return False
    
    def check_sidebar_component(self):
        """检查侧边栏组件"""
        self.log("📋 检查侧边栏组件...", "INFO")
        
        try:
            from ui.components.sidebar import SidebarWidget
            self.log("✅ 侧边栏组件导入成功", "SUCCESS")
            
            # 检查必要的依赖
            try:
                from database.db_manager import DatabaseManager
                from theme.theme_manager import ThemeManager
                
                db_manager = DatabaseManager()
                theme_manager = ThemeManager()
                
                self.log("✅ 侧边栏依赖组件正常", "SUCCESS")
                
                # 尝试创建侧边栏实例
                sidebar = SidebarWidget(db_manager, theme_manager)
                self.log("✅ 侧边栏实例创建成功", "SUCCESS")
                
                # 检查分类加载方法
                if hasattr(sidebar, 'load_categories'):
                    self.log("✅ 分类加载方法存在", "SUCCESS")
                else:
                    self.log("❌ 分类加载方法缺失", "ERROR")
                    return False
                
                return True
                
            except Exception as e:
                self.log(f"❌ 侧边栏依赖检查失败: {e}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 侧边栏组件检查失败: {e}", "ERROR")
            self.log(f"详细错误: {traceback.format_exc()}", "DEBUG")
            return False
    
    def check_ui_rendering(self):
        """检查界面渲染"""
        self.log("🖼️ 检查界面渲染...", "INFO")
        
        try:
            # 更新分类预览
            self.update_category_preview()
            self.log("✅ 分类预览更新成功", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"❌ 界面渲染检查失败: {e}", "ERROR")
            self.log(f"详细错误: {traceback.format_exc()}", "DEBUG")
            return False
    
    def update_category_preview(self):
        """更新分类预览"""
        self.category_tree.clear()
        
        try:
            from core.category_manager import get_category_manager
            category_manager = get_category_manager()
            
            all_categories = category_manager.get_all_categories()
            
            for category in all_categories:
                item = QTreeWidgetItem()
                item.setText(0, f"{category['icon']} {category['name']}")
                item.setText(1, "系统" if category['type'] == 'system' else "自定义")
                item.setText(2, "正常")
                
                # 设置颜色
                color = QColor(category['color'])
                item.setForeground(0, color)
                
                self.category_tree.addTopLevelItem(item)
            
            self.category_tree.expandAll()
            
        except Exception as e:
            error_item = QTreeWidgetItem()
            error_item.setText(0, "❌ 加载失败")
            error_item.setText(1, "错误")
            error_item.setText(2, str(e))
            self.category_tree.addTopLevelItem(error_item)
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        self.log("📋 生成诊断报告...", "INFO")
        
        # 统计问题
        log_content = self.log_text.toPlainText()
        error_count = log_content.count("❌")
        warning_count = log_content.count("⚠️")
        success_count = log_content.count("✅")
        
        self.log("=" * 50, "INFO")
        self.log("📋 诊断报告总结", "INFO")
        self.log("=" * 50, "INFO")
        self.log(f"✅ 成功项目: {success_count}", "SUCCESS")
        self.log(f"⚠️ 警告项目: {warning_count}", "WARNING")
        self.log(f"❌ 错误项目: {error_count}", "ERROR")
        
        if error_count == 0 and warning_count == 0:
            self.log("🎉 所有检查通过，分类系统正常！", "SUCCESS")
        elif error_count == 0:
            self.log("⚠️ 有警告但无严重错误，系统基本正常", "WARNING")
        else:
            self.log("❌ 发现严重错误，需要修复", "ERROR")
            self.log("💡 建议点击'修复问题'按钮进行自动修复", "INFO")
    
    def test_category_manager(self):
        """测试分类管理器"""
        self.log("🧪 测试分类管理器功能...", "INFO")
        
        try:
            from core.category_manager import get_category_manager
            category_manager = get_category_manager()
            
            # 测试添加分类
            test_id = category_manager.add_category("测试分类", "🧪", "#FF6B6B", "诊断测试分类")
            if test_id:
                self.log("✅ 分类添加测试成功", "SUCCESS")
                
                # 测试更新分类
                success = category_manager.update_category(test_id, name="测试分类(已更新)")
                if success:
                    self.log("✅ 分类更新测试成功", "SUCCESS")
                
                # 测试删除分类
                success = category_manager.remove_category(test_id)
                if success:
                    self.log("✅ 分类删除测试成功", "SUCCESS")
                else:
                    self.log("❌ 分类删除测试失败", "ERROR")
            else:
                self.log("❌ 分类添加测试失败", "ERROR")
            
            # 更新预览
            self.update_category_preview()
            
        except Exception as e:
            self.log(f"❌ 分类管理器测试失败: {e}", "ERROR")
    
    def test_sidebar(self):
        """测试侧边栏"""
        self.log("🧪 测试侧边栏组件...", "INFO")
        
        try:
            from ui.components.sidebar import SidebarWidget
            from database.db_manager import DatabaseManager
            from theme.theme_manager import ThemeManager
            
            # 创建依赖
            db_manager = DatabaseManager()
            theme_manager = ThemeManager()
            
            # 创建侧边栏
            sidebar = SidebarWidget(db_manager, theme_manager)
            
            # 测试分类加载
            sidebar.load_categories()
            self.log("✅ 侧边栏分类加载测试成功", "SUCCESS")
            
            # 显示侧边栏
            sidebar.show()
            self.log("✅ 侧边栏显示测试成功", "SUCCESS")
            
        except Exception as e:
            self.log(f"❌ 侧边栏测试失败: {e}", "ERROR")
            self.log(f"详细错误: {traceback.format_exc()}", "DEBUG")
    
    def fix_issues(self):
        """修复问题"""
        self.log("🔧 开始自动修复问题...", "INFO")
        
        try:
            # 1. 重新初始化分类管理器
            self.log("🔄 重新初始化分类管理器...", "INFO")
            from core.category_manager import get_category_manager, cleanup_category_manager
            
            # 清理现有实例
            cleanup_category_manager()
            
            # 重新获取实例
            category_manager = get_category_manager()
            self.log("✅ 分类管理器重新初始化成功", "SUCCESS")
            
            # 2. 检查并修复系统分类
            system_categories = category_manager.get_system_categories()
            if len(system_categories) < 5:  # 应该有至少5个系统分类
                self.log("🔧 修复系统分类...", "INFO")
                category_manager._init_system_categories()
                self.log("✅ 系统分类修复完成", "SUCCESS")
            
            # 3. 保存配置
            category_manager.save_categories()
            self.log("✅ 配置保存完成", "SUCCESS")
            
            # 4. 更新预览
            self.update_category_preview()
            self.log("✅ 界面更新完成", "SUCCESS")
            
            self.log("🎉 问题修复完成！", "SUCCESS")
            
        except Exception as e:
            self.log(f"❌ 问题修复失败: {e}", "ERROR")
            self.log(f"详细错误: {traceback.format_exc()}", "DEBUG")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = CategoryDisplayDiagnosticWindow()
    window.show()
    
    print("分类显示问题诊断工具启动成功！")
    print("功能特性：")
    print("1. 🔍 全面的分类系统诊断")
    print("2. 📊 分类数据完整性检查")
    print("3. 🖼️ 界面渲染问题检测")
    print("4. 🔧 自动问题修复")
    print("5. 🧪 组件功能测试")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
