# 智能素材管理器 - 主程序入口
# 功能：应用程序启动和初始化，设置全局配置和主题

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QDir
from PySide6.QtGui import QIcon, QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from theme.theme_manager import ThemeManager
from ui.main_window import MainWindow
from database.db_manager import DatabaseManager
from utils.config_manager import ConfigManager

class SmartAssetManager:
    """智能素材管理器主应用类"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.theme_manager = None
        self.db_manager = None
        self.config_manager = None
        
    def initialize_app(self):
        """初始化应用程序"""
        # 创建QApplication实例
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("智能素材管理器")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("SmartAsset")
        
        # 设置应用程序图标
        icon_path = project_root / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置默认字体
        font = QFont("Microsoft YaHei", 9)
        self.app.setFont(font)

        
    def initialize_managers(self):
        """初始化各种管理器"""
        # 初始化配置管理器
        self.config_manager = ConfigManager()
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager()
        self.db_manager.initialize_database()
        
        # 初始化主题管理器
        self.theme_manager = ThemeManager()
        self.theme_manager.load_theme(self.config_manager.get_theme())
        
    def create_main_window(self):
        """创建主窗口"""
        self.main_window = MainWindow(
            theme_manager=self.theme_manager,
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
        
        # 应用主题到主窗口
        self.theme_manager.apply_theme_to_widget(self.main_window)
        
        # 显示主窗口
        self.main_window.show()
        
    def setup_signal_connections(self):
        """设置信号连接"""
        # 主题变更信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
        
        # 应用程序退出信号
        self.app.aboutToQuit.connect(self.on_app_quit)
        
    def on_theme_changed(self, theme_name):
        """主题变更处理"""
        if self.main_window:
            self.theme_manager.apply_theme_to_widget(self.main_window)
        self.config_manager.set_theme(theme_name)
        
    def on_app_quit(self):
        """应用程序退出处理"""
        if self.db_manager:
            self.db_manager.close()
        if self.config_manager:
            self.config_manager.save_config()
            
    def run(self):
        """运行应用程序"""
        try:
            self.initialize_app()
            self.initialize_managers()
            self.create_main_window()
            self.setup_signal_connections()
            
            # 启动事件循环
            return self.app.exec()
            
        except Exception as e:
            print(f"应用程序启动失败: {e}")
            return 1

def main():
    """主函数"""
    # 创建应用程序实例
    app = SmartAssetManager()
    
    # 运行应用程序
    exit_code = app.run()
    
    # 退出
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
