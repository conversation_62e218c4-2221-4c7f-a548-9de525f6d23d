#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能列表视图
实现虚拟滚动、懒加载、批量更新等性能优化技术
"""

from PySide6.QtWidgets import (QListView, QWidget, QVBoxLayout, QHBoxLayout, 
                               QLabel, QPushButton, QProgressBar, QScrollBar,
                               QAbstractItemView, QStyledItemDelegate)
from PySide6.QtCore import Qt, Signal, QTimer, QRect, QSize, QModelIndex
from PySide6.QtGui import QPainter, QPixmap, QFont, QPen, QBrush, QColor
from typing import List, Dict, Any
import time

from .virtual_list_model import VirtualListModel

class HighPerformanceListView(QListView):
    """高性能列表视图"""
    
    # 信号定义
    item_activated = Signal(int, dict)  # 项目激活
    selection_changed_signal = Signal(list)  # 选择变更
    visible_range_changed = Signal(int, int)  # 可见范围变更
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置视图属性
        self.setViewMode(QListView.IconMode)
        self.setResizeMode(QListView.Adjust)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.setUniformItemSizes(True)  # 关键优化：统一项目大小
        
        # 性能优化设置
        self.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setLayoutMode(QListView.Batched)  # 批量布局模式
        self.setBatchSize(50)  # 批量大小
        
        # 创建虚拟模型
        self.virtual_model = VirtualListModel(self)
        self.setModel(self.virtual_model)
        
        # 创建自定义委托
        self.item_delegate = HighPerformanceItemDelegate(self)
        self.setItemDelegate(self.item_delegate)
        
        # 性能监控
        self.last_scroll_time = 0
        self.scroll_timer = QTimer()
        self.scroll_timer.setSingleShot(True)
        self.scroll_timer.timeout.connect(self._on_scroll_finished)
        
        # 连接信号
        self.setup_connections()
        
        # 预加载定时器
        self.preload_timer = QTimer()
        self.preload_timer.setSingleShot(True)
        self.preload_timer.timeout.connect(self._preload_visible_items)
    
    def setup_connections(self):
        """设置信号连接"""
        # 模型信号
        self.virtual_model.data_loading.connect(self._on_data_loading)
        self.virtual_model.data_loaded.connect(self._on_data_loaded)
        self.virtual_model.thumbnail_ready.connect(self._on_thumbnail_ready)
        
        # 视图信号
        self.activated.connect(self._on_item_activated)
        self.selectionModel().selectionChanged.connect(self._on_selection_changed)
        
        # 滚动信号
        self.verticalScrollBar().valueChanged.connect(self._on_scroll)
    
    def setDataSource(self, data: List[Dict[str, Any]]):
        """设置数据源"""
        print(f"设置数据源: {len(data)} 项")
        
        # 暂时禁用更新
        self.setUpdatesEnabled(False)
        
        try:
            # 设置模型数据
            self.virtual_model.setDataSource(data)
            
            # 重置滚动位置
            self.scrollToTop()
            
            # 预加载可见项
            self._preload_visible_items()
            
        finally:
            # 重新启用更新
            self.setUpdatesEnabled(True)
    
    def _on_scroll(self, value):
        """滚动事件处理"""
        current_time = time.time()
        self.last_scroll_time = current_time
        
        # 延迟处理滚动，避免频繁更新
        self.scroll_timer.stop()
        self.scroll_timer.start(100)  # 100ms延迟
        
        # 立即更新可见范围
        self._update_visible_range()
    
    def _on_scroll_finished(self):
        """滚动完成处理"""
        # 预加载新的可见项
        self._preload_visible_items()
    
    def _update_visible_range(self):
        """更新可见范围"""
        # 获取可见区域
        visible_rect = self.viewport().rect()
        
        # 计算可见项范围
        first_visible = self.indexAt(visible_rect.topLeft())
        last_visible = self.indexAt(visible_rect.bottomRight())
        
        if first_visible.isValid() and last_visible.isValid():
            start_row = first_visible.row()
            end_row = last_visible.row() + 1
            
            self.visible_range_changed.emit(start_row, end_row)
    
    def _preload_visible_items(self):
        """预加载可见项"""
        # 获取可见区域
        visible_rect = self.viewport().rect()
        
        # 扩展预加载区域
        extended_rect = visible_rect.adjusted(0, -visible_rect.height(), 0, visible_rect.height())
        
        # 计算需要预加载的项
        first_index = self.indexAt(extended_rect.topLeft())
        last_index = self.indexAt(extended_rect.bottomRight())
        
        if first_index.isValid() and last_index.isValid():
            start_row = max(0, first_index.row() - 10)
            end_row = min(self.virtual_model.rowCount(), last_index.row() + 10)
            
            # 请求预加载
            self.virtual_model.prefetchData(start_row, end_row - start_row)
    
    def _on_data_loading(self, start: int, end: int):
        """数据加载开始"""
        print(f"开始加载数据: {start}-{end}")
    
    def _on_data_loaded(self, start: int, end: int):
        """数据加载完成"""
        print(f"数据加载完成: {start}-{end}")
    
    def _on_thumbnail_ready(self, row: int, pixmap: QPixmap):
        """缩略图就绪"""
        # 更新对应项的显示
        index = self.virtual_model.index(row)
        self.update(index)
    
    def _on_item_activated(self, index: QModelIndex):
        """项目激活"""
        if index.isValid():
            item_data = self.virtual_model.data(index, Qt.UserRole)
            self.item_activated.emit(index.row(), item_data)
    
    def _on_selection_changed(self, selected, deselected):
        """选择变更"""
        selected_rows = []
        for index in self.selectionModel().selectedIndexes():
            selected_rows.append(index.row())
        
        self.selection_changed_signal.emit(selected_rows)
    
    def resizeEvent(self, event):
        """窗口大小变更"""
        super().resizeEvent(event)
        
        # 延迟预加载，避免频繁触发
        self.preload_timer.stop()
        self.preload_timer.start(200)
    
    def wheelEvent(self, event):
        """鼠标滚轮事件优化"""
        # 使用像素滚动，更流畅
        delta = event.angleDelta().y()
        scroll_bar = self.verticalScrollBar()
        
        # 计算滚动步长
        step = max(1, abs(delta) // 8)  # 减小滚动步长
        if delta < 0:
            step = -step
        
        # 应用滚动
        new_value = scroll_bar.value() - step * 3  # 调整滚动速度
        scroll_bar.setValue(new_value)
        
        event.accept()
    
    def clear_cache(self):
        """清空缓存"""
        self.virtual_model.invalidateCache()
    
    def refresh_view(self):
        """刷新视图"""
        self.virtual_model.invalidateCache()
        self.viewport().update()

class HighPerformanceItemDelegate(QStyledItemDelegate):
    """高性能项目委托"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 缓存字体和画笔
        self.title_font = QFont()
        self.title_font.setPointSize(10)
        self.title_font.setBold(True)
        
        self.subtitle_font = QFont()
        self.subtitle_font.setPointSize(8)
        
        self.border_pen = QPen(QColor(200, 200, 200))
        self.selected_brush = QBrush(QColor(100, 150, 255, 50))
    
    def paint(self, painter: QPainter, option, index: QModelIndex):
        """绘制项目"""
        if not index.isValid():
            return
        
        # 获取数据
        item_data = index.data(Qt.UserRole) or {}
        title = index.data(Qt.DisplayRole) or "Unknown"
        icon = index.data(Qt.DecorationRole)
        
        # 绘制背景
        rect = option.rect
        if option.state & option.State_Selected:
            painter.fillRect(rect, self.selected_brush)
        
        # 绘制边框
        painter.setPen(self.border_pen)
        painter.drawRect(rect.adjusted(1, 1, -1, -1))
        
        # 绘制图标
        icon_rect = QRect(rect.x() + 10, rect.y() + 10, 128, 128)
        if icon and not icon.isNull():
            icon.paint(painter, icon_rect)
        else:
            # 绘制占位符
            painter.fillRect(icon_rect, QColor(240, 240, 240))
            painter.setPen(QColor(150, 150, 150))
            painter.drawText(icon_rect, Qt.AlignCenter, "📁")
        
        # 绘制标题
        title_rect = QRect(rect.x() + 5, rect.y() + 145, rect.width() - 10, 20)
        painter.setFont(self.title_font)
        painter.setPen(QColor(50, 50, 50))
        
        # 截断长标题
        metrics = painter.fontMetrics()
        elided_title = metrics.elidedText(title, Qt.ElideRight, title_rect.width())
        painter.drawText(title_rect, Qt.AlignCenter, elided_title)
        
        # 绘制加载状态
        if item_data.get('loading', False):
            loading_rect = QRect(rect.x() + 5, rect.y() + 165, rect.width() - 10, 10)
            painter.fillRect(loading_rect, QColor(200, 200, 200))
            painter.setPen(QColor(100, 100, 100))
            painter.drawText(loading_rect, Qt.AlignCenter, "Loading...")
    
    def sizeHint(self, option, index: QModelIndex) -> QSize:
        """返回项目大小提示"""
        return QSize(150, 180)  # 固定大小，提升性能

class PerformanceMonitorWidget(QWidget):
    """性能监控控件"""
    
    def __init__(self, list_view: HighPerformanceListView, parent=None):
        super().__init__(parent)
        
        self.list_view = list_view
        self.setup_ui()
        
        # 性能统计
        self.load_count = 0
        self.thumbnail_count = 0
        self.start_time = time.time()
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_stats)
        self.update_timer.start(1000)  # 每秒更新
        
        # 连接信号
        self.list_view.virtual_model.data_loaded.connect(self._on_data_loaded)
        self.list_view.virtual_model.thumbnail_ready.connect(self._on_thumbnail_ready)
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("📊 性能监控")
        title.setFont(QFont("", 10, QFont.Bold))
        layout.addWidget(title)
        
        # 统计标签
        self.stats_label = QLabel("等待数据...")
        layout.addWidget(self.stats_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 控制按钮
        buttons_layout = QHBoxLayout()
        
        clear_btn = QPushButton("清空缓存")
        clear_btn.clicked.connect(self.list_view.clear_cache)
        buttons_layout.addWidget(clear_btn)
        
        refresh_btn = QPushButton("刷新视图")
        refresh_btn.clicked.connect(self.list_view.refresh_view)
        buttons_layout.addWidget(refresh_btn)
        
        layout.addLayout(buttons_layout)
    
    def _on_data_loaded(self, start: int, end: int):
        """数据加载完成"""
        self.load_count += (end - start)
    
    def _on_thumbnail_ready(self, row: int, pixmap: QPixmap):
        """缩略图就绪"""
        self.thumbnail_count += 1
    
    def update_stats(self):
        """更新统计信息"""
        elapsed = time.time() - self.start_time
        
        stats_text = f"""
运行时间: {elapsed:.1f}s
数据加载: {self.load_count} 项
缩略图: {self.thumbnail_count} 个
加载速度: {self.load_count/elapsed:.1f} 项/秒
缩略图速度: {self.thumbnail_count/elapsed:.1f} 个/秒
        """.strip()
        
        self.stats_label.setText(stats_text)
